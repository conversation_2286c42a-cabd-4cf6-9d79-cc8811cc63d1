# Purpose:
Rollback users that have been updated to new steps order to old one

# Motivation:
Steps order has been changed (Connect Integration, Business Information, Payment), rollback migrated users with unfinished onboarding from Connect Integration to their old steps

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**

### How this script was tested
[x] Wet run against dev & test

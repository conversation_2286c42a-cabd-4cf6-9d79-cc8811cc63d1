# Purpose:
The purpose of this script is to incorporate a new property, shipmentPickupDate, into the first entry of the dispute.tracking array,
utilizing the data available in the aftership raw dataset.
This script is intended to be deployed following system updates that ensure the ongoing addition of this property for future disputes.

# Motivation:
The primary motivation behind this initiative is to facilitate the use of the shipmentPickupDate in automation templates.

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**


### How this script was tested
[x] Wet run against dev & test
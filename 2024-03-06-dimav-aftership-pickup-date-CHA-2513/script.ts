import dotenv from 'dotenv';
import { AnyBulkWriteOperation, Document, ObjectId } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';

dotenv.config();

const QUERY_GET_DISPUTES_WITH_RAW_SHIPMENT_PICKUP_DATE = {
    'shippingData.0.rawData.shippingData.shipment_pickup_date': {
        $exists: 1,
        $type: 'string',
    },
};

const BATCH_SIZE = 1000;

export class ScriptAddPickUpDateToShippingObject extends ScriptBase {
    public getName(): string {
        return 'Map Shipment pick update from raw data to shippingData';
    }

    public getAuthor(): string {
        return 'Dima Vinogradov';
    }

    public getTicket(): string {
        return 'CHA-2513';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log("Running in dry run mode (not changing anything)");
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {
        let lastId: ObjectId | undefined;
        let updatedCount = 0;
        const client = await loadMongoClient();
        const collection = client.db('chargeflow_api').collection('disputes');

        const handleDryRun = (doc:Document) => {
            console.log(`Dispute ID: ${doc._id}`);
            console.log(`Current shipment_pickup_date: ${doc.shippingData[0].rawData.shippingData.shipment_pickup_date} type: string`);
            console.log(`Would set shipmentPickupDate to: ${new Date(doc.shippingData[0].rawData.shippingData.shipment_pickup_date)} type: date`);
            console.log('-'.repeat(50));
        };

        const createBulkOperation = (doc:Document) => ({
            updateOne: {
                filter: { _id: doc._id },
                update: {
                    $set: {
                        'shippingData.0.shipmentPickupDate': new Date(
                            doc.shippingData[0].rawData.shippingData.shipment_pickup_date,
                        ),
                    },
                },
            },
        });

        while (true) {
            const query = lastId
                ? {
                      ...QUERY_GET_DISPUTES_WITH_RAW_SHIPMENT_PICKUP_DATE,
                      _id: { $gt: lastId },
                  }
                : QUERY_GET_DISPUTES_WITH_RAW_SHIPMENT_PICKUP_DATE;

            const documents = await collection
                .find(query, {
                    projection: {
                        _id: 1,
                        'shippingData': 1,
                    },
                })
                .sort({ _id: 1 })
                .limit(BATCH_SIZE)
                .toArray();

            if (documents.length === 0) {
                console.log("No more documents to process.");
                break;
            }

            let bulkOps:AnyBulkWriteOperation<Document>[] = [];

            if(!isDryRun){
                bulkOps = documents.map(createBulkOperation);
            } else {
                documents.forEach(handleDryRun);
            }

            if (!isDryRun && bulkOps.length > 0) {
                const result = await collection.bulkWrite(bulkOps);
                updatedCount += result.modifiedCount;
                console.log(
                    `${new Date().toISOString()}: Processed ${documents.length} documents. Total updated so far: ${updatedCount}`,
                );
            }

            lastId = documents[documents.length - 1]._id;
        }

        if (!isDryRun) {
            console.log(`Total documents updated: ${updatedCount}`);
        }
    }
}

const script = new ScriptAddPickUpDateToShippingObject();
script.main();

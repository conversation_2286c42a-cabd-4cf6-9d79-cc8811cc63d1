import dotenv from "dotenv";
import { MongoClient } from "mongodb";
import { ScriptBase } from "../shared/script-base";
import {AssignProcessorsToShops} from "./logic";

dotenv.config();

export class Script extends ScriptBase {
    public getName(): string {
        return 'Assign processorId to afterpayDisputesRawData';
    }

    public getAuthor(): string {
        return 'mateuszs';
    }

    public getTicket(): string {
        return 'CHA-5095';
    }

    async dryRun(): Promise<void> {
        const dryRun = true;

        await this.execute(dryRun);
    }

    async wetRunOnPrimary(): Promise<void> {
        const dryRun = false;

        await this.execute(dryRun);
    }

    wetRunOnSecondary(): Promise<void> {
        throw new Error("Method not implemented.");
    }

    private async execute(dryRun: boolean): Promise<void> {
        const { batchSize, page} = this.getParamsFromEnvVars();
        console.log(batchSize)
        const mongoClient = await MongoClient.connect(process.env.MONGO_URI!);
        const logger = {
            log: (message: string) => {
                console.log(message);
            },
        }

        const logic = new AssignProcessorsToShops(
            mongoClient,
            logger,
        );

        await logic.execute(batchSize, page, dryRun);
    }

    private getParamsFromEnvVars(): { batchSize: number, page: number } {
        const batchSize = process.env.BATCH_SIZE
            ? parseInt(process.env.BATCH_SIZE, 10)
            : 1;
        const page = process.env.PAGE
            ? parseInt(process.env.PAGE, 10)
            : 1;
        return {
            batchSize, page
        };
    }
}

const script = new Script();
script.main();

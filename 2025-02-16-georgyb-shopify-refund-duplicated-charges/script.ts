import '@shopify/shopify-api/adapters/node';
import {ScriptBase} from '../shared/script-base';
import dotenv from 'dotenv';
import fs from 'node:fs';
import neatCsv from 'neat-csv';
import {json2csv} from 'json-2-csv';
import {MongoClient, ObjectId} from 'mongodb';
import {ApiVersion, DataType, RestClientParams, shopifyApi} from '@shopify/shopify-api';
import {ParamsConfig} from '@chargeflow-team/chargeflow-utils-sdk';
import {parseGid} from '@shopify/admin-graphql-api-utilities';

dotenv.config();

const input = 'shopify-chargeflow-payment-history-2025-02-21.csv';
const rate = 'chargeflow_success_rate_feb_18.csv';
const refunds = 'refunds_test.csv';

export class RefundDuplicateChargesScript extends ScriptBase {
    private shops: Record<string, any> = {};
    private disputes: Record<string, any> = {};

    public getName() {
        return 'Refund Duplicate Charges Script';
    }

    public getAuthor() {
        return 'Georgy Bunin';
    }

    public getTicket(): string {
        return 'CHA-1111';
    }

    private getDuplicateChargeId(charge: any): string {
        return [
            `${charge['Shop Domain']}`,
            `${charge.Details.split(' ')[3]}`,
        ].join('_')
    }

    private filterSingleRecords(data: any[]): any[] {
        const chargesCount: Record<string, number> = {};

        data.forEach(charge => {
            const key = this.getDuplicateChargeId(charge);
            if (!chargesCount[key]) {
                chargesCount[key] = 0;
            }
            chargesCount[key]++;
        });

        return data.filter(charge => {
            return chargesCount[this.getDuplicateChargeId(charge)] > 1;
        })
    }

    private filterOppositeRecords(data: any[]): any[] {
        const hashTable: Record<string, any> = {};

        for (let i = 0; i < data.length; i++) {
            const charge = data[i];
            const key = this.getDuplicateChargeId(charge);

            const price = parseFloat(charge['Partner Sale']);
            const indicator = {
                haveOpposite: false,
                index: i,
                oppositeIndex: -1,
                value: price,
                absValue: Math.abs(price)
            }

            if (!hashTable[key]) {
                hashTable[key] = [indicator];
            } else {
                const j = hashTable[key]
                    .findIndex((e: any) => {
                        return !e.haveOpposite && e.absValue === indicator.absValue && e.value !== indicator.value
                    });
                if (j === -1) {
                    hashTable[key].push(indicator);
                } else {
                    hashTable[key][j].oppositeIndex = i;
                    hashTable[key][j].haveOpposite = true;
                    indicator.oppositeIndex = hashTable[key][j].index;
                    indicator.haveOpposite = true;

                    hashTable[key].push(indicator);
                }
            }

            data[i].opposite = indicator;
        }

        return data.filter(e => !e.opposite.haveOpposite);
    };

    private splitToDuplicatesAndPossibleDuplicates(data: any[]): any[] {
        const groupedByCaseId: Record<string, any[]> = {};

        const isDuplicate = (charge: any) => {
            const list = groupedByCaseId[this.getDuplicateChargeId(charge)];
            return list.length === 2 && list[0]['Partner Sale'] === list[1]['Partner Sale']
        }

        const isNotDuplicates = (list: any[]) => !isDuplicate(list);

        data.forEach(charge => {
            const key = this.getDuplicateChargeId(charge);
            groupedByCaseId[key] ||= [];
            groupedByCaseId[key].push(charge);
        });

        const duplicates = Object.values(data).filter(isDuplicate);
        const possibleDuplicates = Object.values(data).filter(isNotDuplicates);

        return [duplicates, possibleDuplicates];
    }

    private async loadSuccessRateFromFile(file: string) {
        const successRates: Record<string, number> = {};
        if (fs.existsSync(file)) {
            const list = await neatCsv<any>(fs.readFileSync(file));
            list.forEach((e: any) => {
                if (e['Chargeflow ID']) {
                    successRates[e['Chargeflow ID']] = parseFloat(e['Chargeflow Success Rate']);
                }
            });
            console.log('Cached success rates loaded from file:', list.length);
            return successRates;
        }

        return {};
    }

    private async getShopByChargeflowId(chargeflowId: string, mongoClient: MongoClient) {
        if (!this.shops[chargeflowId]) {
            const collection = mongoClient.db('chargeflow_api').collection('shops');
            const shop = await collection.findOne({'chargeflow_id': new ObjectId(chargeflowId)}) ?? null;
            if (!shop) {
                return null;
            }
            this.shops[chargeflowId] = shop;
        }

        return this.shops[chargeflowId];
    }

    private async getDisputeByCaseId(caseId: string, mongoClient: MongoClient) {
        if (!this.disputes[caseId]) {
            const collection = mongoClient.db('chargeflow_api').collection('disputes');
            const dispute = await collection.findOne({'dispute.id': caseId}) ?? null;
            if (!dispute) {
                return null;
            }
            this.disputes[caseId] = dispute;
        }

        return this.disputes[caseId];
    }

    private async getSuccessRate(chargeflowId: string, cachedRates: Record<string, number>, mongoClient: MongoClient) {
        const DEFAULT_SUCCESS_RATE = 25;

        if (!chargeflowId || chargeflowId === 'NA') {
            return {successRate: DEFAULT_SUCCESS_RATE};
        }

        if (cachedRates[chargeflowId]) {
            return {successRate: cachedRates[chargeflowId]}
        }

        const shop = await this.getShopByChargeflowId(chargeflowId, mongoClient);
        const successRate = shop ? parseFloat(this.shops[chargeflowId].chargeflow_success_rate) : DEFAULT_SUCCESS_RATE;

        return {successRate};
    }

    private async getShopifyAccessDetails(chargeflowId: any, mongoClient: MongoClient) {
        if (!chargeflowId || chargeflowId === 'NA') {
            return {};
        }
        const shop = await this.getShopByChargeflowId(chargeflowId, mongoClient);
        return shop ? {
            access_token: shop.connect.access_token || '',
            shop_name: shop.connect.shop_name || shop.name || '',
            scope: shop.connect.scope || ''
        } : {};
    }

    private async getDisputeData(caseId: string, mongoClient: MongoClient) {
        const dispute = await this.getDisputeByCaseId(caseId, mongoClient);

        return {
            chargeflowId: dispute?.chargeflowId?.toString() ?? 'NA',
            amountInUSD: dispute?.chargeflow?.submitted?.amountInUSD ?? 'NA',
            amountWon: dispute?.chargeflow?.resolved?.amountWon ?? 0,
            disputeSuccessFee: dispute?.chargeflow?.resolved?.successFee ?? 'NA',
            disputeStatus: dispute?.lastDisputeStatus?.status ?? 'NA',
        }
    }

    private getShopifyRestApi(shop: string, accessToken: string, scopes: string[]) {
        const session = {shop: `${shop}.myshopify.com`, accessToken};
        const shopify = shopifyApi({
            apiKey: process.env.SHOPIFY_CLIENT_API_ID,
            apiSecretKey: process.env.SHOPIFY_CLIENT_API_KEY as string,
            scopes: scopes || (process.env.SHOPIFY_SCOPE ?? '').split(','),
            hostName: process.env.HOST_NAME as string,
            apiVersion: ApiVersion.January25,
            isEmbeddedApp: false,
            logger: {
                log: () => {
                }
            }
        });
        return new shopify.clients.Rest({session} as RestClientParams);
    }

    private getShopifyGraphQL(shop: string, accessToken: string, scopes: string[]) {
        const session = {shop: `${shop}.myshopify.com`, accessToken};
        const shopify = shopifyApi({
            apiKey: process.env.SHOPIFY_CLIENT_API_ID,
            apiSecretKey: process.env.SHOPIFY_CLIENT_API_KEY as string,
            scopes: scopes || (process.env.SHOPIFY_SCOPE ?? '').split(','),
            hostName: process.env.HOST_NAME as string,
            apiVersion: ApiVersion.Unstable,
            isEmbeddedApp: false,
            logger: {
                log: () => {
                }
            }
        });
        return new shopify.clients.Graphql({session} as RestClientParams);
    }

    private async asyncPartnerGraphqlRequest(payload: any) {
        const orgId = process.env.SHOPIFY_PARTNER_ORG_ID!;
        const partnerAccessToken = process.env.SHOPIFY_PARTNER_ACCESS_TOKEN!;

        return fetch(`https://partners.shopify.com/${orgId}/api/2025-01/graphql.json`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Shopify-Access-Token': partnerAccessToken
            },
            body: JSON.stringify(payload)
        }).then(e => e.json());
    }

    private async refundWithRestClient(chargeflowId: string, caseId: string, refund: any, mongoClient: any) {
        const {shop_name, access_token, scope} = await this.getShopifyAccessDetails(chargeflowId, mongoClient);

        if (!access_token) {
            console.log('Shop not found', chargeflowId, caseId);
            return;
        }


        const shopifyClient = this.getShopifyRestApi(shop_name, access_token, scope.split(','));
        return shopifyClient.post({
            path: 'application_credits',
            data: {
                'application_credit': {
                    description: `Chargeflow resolved dispute ${caseId.toString().trim()} success-fee`,
                    amount: refund,
                    test: true,
                },
            },
            type: DataType.JSON,
        });

    }

    private async refundWithGraphQlClient(chargeflowId: string, caseId: string, refund: any, mongoClient: any) {
        const {shop_name, access_token, scope} = await this.getShopifyAccessDetails(chargeflowId, mongoClient);

        if (!access_token) {
            console.log('Shop not found', chargeflowId, caseId);
            return;
        }

        const adminShopifyClient = this.getShopifyGraphQL(shop_name, access_token, scope.split(','));
        const infoQuery = `{ shop { id } app { id } }`
        const infoResponse = await adminShopifyClient.request(infoQuery);
        const [shopId, appId] = [parseGid(infoResponse.data.shop.id), parseGid(infoResponse.data.app.id)];

        // https://admin.shopify.com/store/avia-chargeflow-io/shop.json
        const devShopId = '64630817010';
        // chargeflow (dev)
        const devAppId = '5620219';

        const description = `Chargeflow resolved dispute ${caseId.toString().trim()} success-fee`;
        const query = `
mutation appCreditCreate($appId: ID!, $shopId: ID!, $amount: MoneyInput!, $description: String!, $test: Boolean) {
  appCreditCreate(appId: $appId, shopId: $shopId, amount: $amount, description: $description, test: $test) {
    appCredit { id test name amount { amount currencyCode } }
    userErrors { field message }
  }
}        
`;

        const getVariables = (prod: boolean = false) => {
            if (!prod) {
                return {
                    amount: {amount: '0.1', currencyCode: 'USD'},
                    appId: `gid://partners/App/${devAppId}`,
                    shopId: `gid://partners/Shop/${devShopId}`,
                    description,
                    test: true
                }
            }

            return {
                amount: {amount: refund, currencyCode: 'USD'},
                appId: `gid://partners/App/${appId}`,
                shopId: `gid://partners/Shop/${shopId}`,
                description,
                test: false
            }
        }

        const variables = getVariables(true);
        return this.asyncPartnerGraphqlRequest({query, variables});
    }

    // filter possible duplicates and analyze possible duplicates with success rate and mongo
    public async dryRun(): Promise<void> {
        await ParamsConfig.loadParameters();

        const options = {useNewUrlParser: true, useUnifiedTopology: true, auth: JSON.parse(process.env.MONGO_AUTH!)};
        const mongoClient = await MongoClient.connect(process.env.MONGO_URI!, options);

        const data = await neatCsv<any>(fs.readFileSync(input));
        const disputeMsg = 'Chargeflow resolved dispute';
        const disputeCharges = data.filter((d: any) => d.Details.startsWith(disputeMsg));

        const withoutSingleRecords = this.filterSingleRecords(disputeCharges);
        const withoutOppositeValues = this.filterOppositeRecords(withoutSingleRecords);
        const withoutSingleRecordsAgain = this.filterSingleRecords(withoutOppositeValues);

        console.log('-'.repeat(80));
        console.log(
            'Raw data', data.length,
            'withoutSingleRecords:', withoutSingleRecords.length,
            'withoutOppositeValues:', withoutOppositeValues.length,
            'withoutSingleRecordsAgain:', withoutSingleRecordsAgain.length
        );

        const sortedDataSet = withoutSingleRecordsAgain.sort((a, b) => {
            return a['Details'].localeCompare(b['Details']);
        }).map(e => {
            delete e.opposite;
            return e;
        });

        const [duplicates, possibleDuplicates] = this.splitToDuplicatesAndPossibleDuplicates(sortedDataSet);

        console.log(
            'Total records', sortedDataSet.length,
            'duplicates:', duplicates.length,
            'possibleDuplicates:', possibleDuplicates.length,
        );

        console.log('-'.repeat(80));
        console.log('Possible duplicates count:', possibleDuplicates.length);
        console.log('Records saved to report1_PossibleDuplicates.csv');
        fs.writeFileSync('report1_PossibleDuplicates.csv', json2csv(possibleDuplicates));

        console.log('Duplicates count:', duplicates.length);
        console.log('Records saved to report1_Duplicates.csv');
        fs.writeFileSync('report1_Duplicates.csv', json2csv(duplicates));
        console.log('-'.repeat(80));

        // --------------------------------------------------------------------
        const cachedRates: Record<string, number> = await this.loadSuccessRateFromFile(rate);

        const transactionReport = [];
        const disputeHash: Record<string, any[]> = {};

        const transactions = possibleDuplicates;

        for (let i = 0; i < transactions.length; i++) {
            const transaction = transactions[i];
            const caseId = transaction.Details.split(' ')[3];

            if (!caseId) {
                continue;
            }

            const disputeData = await this.getDisputeData(caseId, mongoClient);
            const chargeflowId = disputeData.chargeflowId ?? 'NA';
            const {successRate} = await this.getSuccessRate(disputeData.chargeflowId, cachedRates, mongoClient);

            // remove white noise
            const {
                'Billing Period Start': a,
                'Billing Period End': b,
                'Shop Country': c,
                'Payment Duration': d,
                'Shop Email': e,
                'Charge ID': f,
                ...rest
            } = transaction;

            transactionReport.push({caseId, ...rest, ...disputeData, shopSuccessRate: successRate});

            if (chargeflowId && chargeflowId !== 'NA') {
                const key = chargeflowId + '_' + caseId;
                disputeHash[key] ||= [];
                disputeHash[key].push({caseId, ...disputeData, successRate, transaction});
            }

            const index = (i + 1).toString().padStart(4) + ' of ' + transactions.length;

            console.log(index,
                chargeflowId.toString().padStart(24),
                caseId.toString().padStart(24),
                transaction['Partner Sale'].toString().padStart(8)
            );
        }

        const round3 = (num: number) => Math.round(num * 1000) / 1000;
        const floor2 = (num: number) => Math.floor(Math.abs(num) * 100) / 100;

        const disputeReport = Object.values(disputeHash).map((records: any[]) => {
            const {chargeflowId, caseId, amountWon, disputeSuccessFee, successRate} = records[0];

            const transactionsDetails = records.map((e: any) => {
                const partner_sale = e.transaction['Partner Sale'];
                const processing_fee = e.transaction['Processing Fee'];
                const shopify_fee = e.transaction['Shopify Fee'];
                const partner_share = e.transaction['Partner Share'];
                const regulatory_operating_fee = e.transaction['Regulatory Operating Fee'];

                return {
                    partner_sale,
                    processing_fee,
                    shopify_fee,
                    partner_share,
                    regulatory_operating_fee,
                }
            });

            const actualAmount = round3(records.reduce((acc, curr) => {
                return acc + parseFloat(curr.transaction['Partner Sale']);
            }, 0));

            const shouldBeAmount = round3(amountWon * successRate / 100);

            return {
                chargeflowId,
                caseId,
                amountWon,
                disputeSuccessFee,
                successRate,
                actualAmount,
                shouldBeAmount,
                deltaAmount: round3(actualAmount - shouldBeAmount),
                transactionsSummary: transactionsDetails.map((e: any) => e.partner_sale).join(', '),
                transactionsDetails
            }
        });

        const delta0: Record<string, boolean> = {};
        disputeReport.forEach((e: any) => delta0[e.caseId] = Math.abs(floor2(e.deltaAmount)) === 0);

        const disputeReportWithout0 = disputeReport.filter((e: any) => !delta0[e.caseId]);
        const transactionReportWithout0 = transactionReport.filter((e: any) => !delta0[e.caseId]);

        console.log('-'.repeat(80));
        console.log('Possible duplicates transactions before delta0:', transactions.length);
        console.log('Possible duplicates transactions after  delta0:', transactionReportWithout0.length);

        console.log('Possible duplicates disputes before delta0:', disputeReport.length);
        console.log('Possible duplicates disputes after  delta0:', disputeReportWithout0.length);
        console.log('-'.repeat(80));

        console.log('Records saved to report2_ToReviewDisputes.csv');
        console.log('Records saved to report2_ToReviewTransactions.csv');
        fs.writeFileSync('report2_ToReviewDisputes.csv', json2csv(disputeReportWithout0));
        fs.writeFileSync('report2_ToReviewTransactions.csv', json2csv(transactionReportWithout0));
    }

    // perform refund via Shopify API
    public async wetRunOnPrimary(): Promise<void> {
        await ParamsConfig.loadParameters();

        const options = {useNewUrlParser: true, useUnifiedTopology: true, auth: JSON.parse(process.env.MONGO_AUTH!)};
        const mongoClient = await MongoClient.connect(process.env.MONGO_URI!, options);
        console.log('-'.repeat(80));

        const data = await neatCsv<any>(fs.readFileSync(refunds));

        console.log('Refunds count:', data.length);

        for (let i = 0; i < data.length; i++) {
            const {CaseID: caseId} = data[i];

            const refund = parseFloat(data[i].Refund.toString().replace('US$', ''));
            const disputeData = await this.getDisputeData(caseId, mongoClient);
            const chargeflowId = disputeData.chargeflowId ?? 'NA';

            if (!chargeflowId || chargeflowId === 'NA') {
                console.log('Chargeflow ID not found', caseId);
                continue;
            }

            const response = await this.refundWithGraphQlClient(chargeflowId, caseId, refund, mongoClient);
            console.log(`[Refund]`,
                ' chargeflowId: ', chargeflowId.toString().padStart(24),
                ' caseId: ', caseId.toString().padEnd(24),
                ' refund: ', refund,
                ' response: ', JSON.stringify(response.data?.appCreditCreate?.appCredit)
            );
        }
    }

    public async wetRunOnSecondary(): Promise<void> {
        throw new Error('Not implemented');
    }

}

const script = new RefundDuplicateChargesScript();
script.main();

# Purpose:

Shopify Refund duplicated charges

# Motivation:

Remove duplicates charges from shopify via call to refund API

# Instruction for running this script

1. Login to Shopify Partners:
   https://partners.shopify.com/
2. Select `Chargeflow, Inc.` Account
3. Go to Shopify Chargeflow App Overview:
   https://partners.shopify.com/1838919/apps/4704285/overview
4. Click to `Export` button and select `Export earnings`
5. Report will be sent to your email
6. Download and copy the report to script root folder
7. Change report name in `script.ts`
8. Run file analytics `npx tsx ./script.ts dry`

Now there is 2 actions:
1. Run refund for duplicate transactions by running `npx tsx ./script.ts wet-primary`

    This script take list from `refunds.csv`
2. Analyze possible disputes manually by review `report_PossibleDuplicates.csv`

# Running this script

`npx tsx ./script.ts <"dry" | "wet-secondary" | "wet-primary">`

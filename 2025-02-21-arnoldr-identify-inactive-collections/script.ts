import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Db, MongoServerError } from 'mongodb';
import path from 'path';
import fs from 'fs/promises';
import { Parser } from 'json2csv';

dotenv.config();

const mongo = loadMongoClient();

export class IdentifyInactiveCollections extends ScriptBase {
  db: Db | undefined = undefined;

  public getName(): string {
    return "Identify Inactive Collections";
  }

  public getAuthor(): string {
    return "<PERSON> Ramos";
  }

  public getTicket(): string {
    return "CHA-12819";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(context: ScriptContext, isDryRun: boolean) {
    const client = await mongo
    try {

      const databaseNames = await this.getDatabaseNames(client);
      
      const allCollectionDetails: any = [];

      const errorMessages:any = [];

      for (const dbName of databaseNames) {
        try {
          console.log(`Getting collection stats for database: ${dbName}`);
          const db = await client.db(dbName);
          const collectionDetails = await this.getCollectionDetails(db);
          allCollectionDetails.push(collectionDetails);
        } catch (error) {
          const err = error as MongoServerError
          errorMessages.push({db: dbName, error: err.message});
        }
      }

      if (errorMessages.length > 0) {
        console.log('Error Messages:');
        console.log(errorMessages);
      }

      if (allCollectionDetails.length > 0) {
        console.log('Writing to CSV file...');
        const csvFilePath = path.join(__dirname, `Collections_Stats_Report_${new Date().toISOString()}.csv`);
        await this.writeToCsv(allCollectionDetails.flat(), csvFilePath);
        console.log(`CSV file created at: ${csvFilePath}`);
      }

    } finally {
      await client.close();
    }
  }

  private async getDatabaseNames(client: any): Promise<string[]> {
    const adminDb = client.db().admin();
    const databases = await adminDb.listDatabases();
    return databases.databases.map((database: any) => database.name);
  }

  private async getCollectionDetails(db: Db) {
    const collections = await db.listCollections().toArray();
    const collectionDetails: any = [];

    for (const collectionInfo of collections) {
      const collectionName = collectionInfo.name;

      const stats = await db.command({ collStats: collectionName });
      const lastDocument = await db.collection(collectionName).find({}).sort({ _id: -1 }).limit(1).toArray();

      collectionDetails.push({
        'Database Name': db.databaseName,
        'Collection Name': collectionName,
        'Number of Indexes': stats.nindexes,
        'Document Count': stats.count,
        'Logical Data Size': this.formatSize(Number(stats.size)),
        'Storage Size': this.formatSize(Number(stats.storageSize)),
        'Total Index Size': this.formatSize(Number(stats.totalIndexSize)),
        'Last Document Date': lastDocument.length > 0 ? lastDocument[0]._id.getTimestamp() : 'No Documents',
      });
    }

    return collectionDetails;
  }

  private async writeToCsv(data: any[], filePath: string): Promise<void> {
    const fields = [
      'Database Name',
      'Collection Name',
      'Number of Indexes',
      'Document Count',
      'Logical Data Size',
      'Storage Size',
      'Total Index Size',
      'Last Document Date',
      'Needed?',
      'Reason'
    ];

    const json2csvParser = new Parser({ fields });
    const csv = json2csvParser.parse(data);

    await fs.writeFile(filePath, csv, 'utf8');
  }

  private formatSize(bytes: number): string {
    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(2)} KB`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    }
  }

}

const script = new IdentifyInactiveCollections();
script.main();
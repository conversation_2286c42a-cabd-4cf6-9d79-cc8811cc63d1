import { loadMongoClient } from "../shared/mongo-config";
import { <PERSON>riptBase, ScriptContext } from "../shared/script-base";
import { ObjectId } from "mongodb";

const chargeflowIds = ["67f422242b2de006c01d653a"];

class Script extends ScriptBase {
  public getName(): string {
    return "Fix resolvedDate for woo disputes";
  }
  public getAuthor(): string {
    return "<PERSON><PERSON><PERSON>";
  }
  public getTicket(): string {
    return "CHA-15015";
  }
  async dryRun(context: ScriptContext) {
    console.log("Running in dry run (not changing anything)");

    for (const chargeflowId of chargeflowIds) {
      await getBrokenDisputesFromProductionDB(chargeflowId);
    }
  }

  async wetRunOnSecondary(context: ScriptContext) {
    await this.doWetRun(false);
  }
  async wetRunOnPrimary(context: ScriptContext) {
    await this.doWetRun(true);
  }

  private async doWetRun(mainRun: boolean) {
    console.log("Running in wet run (changing data)");

    const client = await loadMongoClient();
    const session = client.startSession();
    try {
      session.startTransaction();

      for (const chargeflowId of chargeflowIds) {
        const disputes = await getBrokenDisputesFromProductionDB(chargeflowId);
        if (mainRun) {
          await updateBrokenDisputesFromProductionDB(disputes);
        }
      }

      await session.commitTransaction();
      client.close();
    } catch (ex) {
      console.warn("Aborting transaction");
      await session.abortTransaction();
      console.error(ex);
      throw ex;
    } finally {
      session.endSession();
    }
  }
}

async function getBrokenDisputesFromProductionDB(chargeflowId: string) {
  const client = await loadMongoClient();

  const disputes = await client
    .db("chargeflow_api")
    .collection("disputes")
    .find({
      "lastDisputeStatus.status": { $in: ["won", "warning_won"] },
      "chargeflow.resolved.resolvedDate": { $in: [null, undefined] },
      chargeflowId: new ObjectId(chargeflowId),
    })
    .toArray();

  console.log(
    `Number of broken disputes  ${
      disputes?.length
    } for chargeflowId ${chargeflowId.toString()}`
  );

  return disputes;
}

async function updateBrokenDisputesFromProductionDB(
  disputes: any[],
) {
  const client = await loadMongoClient();

  for (const dispute of disputes) {
    const txnId = dispute.dispute.rawData.dispute.balance_transaction;

    if (txnId) {
      const txn = findTxn(
        txnId,
        dispute.dispute.rawData.dispute.balance_transactions
      );

      if (txn) {
        const resolveDate = new Date(txn.available_on * 1000);

        await client
          .db("chargeflow_api")
          .collection("disputes")
          .updateOne(
            { _id: dispute._id },
            { $set: { "chargeflow.resolved.resolvedDate": resolveDate } }
          );

        console.log(`Updated dispute ${dispute._id}`);
      }
    } else if (dispute.lastDisputeStatus.status === "warning_won") {
      const resolveDate = dispute.lastDisputeStatus.statusDate;

      await client
          .db("chargeflow_api")
          .collection("disputes")
          .updateOne(
            { _id: dispute._id },
            { $set: { "chargeflow.resolved.resolvedDate": resolveDate } }
          );

      console.log(`Updated dispute ${dispute._id}`);
    }
  }
}

function findTxn(txnId: string, txnArr: any[]) {
  if (txnId && Array.isArray(txnArr)) {
    return txnArr.find((txn) => txn.id === txnId);
  }

  return null;
}

const script = new Script();
script.main();

# Purpose
This script updates disputes statuses if chargeflowId and transactionId of alert and dispute are matching

https://linear.app/chargeflow/issue/CHA-6959/create-a-script-to-update-retro

## Motivation
Set dispute status to "Prevented" (due to existing alert) instead of "Lost"

## Running this script
`npx ts-node ./script.ts <dry wet-primary>`

## Required env variables
MONGO_URI=<mongo_uri_string>
BATCH_SIZE=100

### Modes
dry: logs total amount of alerts with service RDR and existing transactionId and total amount of disputes which statuses have to be updated (match chargeflowId and transactionId of relevant alerts)
wet-primary: updates disputes statuses if chargeflowId and transactionId of alert and dispute are matching

### How this script was tested
dry and wet-primary was tested on dev mongo DB cluster
dry was tested on prod mongo DB cluster
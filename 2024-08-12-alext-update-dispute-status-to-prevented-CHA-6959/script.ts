import { MongoClient, WithId, Document } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext, ScriptMode } from "../shared/script-base";

export class UpdateDisputeStatusToPrevented extends ScriptBase {
    public getName(): string {
        return 'Update dispute status to prevented';
    }

    public getAuthor(): string {
        return 'Alex Tkach';
    }
    
    public getTicket(): string {
        return 'CHA-6959';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.logic(context);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context);
    }

    async wetRunOnSecondary(): Promise<void> {
        throw new Error('Method is not implemented.');
    }

    async logic(ctx: ScriptContext) {
        try {
            const mode = (ctx.args[0] ?? 'dry') as ScriptMode;
            const batchSize = process.env.BATCH_SIZE ? parseInt(process.env.BATCH_SIZE) : 100;
            const client: MongoClient = await loadMongoClient();

            let skip = 0;
            let alerts: WithId<Document>[] = [];
            let totalDocsToUpdate = 0;
            let totalAlerts = 0;
            let totalDocsUpdated = 0;
            
            do {
                alerts = await this.getAlerts(client, skip, batchSize);
                if (alerts.length === 0) {
                    break;
                }
                totalAlerts += alerts.length;

                if (mode === 'dry') {
                    const matchingDisputes = await this.countMatchingDisputes(client, alerts);
                    console.log('Found %s disputes to update continue to next batch', matchingDisputes);
                    totalDocsToUpdate += matchingDisputes;
                }

                if (mode === 'wet-primary') {
                    const updateDisputeStatusOps = this.createUpdateDisputeStatusOps(alerts);
                    const updatedDisputesCount = await this.updateDisputeStatus(client, updateDisputeStatusOps);

                    console.log('Updated %s disputes, continue to next batch', updatedDisputesCount);
                    totalDocsUpdated += updatedDisputesCount;
                }

                skip += batchSize;
            } while (alerts.length > 0);

            console.log('Total alerts with service RDR and existing transactionId: %s', totalAlerts);
            console.log('Total disputes to update: %s', totalDocsToUpdate);

            if (mode === 'wet-primary') {
                console.log('Total disputes updated: %s', totalDocsUpdated);
            }
        } catch (error) {
            console.error('Error: %j', error);
        }
    }

    private async getAlerts(client: MongoClient, skip: number, batchSize: number): Promise<WithId<Document>[]> {
        try {
            return client
            .db('chargeflow_api')
            .collection('alerts')
            .find({
                service: 'rdr',
                processorTransactionId: { $exists: true },
            }, { 
                projection: {
                    chargeflowId: 1,
                    alertId: 1,
                    accountId: 1,
                    processorTransactionId: 1,
                } 
            })
            .skip(skip)
            .limit(batchSize)
            .toArray();
        } catch (error) {
            console.error('Error fetching alerts: %j', error);
            throw error;
        }
    }

    private createUpdateDisputeStatusOps(alerts: any[]): any[] {
        const disputeStatus = {
            dateCreated: new Date(),
            statusDate: new Date(),
            status: 'prevented',
            processorStatus: null,
            source: 'alerts-linking',
        };

        return alerts.map((item) => ({
            updateOne: {
                filter: { 
                    'chargeflowId': item.chargeflowId,
                    'transaction.id': item.processorTransactionId,
                    'lastDisputeStatus.status': 'lost',
                },
                update: {
                    $set: {
                        'lastDisputeStatus': disputeStatus,
                        'alertId': item.alertId,
                    },
                    $push: { 'dispute.disputeStatuses': disputeStatus },
                },
            }
        }));
    }

    private async updateDisputeStatus(client: MongoClient, ops: any[]): Promise<number> {
        try {
            return client
                .db('chargeflow_api')
                .collection('disputes')
                .bulkWrite(ops)
                .then((result) => result.modifiedCount);
        } catch (error) {
            console.error('Error updating disputes: %j', error);
            return 0;
        }
    }

    private async countMatchingDisputes(client: MongoClient, alerts: any[]): Promise<number> {
        try {
            const promises = alerts.map((alert) => {
                return client
                    .db('chargeflow_api')
                    .collection('disputes')
                    .countDocuments({
                        'chargeflowId': alert.chargeflowId,
                        'transaction.id': alert.processorTransactionId,
                        'lastDisputeStatus.status': 'lost',
                    });
            }
            );
            const results = await Promise.all(promises);
            return results.reduce((acc, count) => acc + count, 0);
        } catch (error) {
            console.error('Error counting disputes: ', error);
            return 0;
        }
    }
       
}

const script = new UpdateDisputeStatusToPrevented();
script.main();

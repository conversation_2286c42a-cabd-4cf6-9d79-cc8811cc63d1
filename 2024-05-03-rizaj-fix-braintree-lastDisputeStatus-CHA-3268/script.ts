import dotenv from 'dotenv';
import { ObjectId } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import RocksetClient from '../shared/rockset';
import { ScriptBase, ScriptContext } from '../shared/script-base';

dotenv.config();

interface DisputeStatus {
    status: string;
    processorStatus: string;
    statusDate: Date;
    source: string;
}

interface Dispute {
    _id: string;
    disputeStatuses: DisputeStatus[];
    lastDisputeStatus: DisputeStatus | string;
}

export class ScriptUpdateStripeUserCompanies extends ScriptBase {
    public getName(): string {
        return 'Set disputes with wrong lastDisputeStatus to correct format.';
    }

    public getAuthor(): string {
        return 'Riza Jumola';
    }

    public getTicket(): string {
        return 'CHA-3268';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    getUpdateDisputeQuery(dispute: Dispute) {
        const filter = { _id: new ObjectId(dispute._id) };
        const lastDisputeStatus = dispute.disputeStatuses?.at(-1);
        if (!lastDisputeStatus) {
            console.log('No dispute status found for', dispute._id);
            return;
        }
        const update = { $set: { lastDisputeStatus } };
        return { updateOne: { filter, update } };
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {
        const rocksetClient = new RocksetClient();
        const mongoClient = await loadMongoClient();
        const disputesCollection = mongoClient
            .db('chargeflow_api')
            .collection('disputes');
        const query = rocksetClient.queryBuilder()
            .select(
                'd._id',
                'd.dispute.disputeStatuses',
                'd.lastDisputeStatus'
            )
            .fromRaw('prod.disputes_view d')
            .whereRaw(`TYPEOF(d.lastDisputeStatus) != 'object'`)
            .toQuery();
        const handleDryRun = async () => {
            const toUpdate: Dispute[] = await rocksetClient.executeQuery(query);
            console.log(`Will update ${toUpdate.length} disputes...`);
            console.log('Update queries...');
            const updateQueries = toUpdate.map((dispute: Dispute) =>
                this.getUpdateDisputeQuery(dispute)
            ).filter(Boolean);
            console.log(JSON.stringify(updateQueries, null, 2));
        };

        const handleWetRun = async () => {
            const toUpdate = await rocksetClient.executeQuery(query);
            console.log(`Found ${toUpdate.length} to update.`);
            const updateQueries = toUpdate.map((dispute: Dispute) =>
                this.getUpdateDisputeQuery(dispute)
            ).filter(Boolean);
            const res = await disputesCollection.bulkWrite(updateQueries);
            console.log('Execution result...');
            console.log(JSON.stringify(res, null, 2));
        };

        try {
            if (isDryRun) {
                await handleDryRun();
            } else {
                await handleWetRun();
            }
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        } finally {
            mongoClient.close();
        }
    }
}

const script = new ScriptUpdateStripeUserCompanies();
script.main();

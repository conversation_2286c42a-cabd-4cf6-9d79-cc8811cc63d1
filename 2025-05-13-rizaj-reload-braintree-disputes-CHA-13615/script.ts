import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { Db, ObjectId } from "mongodb";
import { AwsEventBridge } from "@chargeflow-team/chargeflow-utils-sdk";
dotenv.config();

const SOURCE_FETCH = 'fetch';
const PROCESSOR = 'braintree';
const PROCESSOR_DISPUTE_REASON: Record<string, string> = Object.freeze({
  'cancelled_recurring_transaction': 'canceled_recurring_billing',
  'credit_not_processed': 'credit_not_processed',
  'duplicate': 'duplicate_charge',
  'fraud': 'fraud',
  'general': 'general',
  'invalid_account': 'incorrect_account_details',
  'not_recognized': 'unauthorized',
  'product_not_received': 'not_received',
  'product_unsatisfactory': 'not_as_described',
  'transaction_amount_differs': 'incorrect_amount',
});
const PROCESSOR_DISPUTE_STAGE: Record<string, string> = Object.freeze({
  'retrievals': 'inquiry',
  'chargeback': 'chargeback',
  'pre_arbitration': 'pre_arbitration',
});
const PROCESSOR_DISPUTE_STATUS: Record<string, string> = Object.freeze({
  'open': 'needs_response',
  'accepted': 'lost',
  'auto_accepted': 'lost',
  'disputed': 'under_review',
  'expired': 'lost',
  'won': 'won',
  'lost': 'lost',
});
const DISPUTE_STATUS_RESOLVED = Object.freeze([
  'won',
  'lost',
  'accepted',
  'expired',
  'auto_accepted',
]);

interface BTDisputeStatusHistory {
  status: string;
  effectiveDate: string;
}

interface BTDispute {
  statusHistory: BTDisputeStatusHistory[];
  chargeflowId: string;
  caseNumber: string;
  createdAt: string;
  kind: string;
  transaction: {
    id: string;
  }
  amountDisputed: string;
  currencyIsoCode: string;
  reason: string;
  reasonCode: string;
  reasonDescription: string;
  processorComments: string;
  responseDeadline: string;
  evidence: string[];
  merchantAccountId: string;
}

interface Shop {
  _id: ObjectId;
  name: string;
  chargeflow_id: ObjectId;
  accountId: ObjectId;
}

export class Script extends ScriptBase {

  public db!: Db;
  public getName(): string {
    return "Re-load Braintree disputes from braintreeRawData to be saved to mongodb";
  }

  public getAuthor(): string {
    return "Riza Jumola";
  }

  public getTicket(): string {
    return "CHA-13615";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context);
  }

  async wetRunOnSecondary(context: ScriptContext): Promise<void> {
    await this.logic(context);
  }

  private async logic(ctx: ScriptContext) {
    try {
      isEnvVarsOkOrThrow([
        "MONGO_URI",
        "MONGO_DB_NAME",
        "DISPUTES_EVENT_BUS_NAME",
      ]);

      const mongo = await loadMongoClient();
      console.log("Mongo client loaded");

      this.db = mongo.db(process.env.MONGO_DB_NAME);
      const unsyncedDisputes = await this.getUnsyncedBraintreeDisputes();
      console.log('Found ' + unsyncedDisputes.length);
      const isDryRun = this.mode === 'dry';
      if (!isDryRun) {
        const pool = this.mode === 'wet-secondary' ? [unsyncedDisputes[0]] : unsyncedDisputes;
        for (const btDispute of pool) {
          console.log('Processing ' + btDispute.caseNumber);
          try {
            if (!btDispute.shop) {
              throw new Error('Removed shop '  + btDispute.chargeflowId);
            }
            const shop = btDispute.shop;
            const cfDispute = this.mapRawBTDisputeToChargeflowDispute(
              btDispute,
              shop.chargeflow_id.toString(),
              shop.name,
              shop._id.toString(),
              shop.accountId.toString(),
            );
            await this.publishDisputeReceivedEvent(cfDispute);
          } catch (err) {
            console.log('Error processing ' + btDispute.caseNumber);
            console.log(err);
          }
        }
      }
    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }

  private async getUnsyncedBraintreeDisputes(): Promise<(BTDispute & { shop: Shop })[]> {
    const braintreeDisputesRawDataColl = this.db.collection('braintreeDisputesRawData');
    const rawDisputes = await braintreeDisputesRawDataColl.aggregate<BTDispute & { shop: Shop }>([
  {
    $match: {
      createdAt: {
        $lte: "2025-05-14T00:00:00.000Z"
      }
    }
  },
  {
    $lookup: {
      from: "disputes",
      let: {
        caseNumber: "$caseNumber",
        cfId: "$chargeflowId"
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                {
                  $eq: [
                    "$dispute.id",
                    "$$caseNumber"
                  ]
                },
                {
                  $eq: ["$chargeflowId", "$$cfId"]
                }
              ]
            }
          }
        }
      ],
      as: "dispute"
    }
  },
  {
    $unwind:
      /**
       * path: Path to the array field.
       * includeArrayIndex: Optional name for index.
       * preserveNullAndEmptyArrays: Optional
       *   toggle to unwind null and empty values.
       */
      {
        path: "$dispute",
        preserveNullAndEmptyArrays: true
      }
  },
  {
    $match: {
      dispute: {
        $exists: false
      }
    }
  },
  {
    $lookup: {
      from: "shops",
      localField: "chargeflowId",
      foreignField: "chargeflow_id",
      as: "shop"
    }
  },
  {
    $unwind:
      /**
       * path: Path to the array field.
       * includeArrayIndex: Optional name for index.
       * preserveNullAndEmptyArrays: Optional
       *   toggle to unwind null and empty values.
       */
      {
        path: "$shop",
        preserveNullAndEmptyArrays: true
      }
  },
  {
    $match:
      /**
       * query: The query in MQL.
       */
      {
        shop: {
          $exists: true
        }
      }
  },
  // {
  //   $addFields: {
  //     shop: {
  //       $first: "$shops"
  //     }
  //   }
  // }
  {
    $match: {
      "shop.chargeflow_status": {
        $in: ["registered", "active", "enabled"]
      }
    }
  },
  {
    $lookup: {
      from: "processors",
      let: {
        cfId: "$chargeflowId"
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                {
                  $eq: [
                    "$chargeflow_id",
                    "$$cfId"
                  ]
                },
                {
                  $eq: [
                    "$processor_name",
                    "braintree"
                  ]
                }
              ]
            }
          }
        }
      ],
      as: "processor"
    }
  },
  {
    $unwind: {
      path: "$processor",
      preserveNullAndEmptyArrays: true
    }
  },
  {
    $match:
      /**
       * query: The query in MQL.
       */
      {
        processor: {
          $exists: true
        }
      }
  },
  {
    $project:
      /**
       * specifications: The fields to
       *   include or exclude.
       */
      {
        processor: 0
      }
  }
]).toArray();
    return rawDisputes;
  }

  private mapRawBTDisputeToChargeflowDispute(
    braintreeDispute: BTDispute,
    chargeflowId: string,
    shopName: string,
    shopId: string,
    accountId: string,
  ) {
    try {
      const disputeStatuses = braintreeDispute.statusHistory.map(
        item => this.mapDisputeStatus(item, SOURCE_FETCH),
      ).reverse();

      const latestStatus = disputeStatuses?.at(-1);
      const lastDisputeStatus = {
        'dateCreated': new Date(),
        'statusDate': latestStatus?.statusDate,
        'status': latestStatus?.status,
        'processorStatus': latestStatus?.processorStatus,
        'source': 'fetch',
      };

      const isClosed = DISPUTE_STATUS_RESOLVED.includes(latestStatus?.status as string);
      const closedDate = isClosed && latestStatus?.statusDate ? new Date(latestStatus.statusDate) : null;

      return {
        shopName: shopName,
        shopId: new ObjectId(shopId),
        chargeflowId: new ObjectId(chargeflowId),
        accountId: new ObjectId(accountId),
        liveMode: true,
        lastDisputeStatus: lastDisputeStatus,
        id: braintreeDispute.caseNumber,
        dateCreated: new Date(braintreeDispute.createdAt),
        processor: PROCESSOR,
        source: PROCESSOR,
        stage: PROCESSOR_DISPUTE_STAGE[braintreeDispute.kind],
        transactionId: braintreeDispute.transaction.id,
        amount: Number(braintreeDispute.amountDisputed),
        currency: braintreeDispute.currencyIsoCode,
        reason: PROCESSOR_DISPUTE_REASON[braintreeDispute.reason],
        processorReason: braintreeDispute.reason,
        reasonCode: braintreeDispute.reasonCode,
        reasonDescription: braintreeDispute.reasonDescription,
        processorComments: braintreeDispute.processorComments,
        disputeStatuses,
        responseDue: new Date(braintreeDispute.responseDeadline),
        closedDate,
        submittedCount: braintreeDispute.evidence.length,
        disputeFee: null,
        statementDescriptor: null,
        mid: braintreeDispute.merchantAccountId,
        isRefundable: null,
        intentionalFraud: null,
      };
    } catch (error) {
      console.error('Failed to map dispute', { braintreeDispute });
      throw error;
    }
  }

  private mapDisputeStatus(itemStatusHistory: BTDisputeStatusHistory, eventSource: string) {
    return {
      status: PROCESSOR_DISPUTE_STATUS[itemStatusHistory.status],
      dateCreated: new Date(),
      statusDate: new Date(itemStatusHistory.effectiveDate),
      processorStatus: itemStatusHistory.status,
      source: eventSource,
    };
  }

  private async publishDisputeReceivedEvent(dispute: Record<string, any>) {
    console.log('JSON.stringify(dispute)', JSON.stringify(dispute));
    await AwsEventBridge.putEvent(
      JSON.stringify(dispute),
      'prod-braintree-integration',
      process.env.DISPUTES_EVENT_BUS_NAME as string,
      { DetailType: 'DISPUTE_RECIEVED', }
    );
    console.log('Successfully sent DISPUTE_RECIEVED event for: ' + dispute.id);
  }
}

const script = new Script();
script.main();

{"name": "convert-processorid-to-objectid", "version": "1.0.0", "description": "Script to convert processorId field from string to MongoDB ObjectId in processor-meta-data collection", "main": "convert-processorId-to-objectId.ts", "scripts": {"dry": "npx ts-node convert-processorId-to-objectId.ts dry", "wet-secondary": "npx ts-node convert-processorId-to-objectId.ts wet-secondary", "wet-primary": "npx ts-node convert-processorId-to-objectId.ts wet-primary"}, "dependencies": {"mongodb": "^4.0.0", "dotenv": "^16.0.0"}, "devDependencies": {"@types/node": "^18.0.0", "ts-node": "^10.0.0", "typescript": "^4.0.0"}}
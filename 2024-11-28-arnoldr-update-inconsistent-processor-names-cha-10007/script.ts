import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Db } from 'mongodb';
import path from 'path';
import fs from 'fs/promises';

dotenv.config();

const mongo = loadMongoClient();

const processorNameMapping = {
  businesstrack: "businessTrack",
  Merlink: "merlink",
  "afterpay/clearpay": "afterpay",
  SafeCharge: "nuvei",
  safecharge: "nuvei",
  "checkout.com": "checkout",
  globalpayments: "globalPayments"
};

export class UpdateForInconsistentProcessorNames extends ScriptBase {
  db: Db | undefined = undefined;

  public getName(): string {
    return "Update inconsistent processor names";
  }

  public getAuthor(): string {
    return "Arnold Ramos";
  }

  public getTicket(): string {
    return "CHA-10007";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async fetchCounts() {
    const mongoInstance = await mongo;
    const collection = mongoInstance.db("chargeflow_api").collection("disputes");

    for (const [filterName] of Object.entries(processorNameMapping)) {
      const count = await collection.countDocuments({ 'dispute.processor': filterName });
      console.log(`Count for ${filterName}: ${count}`);
    }
  }


  private async logic(context: ScriptContext, isDryRun: boolean) {
    if (isDryRun) {
      return this.fetchCounts();
    }

    const mongoInstance = await mongo;
    const collection = mongoInstance.db("chargeflow_api").collection("disputes");

    const bulkOps = Object.entries(processorNameMapping).map(
      ([filterName, updateName]) => ({
        updateMany: {
          filter: { 'dispute.processor': filterName },
          update: { $set: { 'dispute.processor': updateName } },
        },
      })
    );

    console.log(JSON.stringify(bulkOps, null, 2));

    const result = await collection.bulkWrite(bulkOps);
    console.log(result);
  }
}

const script = new UpdateForInconsistentProcessorNames();
script.main();

import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Collection, ObjectId, Filter } from 'mongodb';
import * as fs from 'fs';
import * as path from 'path';
import { MongoClient } from 'mongodb';

dotenv.config();

interface Charge {
    _id: ObjectId;
    chargeDescription: string;
    disputeId?: ObjectId | null;
    caseId?: string;
}

interface Dispute {
    _id: ObjectId;
    dispute: {
        id: string;
    };
}

interface ChangeLog {
    chargeId: string;
    before: {
        disputeId?: string | null;
        caseId?: string | null;
    };
    after: {
        disputeId: string;
        caseId: string;
    };
}

interface ChargeUpdate {
    charge: Charge;
    caseId: string;
    dispute?: Dispute;
}

const BATCH_SIZE = 100;

export class AddDisputeIdToCharges extends ScriptBase {
    private chargesCollection!: Collection<Charge>;
    private disputesCollection!: Collection<Dispute>;
    private client!: MongoClient;

    constructor() {
        super();
    }

    private async initializeMongo() {
        this.client = await loadMongoClient();
        const db = this.client.db('chargeflow_api');
        this.chargesCollection = db.collection<Charge>('charges');
        this.disputesCollection = db.collection<Dispute>('disputes');
    }

    public getName(): string {
        return 'Add disputeId to charges on charges collection';
    }

    public getAuthor(): string {
        return 'Asaf Gamliel';
    }

    public getTicket(): string {
        return 'CHA-14175';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private extractCaseId(chargeDescription: string): string | null {
        // Look for case ID between "resolved dispute" and "success-fee"
        if (!chargeDescription) {
            return null;
        }
        const match = chargeDescription.match(/resolved dispute\s+([^\s]+)\s*success/i);
        return match ? match[1] : null;
    }

    private async writeChangeLog(changes: ChangeLog[]): Promise<void> {
        const outputDir = path.join(__dirname, 'output');
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir);
        }
        const outputPath = path.join(outputDir, `changes-${new Date().toISOString()}.json`);
        await fs.promises.writeFile(outputPath, JSON.stringify(changes, null, 2));
        console.log(`Change log written to ${outputPath}`);
    }

    private async extractCaseIdsFromCharges(charges: Charge[]): Promise<ChargeUpdate[]> {
        const updates: ChargeUpdate[] = [];
        
        for (const charge of charges) {
            const caseId = this.extractCaseId(charge.chargeDescription);
            if (caseId) {
                updates.push({ charge, caseId });
            } else {
                console.log(`Could not extract case ID from charge ${charge._id} description: ${charge.chargeDescription}`);
            }
        }
        
        return updates;
    }

    private async findDisputes(updates: ChargeUpdate[]): Promise<ChargeUpdate[]> {
        const caseIds = updates.map(update => update.caseId);
        const disputes = await this.disputesCollection
            .find({ 'dispute.id': { $in: caseIds } })
            .toArray();

        const disputeMap = new Map(disputes.map(d => [d.dispute.id, d]));
        
        return updates.map(update => ({
            ...update,
            dispute: disputeMap.get(update.caseId)
        }));
    }

    private processUpdate(
        update: ChargeUpdate,
        isDryRun: boolean,
        bulkOps: any[]
    ): number {
        if (!update.dispute) {
            return 0;
        }

        if (isDryRun) {
            console.log(`[DRY RUN] Would update charge ${update.charge._id}:`);
            console.log(`  - Case ID: ${update.caseId}`);
            console.log(`  - Dispute ID: ${update.dispute._id}`);
            return 1;
        } else {
            bulkOps.push({
                updateOne: {
                    filter: { _id: update.charge._id },
                    update: {
                        $set: {
                            disputeId: update.dispute._id,
                            caseId: update.caseId
                        }
                    }
                }
            });
            return 0;
        }
    }
    
    private async updateBatch(
        batch: ChargeUpdate[], 
        isDryRun: boolean,
        changes: ChangeLog[]
    ): Promise<{ updated: number; failed: number }> {
        let updated = 0;
        let failed = 0;

        const bulkOps: any[] = [];
        for (const update of batch) {
            if (!update.dispute) {
                console.log(`No dispute found for case ID ${update.caseId} (charge ${update.charge._id})`);
                failed++;
                continue;
            }

            updated += this.processUpdate(update, isDryRun, bulkOps);

            changes.push({
                chargeId: update.charge._id.toString(),
                before: {
                    disputeId: update.charge.disputeId?.toString() || null,
                    caseId: update.charge.caseId || null
                },
                after: {
                    disputeId: update.dispute._id.toString(),
                    caseId: update.caseId
                }
            });
        }

        if (!isDryRun && bulkOps.length > 0) {
            try {
                console.time(`Bulk update ${bulkOps.length} charges`);
                const result = await this.chargesCollection.bulkWrite(bulkOps);
                console.timeEnd(`Bulk update ${bulkOps.length} charges`);
                updated = result.modifiedCount;
                console.log(`Bulk updated ${updated} charges`);
            } catch (error) {
                console.error('Bulk update failed:', error);
                failed += bulkOps.length;
            }
        }

        return { updated, failed };
    }

    private async processBatchOfCharges(
        isDryRun: boolean,
        changes: ChangeLog[],
        skip: number
    ): Promise<{ processedCount: number; updatedCount: number; failedCount: number; hasMore: boolean }> {
        const query: Filter<Charge> = {
            $or: [
                { disputeId: null },
                { disputeId: { $exists: false } }
            ]
        };

        const charges = await this.chargesCollection
            .find(query)
            .skip(skip)
            .limit(BATCH_SIZE)
            .toArray();

        if (charges.length === 0) {
            console.log('No charges to process');
            return { processedCount: 0, updatedCount: 0, failedCount: 0, hasMore: false };
        }

        const updates = await this.extractCaseIdsFromCharges(charges);
        const updatesWithDisputes = await this.findDisputes(updates);
        const { updated, failed } = await this.updateBatch(updatesWithDisputes, isDryRun, changes);

        return {
            processedCount: charges.length,
            updatedCount: updated,
            failedCount: failed,
            hasMore: charges.length === BATCH_SIZE
        };
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {
        console.time('Total script execution');
        await this.initializeMongo();
        let totalProcessed = 0;
        let totalUpdated = 0;
        let totalFailed = 0;
        const changes: ChangeLog[] = [];

        try {
            let skip = 0;
            let hasMore = true;

            console.log('Starting to process charges in batches...');

            while (hasMore) {
                console.time(`Batch starting at ${skip}`);
                const {
                    processedCount,
                    updatedCount,
                    failedCount,
                    hasMore: moreRecords
                } = await this.processBatchOfCharges(
                    isDryRun,
                    changes,
                    skip
                );
                console.timeEnd(`Batch starting at ${skip}`);

                totalProcessed += processedCount;
                totalUpdated += updatedCount;
                totalFailed += failedCount;
                hasMore = moreRecords;

                if (processedCount > 0) {
                    console.log(`Processed batch starting at ${skip}:`);
                    console.log(`  - Processed: ${processedCount}`);
                    console.log(`  - Updated: ${updatedCount}`);
                    console.log(`  - Failed: ${failedCount}`);
                }

                skip += BATCH_SIZE;
            }
            
            console.log('\nFinal Summary:');
            console.log(`Total charges processed: ${totalProcessed}`);
            console.log(`Successfully ${isDryRun ? 'would be updated' : 'updated'}: ${totalUpdated}`);
            console.log(`Failed: ${totalFailed}`);

            await this.writeChangeLog(changes);
            
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        } finally {
            await this.client.close();
            console.timeEnd('Total script execution');
        }
    }
}

const script = new AddDisputeIdToCharges();
script.main();

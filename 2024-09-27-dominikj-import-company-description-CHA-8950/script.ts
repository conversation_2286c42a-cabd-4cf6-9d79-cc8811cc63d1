import dotenv from "dotenv";
import { AnyBulkWriteOperation, Document, ObjectId, WithId } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { IMongoCompany } from "@chargeflow-team/data-access/dist/Company/Types";
import neatCsv from "neat-csv";
import fs from "fs";

const SKIP_CONTENT = "Error: Unable to find website";

type CompanyDescriptionCsvItem = {
  "Chargeflow ID": string;
  "Company Brief": string;
};

dotenv.config();

export class ScriptMigrateVariableKeyMappings extends ScriptBase {
  public getName(): string {
    return "Import company description";
  }

  public getAuthor(): string {
    return "Dominik Jan<PERSON>k";
  }

  public getTicket(): string {
    return "CHA-8950";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    let updatedCount = 0;
    const client = await loadMongoClient();
    const collection = client
      .db("chargeflow_api")
      .collection<IMongoCompany>("companies");

    const companyDescriptionDataCsv = fs.readFileSync(
      "company-descriptions.csv"
    );
    const companyDescriptionData = await neatCsv<CompanyDescriptionCsvItem>(
      companyDescriptionDataCsv
    );
    const filteredCompanyDescriptionData = companyDescriptionData.filter(
      (row: any) =>
        !row["Company Brief"].includes(SKIP_CONTENT) &&
        row["Company Brief"].trim().length > 0
    );
    const handleDryRun = (companyDescription: CompanyDescriptionCsvItem) => {
      console.log(
        `Would update company ${companyDescription["Chargeflow ID"]} with description: ${companyDescription["Company Brief"]}`
      );
    };

    const createBulkOperation = (
      companyDescription: CompanyDescriptionCsvItem
    ): AnyBulkWriteOperation<IMongoCompany> => {
      console.log(
        `Will update company ${companyDescription["Chargeflow ID"]} with description: ${companyDescription["Company Brief"]}`
      );
      return {
        updateMany: {
          filter: {
            "users.chargeflowId": new ObjectId(
              companyDescription["Chargeflow ID"]
            ),
          },
          update: {
            $set: {
              "users.companyDescription": companyDescription["Company Brief"],
            },
          },
        },
      };
    };

    let bulkOps: AnyBulkWriteOperation<IMongoCompany>[] = [];

    if (!isDryRun) {
      bulkOps = filteredCompanyDescriptionData
        .map(createBulkOperation)
        .filter((op) => op !== null) as AnyBulkWriteOperation<IMongoCompany>[];
    } else {
      filteredCompanyDescriptionData.forEach(handleDryRun);
    }

    if (!isDryRun && bulkOps.length > 0) {
      const result = await collection.bulkWrite(bulkOps);
      updatedCount += result.modifiedCount;
      console.log(
        `${new Date().toISOString()}: Processed ${
          filteredCompanyDescriptionData.length
        } documents. Total updated so far: ${updatedCount}`
      );
    }

    if (!isDryRun) {
      console.log(`Total documents updated: ${updatedCount}`);
    }
  }
}

const script = new ScriptMigrateVariableKeyMappings();
script.main();

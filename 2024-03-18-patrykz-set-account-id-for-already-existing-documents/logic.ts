import { MongoClient, Db, ObjectId } from 'mongodb';
import get from 'lodash.get';
import { allowedCollections } from './allowedCollections';

export interface Logger {
    log: (message: string) => void;
}

export class SetAccountIdForAlreadyExistingDocumentsMigrationLogic {
    private db: Db

    constructor(
        mongoClient: MongoClient,
        private readonly logger: Logger,
    ) {
        this.db = mongoClient.db('chargeflow_api');
    }

    async execute(batchSize: number, page: number, dryRun: boolean, collectionName: string, chargeflowIdPath: string, accountIdPath: string): Promise<void> {
        try {
            if (!allowedCollections.includes(collectionName)) {
                const errorMessage = `Collection ${collectionName} is not allowed for this migration`;
                this.logger.log(errorMessage);
                throw new Error(errorMessage);
            }

            this.logger.log('Setting account id for already existing documents started, please wait ...');

            const itemFilter = {
                [chargeflowIdPath]: { $exists: true },
                $or: [
                    { [accountIdPath]: { $exists: false } },
                    { [accountIdPath]: null }
                ]};
            const totalItems = await this.db.collection(collectionName).countDocuments(itemFilter);
            const totalBatches = Math.ceil(totalItems / batchSize);

            this.logger.log(`Total documents to process: ${totalItems}`);
            this.logger.log(`Total batches to process: ${totalBatches}`);

            if (dryRun) {
                this.logger.log('Dry run mode enabled, no changes made to the database');

                return;
            }

            for (let currentBatch = page; currentBatch <= totalBatches; currentBatch++) {
                try {
                    this.logger.log(`Processing batch ${currentBatch} of ${totalBatches}`);
                    const currentPage = (currentBatch - 1) * batchSize;
                    const items = await this.db.collection(collectionName)
                        .find(itemFilter)
                        .skip(currentPage)
                        .limit(batchSize)
                        .toArray();

                    await Promise.all(
                        items.map(async item => {
                            if (!item || !get(item, chargeflowIdPath)) {
                                return;
                            }

                            let chargeflowId = get(item, chargeflowIdPath);
                            const accountObject = await this.db.collection('account').findOne({
                                "legacy.chargeflowId": new ObjectId(chargeflowId),
                            });

                            if (!accountObject || !accountObject._id) {
                                this.logger.log(`Couldn't find account object for given chargeflowId: ${chargeflowId}, skipping processing document from collection: ${collectionName} with id: ${item._id}`);
                                return;
                            }

                            return this.db.collection(collectionName).updateOne(
                                {
                                    _id: new ObjectId(item._id),
                                }, {
                                    $set: {
                                        [accountIdPath]: new ObjectId(accountObject._id),
                                    },
                                });
                        }),
                    );

                    this.logger.log(`Batch ${currentBatch} of ${totalBatches} processed successfully`);
                } catch (error: unknown) {
                    if (error instanceof Error) {
                        this.logger.log(`Batch ${currentBatch} of ${totalBatches} failed, reason: ${error.message}`);
                    }

                    throw error;
                }
            }

            this.logger.log('Setting account id for already existing documents finished successfully');
        } catch (error) {
            if (error instanceof Error) {
                this.logger.log(`Setting account id for already existing documents failed, reason: ${error.message}`);
            }

            throw error;
        }
    }
}

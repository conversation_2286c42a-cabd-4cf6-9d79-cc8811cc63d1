import dotenv from "dotenv";
import { MongoClient } from "mongodb";
import { ScriptBase } from "../shared/script-base";
import packageJson from './package.json';
import { SetAccountIdForAlreadyExistingDocumentsMigrationLogic } from "./logic";

dotenv.config();

export class <PERSON>ript extends ScriptBase {
    public getName(): string {
        return packageJson.name;
    }

    public getAuthor(): string {
        return packageJson.author;
    }

    public getTicket(): string {
        return 'CHA-2807';
    }

    async dryRun(): Promise<void> {
        const dryRun = true;

        await this.execute(dryRun);
    }

    async wetRunOnPrimary(): Promise<void> {
        const dryRun = false;

        await this.execute(dryRun);
    }

    wetRunOnSecondary(): Promise<void> {
        throw new Error("Method not implemented.");
    }

    private async execute(dryRun: boolean): Promise<void> {
        const { batchSize, page, collectionName, chargeflowIdPath, accountIdPath} = this.getParamsFromEnvVars();

        const mongoClient = await MongoClient.connect(process.env.MONGO_URI!);
        const logger = {
            log: (message: string) => {
                console.log(message);
            },
        }

        const logic = new SetAccountIdForAlreadyExistingDocumentsMigrationLogic(
            mongoClient,
            logger,
        );

        await logic.execute(batchSize, page, dryRun, collectionName, chargeflowIdPath, accountIdPath,);
    }

    private getParamsFromEnvVars(): { batchSize: number, page: number, collectionName: string, chargeflowIdPath: string, accountIdPath: string } {
        const batchSize = process.env.BATCH_SIZE
            ? parseInt(process.env.BATCH_SIZE, 10)
            : 1;
        const page = process.env.PAGE
            ? parseInt(process.env.PAGE, 10)
            : 1;
        const collectionName = process.env.COLLECTION_NAME!;
        const chargeflowIdPath = process.env.CHARGEFLOW_ID_PATH!;
        const accountIdPath = process.env.ACCOUNT_ID_PATH!;

        return {
            batchSize,
            page,
            collectionName,
            chargeflowIdPath,
            accountIdPath,
        };
    }
}

const script = new Script();
script.main();

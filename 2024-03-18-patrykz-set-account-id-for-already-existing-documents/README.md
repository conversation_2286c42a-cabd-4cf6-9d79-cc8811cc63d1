# Purpose
This script sets account id for every document that has chargeflow id but does not have account id

https://linear.app/chargeflow/issue/CHA-2807/[new-model]-migrate-every-document-that-has-reference-to-chargeflow-id

## Motivation
Every document that has chargeflow id set should also have account id to start using account model in the future.

## Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

1. Set the following environment variables in CWD:
```
MONGO_URI=<db connection string>
COLLECTION_NAME=<db collection name>
CHARGEFLOW_ID_PATH=<chargeflow id path in document>
ACCOUNT_ID_PATH=<account id path in document>
BATCH_SIZE=<batch size>
PAGE=<on which migration stops if not provided starting from page 1>
```

2. Install deps from CWD: `npm i`

3. Run the `dry` mode first. See the number of projected items to migrate and adjust the `BATCH_SIZE` env var.

4. Run the migration in `wet-primary` mode once everything is prepared.

5. Repeat the migration run for every document type that has to be migrated. Right now it is [32 document types](https://app.eraser.io/workspace/********************) so the migration has to be ran 32 times.


### Modes
dry - does not update anything, writes the results to the console
wet-secondary - not implemented
wet-primary - executes the actual migration

### How this script was tested
- [x] test migration against `dev` db
- [x] test migration against `test` db

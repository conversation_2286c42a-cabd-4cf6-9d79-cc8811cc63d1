# Purpose:
The purpose of this script is to add addon users to existing shops.

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> <chargeflow-id> <new-user-email-address> <new-user-full-name> <local-aws-cred-profile>`
NOTE: a `.env` file is expected to be present in CWD with

**AWS_REGION**
**MONGO_URI**
**MONGO_DB_NAME**
**AWS_MERCHANT_POOL_ID**
**AWS_MERCHANT_POOL_CLIENT_ID**
**COGNITO_SECRET**
**USERS_EVENT_BUS**

See `.env.example` for an example.

### How this script was tested
[x] Wet run against dev & prod with data diff validation afterwards

### Sample command

```bash
npx ts-node ./script.ts dry 1234567890a <EMAIL> 'Ritz Jumola' chargeflow
```
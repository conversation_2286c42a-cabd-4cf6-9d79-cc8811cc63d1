import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import {
  createAccountMapping,
  createCognitoUserIfNotExists,
  createNewCompanyIfNotExists,
  getAccountMappingsByEmail,
  getBusinessUnit,
  getCompaniesDocByEmailOrThrow,
  getOneAccountMappingByChargeflowId,
  getOrCreateAccountByShopName,
  getParamsOrThrow,
  getShopByChargeflowIdOrThrow,
  isEmailAddedToSameShop,
  isEnvVarsOkOrThrow,
  logAwsProfile,
} from "./lib";
import { loadMongoClient } from "../shared/mongo-config";
import { fromIni } from "@aws-sdk/credential-provider-ini";
import { CognitoIdentityProviderClient } from "@aws-sdk/client-cognito-identity-provider";
import { AwsEventBridge } from "@chargeflow-team/chargeflow-utils-sdk";
import { UserAddedEventSchema, USER_ADDED_DETAIL_TYPE } from "@chargeflow-team/events-infra";

dotenv.config();

export class Script extends ScriptBase {
  public getName(): string {
    return "Adding an addon user to a shop";
  }

  public getAuthor(): string {
    return "Ritz Jumola";
  }

  public getTicket(): string {
    return "CHA-7941";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async putUserAddedEvent(user: Record<string, any>): Promise<void> {
    await AwsEventBridge.putEventWithValidation(
      user,
      UserAddedEventSchema,
      USER_ADDED_DETAIL_TYPE,
      'utility-scripts',
      process.env.USERS_EVENT_BUS_NAME as string,
    );
  }

  private async publishUserAddedEvent(email: string, accountId: string, businessUnitId: string, name: string, cognitoUserId: string, chargeflowId: string): Promise<void> {
    const fullName = name.split(' ');
    await this.putUserAddedEvent({
      email: email,
      accountId: accountId.toString(),
      chargeflowId: chargeflowId.toString(),
      businessUnitId: businessUnitId.toString(),
      firstName: fullName?.[0],
      lastName: fullName?.slice(1).join(' '),
      userId: cognitoUserId,
    });
  }

  private async logic(_ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow();
      console.log("env vars are ok");

      const mongo = await loadMongoClient();
      console.log("mongo client loaded");

      const db = mongo.db(process.env.MONGO_DB_NAME);

      const params = getParamsOrThrow();
      console.log("params are ok %", params);

      const { chargeflowId, email, userFullName, awsProfile } = params;

      logAwsProfile(awsProfile);

      const idp = new CognitoIdentityProviderClient({
        region: process.env.AWS_REGION,
        credentials: fromIni({ profile: awsProfile }),
      });

      const shopDoc = await getShopByChargeflowIdOrThrow(db, chargeflowId);
      const shopName = shopDoc.name;
      const mainShopCompaniesDoc = await getCompaniesDocByEmailOrThrow(
        db,
        shopDoc.email,
        shopDoc.chargeflow_id
      );
      const accountMappingDocs = await getAccountMappingsByEmail(db, email);
      console.log('accountMappingDocs %j', accountMappingDocs);

      if (accountMappingDocs.length && accountMappingDocs.some(doc => isEmailAddedToSameShop(doc, shopDoc))) {
        throw new Error(`The email ${email} is already added to this shop!`);
      }

      const randomAccountMapping = await getOneAccountMappingByChargeflowId(db, shopDoc.chargeflow_id.toString());

      if (!randomAccountMapping) {
        throw new Error("Not one account mapping found!");
      }

      if (this.mode === 'dry') {
        console.log(
          `Would have added user with email ${email} to shop ${shopName}`
        );
        console.log("Dry run mode ended");
        return;
      }

      console.log("Creating account mapping");
      await createAccountMapping(
        db,
        randomAccountMapping.accountId, // IMPORATNT: this is different from the `account` collection
        shopDoc.chargeflow_id,
        email
      );

      console.log(`User with email ${email} added to shop ${shopName}`);

      const cognitoRes = await createCognitoUserIfNotExists(
        idp,
        email,
        userFullName,
        shopDoc.chargeflow_id.toString()
      );

      if (!cognitoRes.cognitoUsername) {
        throw new Error("Cognito username is missing!");
      }

      await createNewCompanyIfNotExists(
        db,
        cognitoRes.cognitoUsername,
        userFullName,
        email,
        shopDoc.chargeflow_id,
        shopDoc.accountId,
        mainShopCompaniesDoc.users.billing?.stripeCustomer?.paymentMethodId,
      );

      const businessUnit = await getBusinessUnit(
        db, shopDoc.chargeflow_id.toString(), shopDoc.accountId.toString(),
      );

      await this.publishUserAddedEvent(
        email, shopDoc.accountId.toString(), businessUnit._id.toString(), userFullName, cognitoRes.cognitoUsername, shopDoc.chargeflow_id.toString()
      );

    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }
}

const script = new Script();
script.main();

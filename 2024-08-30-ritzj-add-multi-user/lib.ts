import { from<PERSON>ni } from "@aws-sdk/credential-provider-ini";
import { Db, ObjectId } from "mongodb";
import {
  AdminUpdateUserAttributesCommand,
  AdminCreateUserCommand,
  InitiateAuthCommand,
  AdminRespondToAuthChallengeCommand,
  ListUsersCommand,
  CognitoIdentityProviderClient,
} from "@aws-sdk/client-cognito-identity-provider";

export const getParamsOrThrow = (): {
  email: string;
  chargeflowId: string;
  userFullName: string;
  awsProfile: string;
} => {
  const args = process.argv.slice(2);
  console.log('args found %j', args);
  const [_mode, chargeflowId, email, userFullName, awsProfile] = args;
  if (!email || !chargeflowId || !userFullName || !awsProfile) {
    console.error(
      "Please provide chargeflowId, email, user full name, and aws profile"
    );
    throw new Error(
      'Run example: ts-node script.ts dry "chargeflowId" "email" "userFullName" "awsProfile"'
    )
  }

  return { chargeflowId, email, userFullName, awsProfile };
};

export const isEnvVarsOkOrThrow = () => {
  const requiredEnvVars = [
    "MONGO_URI",
    "MONGO_DB_NAME",
    "AWS_MERCHANT_POOL_ID",
    "AWS_MERCHANT_POOL_CLIENT_ID",
    "COGNITO_SECRET",
    "AWS_REGION",
    "USERS_EVENT_BUS_NAME",
  ];
  if (!requiredEnvVars.every((envVar) => process.env[envVar])) {
    throw new Error(
      `Missing required environment variables: ${requiredEnvVars.join(", ")}`
    );
  }
};

export const logAwsProfile = (awsProfile: string) => {
  console.log({
    region: process.env.AWS_REGION,
    credentials: fromIni({
      profile: awsProfile,
    }),
  });
};

export const getShopByChargeflowIdOrThrow = async (db: Db, chargeflowId: string) => {
  console.log("getShopByChargeflowIdOrThrow %j", {
    chargeflowId,
  });
  const shop = await db.collection("shops").findOne({ chargeflow_id: new ObjectId(chargeflowId) });
  if (!shop) {
    throw new Error(`No shop found with name ${chargeflowId}`);
  }
  return shop;
};

export const getCompaniesDocByEmailOrThrow = async (
  db: Db,
  email: string,
  chargeflowId: string
) => {
  console.log("getCompaniesDocByEmailOrThrow %j", {
    email,
    chargeflowId,
  });
  const companiesDoc = await db
    .collection("companies")
    .findOne({ $or: [
      {"users.email": email, "users.chargeflowId": chargeflowId},
      {"users.chargeflowId": chargeflowId}
    ] });
  if (!companiesDoc) {
    throw new Error(
      `No company found with email ${email} and chargeflowId ${chargeflowId}`
    );
  }
  return companiesDoc;
};

export const getAccountMappingsByEmail = async (db: Db, email: string) => {
  console.log("getAccountMappingsByEmail %j", {
    email,
  });
  const accountMapping = await db
    .collection("accountMappings")
    .find({ email }).toArray();

  console.log("accountMapping %j", accountMapping);
  return accountMapping;
};

export const isEmailAddedToSameShop = (accountMappingDoc: any, shopDoc: any) => {
  console.log("isEmailAddedToSameShop %j", {
    accountMappingDoc: {
      chargeflowId: accountMappingDoc?.chargeflowId.toString(),
    },
    shopDoc: {
      chargeflow_id: shopDoc.chargeflow_id.toString(),
    },
  });
  return (
    accountMappingDoc &&
    accountMappingDoc.chargeflowId.toString() ===
      shopDoc.chargeflow_id.toString()
  );
};

export const getOneAccountMappingByChargeflowId = async (
  db: Db,
  chargeflowId: string
) => {
  console.log("getOneAccountMappingByChargeflowId %j", {
    chargeflowId,
  });
  const accountMapping = await db.collection("accountMappings").findOne({
    chargeflowId: new ObjectId(chargeflowId),
  });

  console.log("accountMapping %j", accountMapping);
  return accountMapping;
}

export const getOrCreateAccountByShopName = async (
  db: Db,
  shopName: string
) => {
  console.log("getOrCreateAccountByShopName %j", {
    shopName,
  });
  const accountCriteria = { name: shopName };

  console.log("accountCriteria %j", accountCriteria);

  let account: any = {
    name: shopName,
    status: "active",
    dateCreated: new Date(),
    dateUpdated: new Date(),
  };

  console.log("account %j", account);

  const res = await db
    .collection("accounts")
    .updateOne(accountCriteria, { $setOnInsert: account }, { upsert: true });

  console.log("res %j", res);

  if (res.upsertedCount && res.upsertedId) {
    console.log("account upserted", res.upsertedId);
    account._id = res.upsertedId;
  } else {
    console.log("account not upserted. Finding account");
    account = await db.collection("accounts").findOne(accountCriteria);
  }

  console.log("account %j", account);

  return account;
};

export const getBusinessUnit = async (db: Db, chargeflowId: string, accountId: string) => {
  const bu = await db.collection("businessUnits").findOne({
    chargeflowId: new ObjectId(chargeflowId), accountId: new ObjectId(accountId),
  });
  if (!bu) {
    throw new Error(`No business unit found.`);
  }
  return bu;
};

export const createAccountMapping = async (
  db: Db,
  accountId: ObjectId,
  chargeflowId: ObjectId,
  email: string
) => {
  console.info("createAccountMapping", {
    accountId,
    chargeflowId,
    email,
  });
  const uniqueCriteria = {
    accountId,
    email,
  };

  console.info("uniqueCriteria", uniqueCriteria);

  let accountMapping: any = {
    ...uniqueCriteria,
    chargeflowId,
    dateCreated: new Date(),
  };
  console.info("accountMapping", accountMapping);

  const res = await db.collection("accountMappings").updateOne(
    uniqueCriteria,
    {
      $setOnInsert: accountMapping,
      $set: { dateUpdated: new Date() },
    },
    { upsert: true }
  );

  console.log("createAccountMapping %j", res);

  if (res.upsertedCount && res.upsertedId) {
    console.log("account mapping upserted", res.upsertedId);
    accountMapping._id = res.upsertedId;
  } else {
    console.log("account mapping not upserted. Finding account mapping");
    accountMapping = await db
      .collection("accountMappings")
      .findOne(uniqueCriteria);
  }

  console.log("accountMapping %j", accountMapping);

  return accountMapping;
};

export const createCognitoUserIfNotExists = async (
  idp: CognitoIdentityProviderClient,
  email: string,
  name: string,
  chargeflowId: string
): Promise<{
  cognitoUsername: string;
  password?: string;
  isCreated: boolean;
}> => {
  console.log("createCognitoUserIfNotExists", {
    email,
    name,
    chargeflowId,
  });

  // try to create the user
  // if the user already exists, ignore
  // otherwise, create
  try {
    let finalPassword = generatePassword();

    const res = await idp.send(
      new AdminCreateUserCommand({
        MessageAction: "SUPPRESS",
        UserPoolId: process.env.AWS_MERCHANT_POOL_ID,
        Username: email,
        UserAttributes: [
          { Name: "email", Value: email },
          { Name: "name", Value: name },
          { Name: "custom:chargeflowId", Value: chargeflowId },
        ],
        TemporaryPassword: finalPassword,
      })
    );

    const cognitoUsername = res.User?.Username;
    console.log("cognitoUsername", "a", cognitoUsername);

    // mark as email verified
    await idp.send(
      new AdminUpdateUserAttributesCommand({
        UserPoolId: process.env.AWS_MERCHANT_POOL_ID,
        Username: email,
        UserAttributes: [
          {
            Name: "email_verified",
            Value: "true",
          },
        ],
      })
    );

    const response = await idp.send(
      new InitiateAuthCommand({
        AuthFlow: "USER_PASSWORD_AUTH",
        AuthParameters: {
          USERNAME: email,
          PASSWORD: finalPassword,
        },
        ClientId: process.env.AWS_MERCHANT_POOL_CLIENT_ID,
        ClientMetadata: {
          cognitoSecret: process.env.COGNITO_SECRET as unknown as string,
        },
      })
    );

    console.log("response?.ChallengeName", response?.ChallengeName);

    if (response?.ChallengeName === "NEW_PASSWORD_REQUIRED") {
      console.log('Overriding NEW_PASSWORD_REQUIRED so user does not have to change password. Webapp does NOT support this flow.');
      finalPassword = generatePassword();
      console.log("finalPassword", "b", finalPassword);

      await idp.send(
        new AdminRespondToAuthChallengeCommand({
          UserPoolId: process.env.AWS_MERCHANT_POOL_ID,
          ClientId: process.env.AWS_MERCHANT_POOL_CLIENT_ID,
          ChallengeName: "NEW_PASSWORD_REQUIRED",
          ChallengeResponses: {
            USERNAME: email,
            NEW_PASSWORD: finalPassword,
          },
          Session: response.Session,
        })
      );
    }

    if (!cognitoUsername) {
      throw new Error(`Cognito username not found for email ${email}`);
    }

    const finalResponse = { cognitoUsername, password: finalPassword, isCreated: true };
    console.log("res", finalResponse);
    return finalResponse;
  } catch (err: any) {
    if (err.__type !== "UsernameExistsException") {
      console.log("err", err.code, err.message);
      throw err;
    }

    console.log(`User with email ${email} already exists in cognito`);

    // find user in cognito
    const users = await idp.send(
      new ListUsersCommand({
        UserPoolId: process.env.AWS_MERCHANT_POOL_ID,
        Filter: `email = "${email}"`,
      })
    );

    if (!users.Users?.length) {
      throw new Error(`User with email ${email} not found again in cognito`);
    }

    console.log(`users.Users[0] %j`, users.Users)
    const cognitoUsername = users.Users[0].Username;
    if (!cognitoUsername) {
      throw new Error(`Cognito username not found for email ${email}`);
    }

    console.log("cognitoUsername", "c", cognitoUsername);
    return { cognitoUsername, isCreated: false };
  }
};

export const createNewCompanyIfNotExists = async (
  db: Db,
  cognitoUsername: string,
  fullName: string,
  email: string,
  chargeflowId: ObjectId,
  accountId: ObjectId,
  stripePaymentMethodId: string
) => {
  console.log("createNewCompanyIfNotExists %j", {
    cognitoUsername,
    fullName,
    email,
    chargeflowId,
    stripePaymentMethodId,
  });
  // ensure that the company does not already exist
  // if it does, ignore
  const co = await db.collection("companies").findOne({
    "users.email": email,
    "users.chargeflowId": chargeflowId,
  });

  if (co) {
    console.log(
      `Company with email ${email} and chargeflowId ${chargeflowId} already exists`
    );
    return co._id;
  }

  const fullNameSplit = fullName.split(" ");
  const firstName = fullNameSplit?.[0];
  const lastName = fullNameSplit?.slice(1).join(" ");
  const input = {
    cognitoUsername,
    users: {
      dateCreated: new Date(),
      dateUpdated: new Date(),
      status: "active",
      onboardingStageV1: "Completed",
      onboardingStageDate: new Date(),
      email,
      firstName,
      lastName,
      contactInformation: {
        contactName: fullName,
        contactEmail: email,
      },
      businessInformation: {},
      billing: {
        stripeCustomer: {
          paymentMethodId: stripePaymentMethodId,
        },
      },
      integrations: {},
      chargeflowId,
      accountId,
    },
  };

  console.log("inserting company %j", input);

  const response = await db.collection("companies").insertOne(input);
  console.log("new companies created %j", response);
  return response.insertedId;
};

export const generatePassword = () => {
  const symbols = "!@#$%^&*()_+-=[]{}|'";
  const rand = (size: number) => Math.floor(Math.random() * size);
  const randSymbol = () => symbols[rand(symbols.length)];
  const randNumber = () => Math.floor(Math.random() * 10);
  const randLowerCase = () => String.fromCharCode(rand(26) + 97);
  const randUpperCase = () => String.fromCharCode(rand(26) + 65);
  const password = Math.random().toString(36).slice(2).split("");
  for (let i = 0; i < 3; i++) {
    password.splice(rand(password.length), 0, randSymbol());
  }
  password.splice(rand(password.length), 0, randNumber().toString());
  password.splice(rand(password.length), 0, randLowerCase());
  password.splice(rand(password.length), 0, randUpperCase());
  return password.join("");
};

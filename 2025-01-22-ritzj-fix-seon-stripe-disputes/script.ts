import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { Db, ObjectId } from "mongodb";
import AWS from "aws-sdk";
import { Stripe } from 'stripe';
import { SendMessageCommand, SQSClient } from "@aws-sdk/client-sqs";
import crypto from 'crypto';
import { mapStripeDisputeToUdo } from './mapper';
dotenv.config();

export class Script extends ScriptBase {

  public db!: Db;
  public getName(): string {
    return "Update existing stripe disputes";
  }

  public getAuthor(): string {
    return "Ritz Jumola";
  }

  public getTicket(): string {
    return "CHA-7941";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  getParamsOrThrow(params: string[]): Record<string, string> {
    const [_mode, ...args] = process.argv.slice(2);

    const res: Record<string, string> = {};
    args.forEach((arg, idx) => {
      res[params[idx]] = arg;
    });

    return res;
  }

  async processorsFindOneChargeflowIdByAccountId(chargeflowId: string, stripeAccountId: string) {
    console.log('processorsFindOneChargeflowIdByAccountId', chargeflowId, stripeAccountId);
    if (!stripeAccountId) {
        throw new Error('Missing stripeAccountId');
    }

    return this.db
        .collection('processors')
        .findOne({
          'processor_name': 'stripe',
          'chargeflow_id': new ObjectId(chargeflowId),
          'connect.stripe_user_id': stripeAccountId })
        .then(res => {
            return {
                chargeflowId: res?.chargeflow_id,
                processorId: res?._id,
                status: res?.status,
            };
        })
  }

  async disputesFindOneDisputeByCaseId(chargeflowId: string, disputeId: string) {
    if (!chargeflowId) {
        throw new Error('Missing chargeflowId');
    }

    if (!disputeId) {
        throw new Error('Missing disputeId');
    }

    return await this.db.collection('disputes')
        .findOne({
            'chargeflowId': new ObjectId(chargeflowId),
            'dispute.id': disputeId,
        })
  }

  async shopsFindOneShopByChargeflowId(chargeflowId: string) {
    if (!chargeflowId) {
        throw new Error('Missing chargeflowId');
    }

    return await this.db.collection('shops')
        .findOne({ 'chargeflow_id': new ObjectId(chargeflowId) });
  }

  getStripeClient(stripeAccountId: string) {
    return new Stripe(process.env.STRIPE_SECRET!, { apiVersion: '2024-12-18.acacia', stripeAccount: stripeAccountId });
  }

  async getStripeDispute(stripeAccountId: string, disputeId: string) {
    const stripeClient = this.getStripeClient(stripeAccountId);
    return await stripeClient.disputes.retrieve(disputeId, { stripeAccount: stripeAccountId });
  }

  async getStripePaymentIntent(stripeAccountId: string, paymentIntentId: string) {
    const stripeClient = this.getStripeClient(stripeAccountId);
    return await stripeClient.paymentIntents
            .retrieve(paymentIntentId,
                { expand: [ 'latest_charge' ] },
                { stripeAccount: stripeAccountId },
            );
  }

  async getStripeCustomer(stripeAccountId: string, customerId: string) {
    const stripeClient = this.getStripeClient(stripeAccountId);
    return await stripeClient.customers.retrieve(customerId, { stripeAccount: stripeAccountId });
  }

  async getStripeCharge(stripeAccountId: string, chargeId: string) {
    const stripeClient = this.getStripeClient(stripeAccountId);
    return await stripeClient.charges.retrieve(chargeId, { stripeAccount: stripeAccountId });
  }

  async updateMongoDispute(mongoId: string, dispute: any) {
    return await this.db.collection('disputes')
        .updateOne({ _id: new ObjectId(mongoId) }, { $set: dispute });
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow([
        "MONGO_URI",
        "MONGO_DB_NAME",
        "STRIPE_SECRET",
        "AWS_SQS_DISPUTES_URL",
        "AWS_SQS_SENDGRID_NEW_DISPUTE_URL",
        "ENV_TYPE"
      ]);

      const params = this.getParamsOrThrow(['chargeflowId', 'stripeAccountId', 'disputeId']);

      console.log("params are ok %", params);

      const mongo = await loadMongoClient();
      console.log("Mongo client loaded");

      this.db = mongo.db(process.env.MONGO_DB_NAME);

      const { chargeflowId, disputeId, stripeAccountId } = params;

      const { processorId } = await
        this.processorsFindOneChargeflowIdByAccountId(chargeflowId, stripeAccountId);

      if (!chargeflowId) {
        throw new Error('Missing chargeflowId');
      }

      if (!processorId) {
          throw new Error('Missing processorId');
      }

      const isDisputeExists = await this.disputesFindOneDisputeByCaseId(chargeflowId, disputeId);

      if (!isDisputeExists) {
          throw new Error('Dispute not found');
      }

      const shopData = await this.shopsFindOneShopByChargeflowId(chargeflowId);

      const shopName = shopData?.name;
      if (!shopName) {
          throw new Error('Missing shopName');
      }

      // get stripe dispute
      const dispute = await this.getStripeDispute(stripeAccountId, disputeId);

      const paymentIntentId = dispute?.payment_intent;
      const paymentIntent = paymentIntentId
          ? await this.getStripePaymentIntent(stripeAccountId, paymentIntentId as string)
              .catch((err) => {
                  console.error(`Error while fetching paymentIntent: ${err}`);
                  return null;
              })
          : undefined;

      const chargeId = dispute?.charge;
      const charge = chargeId ? await this.getStripeCharge(stripeAccountId, chargeId as string)
          .catch((err) => {
              console.error(`Error while fetching charge: ${err}`);
              return null;
          }) : undefined;

      const customerId = paymentIntent?.customer ?? charge?.customer;

      const customer = customerId
          ? await this.getStripeCustomer(stripeAccountId, customerId as string)
              .catch((err) => {
                  console.error(`Error while fetching customer: ${err}`);
                  return null;
              })
          : undefined;

      (dispute as any)['payment_intent'] = paymentIntent ? {
          ...paymentIntent,
          customer,
      } : undefined;
      (dispute as any).charge = charge ? {
          ...charge,
          customer,
      } : undefined;

      const mappedDispute = mapStripeDisputeToUdo(
          chargeflowId,
          processorId,
          dispute,
          shopData,
          false,
      );

      console.log(JSON.stringify(mappedDispute));


      if (isDryRun) {
        console.log("Dry run mode enabled, no changes made to the database");
        return;
      }

      await this.updateMongoDispute(isDisputeExists._id.toString(), {
        ...mappedDispute,
        reInsertedStripe: true,
      });

      const sqsClient = new SQSClient();

      // send sqs to AWS_SQS_SENDGRID_NEW_DISPUTE_URL
      const cmd1 = new SendMessageCommand({
          MessageAttributes: {
              Key: {
                  DataType: 'String',
                  StringValue: crypto.randomUUID(),
              },
          },
          MessageBody: JSON.stringify({ shopId: shopData._id, disputeId }),
          QueueUrl: process.env.AWS_SQS_SENDGRID_NEW_DISPUTE_URL,
      });
      const cmd1Res = await sqsClient.send(cmd1);
      console.log('cmd1Res %j', cmd1Res);

      // send sqs to AWS_SQS_DISPUTES_URL
      const cmd2 = new SendMessageCommand({
          MessageAttributes: {
              shopName: {
                  DataType: 'String',
                  StringValue: shopName,
              },
              Key: {
                  DataType: 'String',
                  StringValue: crypto.randomUUID(),
              },
          },
          MessageBody: JSON.stringify([isDisputeExists._id.toString()]),
          QueueUrl: process.env.AWS_SQS_DISPUTES_URL,
      });
      const cmd2Res = await sqsClient.send(cmd2);
      console.log('cmd2Res %j', cmd2Res);

      // publish DISPUTE_INGESTED
      const eventbridge = new AWS.EventBridge();
      const disputeIngestedRes = await eventbridge.putEvents({
        Entries: [
            {
                Detail: JSON.stringify({
                  disputeId: isDisputeExists._id,
                  chargeflowId: isDisputeExists.chargeflowId,
                  processorName: isDisputeExists.dispute?.processor,
                  disputePropertiesForLinking: {
                      transactionOrderId: isDisputeExists.transaction?.orderId,
                      transactionId: isDisputeExists.transaction?.id,
                      transactionDate: isDisputeExists.transaction?.dateCreated,
                  }
                }),
                DetailType: 'DISPUTE_INGESTED',
                Source: process.env.ENV_TYPE ? 'dev-stripe-webhooks' : 'prod-stripe-webhooks',
                EventBusName: process.env.ENV_TYPE ? 'dev-disputes-service' : 'prod-disputes-service',
            },
        ],
      }).promise();

      console.log('disputeIngestedRes %j', disputeIngestedRes);

    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }
}

const script = new Script();
script.main();

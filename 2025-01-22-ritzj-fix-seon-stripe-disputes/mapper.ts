'use strict';
const { ObjectId } = require('mongodb');
const { CurrencyUtil } = require('@chargeflow-team/chargeflow-utils-sdk');

const stage = [ 'warning_needs_response', 'warning_under_review', 'warning_closed', 'warning_won' ];

const hasActions = [ 'warning_needs_response', 'needs_response' ];

const actions = [
    {
        'dateCreated': new Date(),
        'actionName': 'accept_claim',
    },
    {
        'dateCreated': new Date(),
        'actionName': 'provide_evidence',
    },
];

const statusConvertor = {
    'warning_needs_response': 'warning_needs_response',
    'warning_under_review': 'warning_under_review',
    'warning_closed': 'warning_won',
    'needs_response': 'needs_response',
    'under_review': 'under_review',
    'charge_refunded': 'refunded',
    'protected': 'insured',
    'lost': 'lost',
    'won': 'won',
    'warning_won': 'won',
};

const reasonConvertor = {
    'bank_cannot_process': 'bank_cannot_process',
    'check_returned': 'check_returned',
    'credit_not_processed': 'credit_not_processed',
    'customer_initiated': 'customer_initiated',
    'debit_not_authorized': 'unauthorized',
    'duplicate': 'duplicate_charge',
    'general': 'general',
    'incorrect_account_details': 'incorrect_account_details',
    'insufficient_funds': 'insufficient_funds',
    'product_not_received': 'not_received',
    'product_unacceptable': 'not_as_described',
    'subscription_canceled': 'canceled_recurring_billing',
    'unrecognized': 'unrecognized',
    'fraudulent': 'fraud',
};

const shopify3DToChargeflowResult = {
    'authenticated': 'pass',
    'failed': 'failed',
    'attempt_acknowledged':'other',
    'exempted': 'other',
    'not_supported': 'other',
    'processing_error': 'other',
};

const wonStatuses = [ 'won', 'warning_won' ];

function capitalizeWords(words?: any) {
    return words?.split(' ')
        ?.filter(Boolean)
        .map((word: any) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
}

export function mapStripeDisputeToUdo(
    chargeflowId: any, processorId: any, dispute: any, shopData: any, isProcessorDeactivated = false,
) {

    if (!chargeflowId) {
        throw new Error('Missing chargeflowId');
    }

    if (!dispute) {
        throw new Error('Missing dispute');
    }

    if (!shopData) {
        throw new Error('Missing shopData');
    }

    const payment = dispute.payment_intent ?? dispute.charge;
    if (!payment) {
        console.warn(
            'Failed to find Stripe payment for dispute: %j, shop name: %s',
            dispute,
            shopData.name,
        );
    }

    const paymentIntentCharge: any = payment?.latest_charge || payment;
    const paymentIntentPaymentDetails: any = paymentIntentCharge?.payment_method_details;
    const paymentIntentBillingDetails: any = paymentIntentCharge?.billing_details;
    const mappedStatus: any = (statusConvertor as any)[dispute.status];

    const customer: any = extractCustomerInfo({ stripeDispute: dispute });

    const stripeProcessorName: any = 'stripe';
    const disputeSourceAmount: any = Number(dispute.amount || null);
    const disputeFeeSourceAmount: any = Number(dispute.balance_transactions?.[0]?.fee || null);
    const transactionSourceAmount: any = Number(payment?.amount || null);
    const disputeCurrency: any = dispute.currency?.toUpperCase() || null;
    const paymentCurrency: any = payment?.currency?.toUpperCase() || null;
    const disputeAmount: any = CurrencyUtil.normalizeToMajorUnits(disputeSourceAmount, disputeCurrency, stripeProcessorName);
    const disputeFee: any = CurrencyUtil.normalizeToMajorUnits(disputeFeeSourceAmount, disputeCurrency, stripeProcessorName);
    const transactionAmount: any =
        CurrencyUtil.normalizeToMajorUnits(transactionSourceAmount, paymentCurrency, stripeProcessorName);
    const amountWon: any = wonStatuses.includes(dispute.status)? disputeAmount : null;

    const newDisputeStatus: any = {
        'dateCreated': new Date(),
        'statusDate': null,
        'status': mappedStatus || null,
        'processorStatus': dispute.status || null,
        'source': 'webhook',
    };

    const last4 = paymentIntentPaymentDetails?.card?.wallet?.['apple_pay'] ?
        paymentIntentPaymentDetails.card.wallet['dynamic_last4'] :
        paymentIntentPaymentDetails?.card?.last4;

    const cvcCheck = paymentIntentPaymentDetails?.card?.checks?.cvc_check;

    const chargeflowDispute = {
        'dateCreated': new Date(),
        'dateUpdated': null,
        'shopName': shopData.name,
        'shopId': shopData._id,
        'chargeflowId': new ObjectId(chargeflowId),
        'accountId': shopData.accountId && new ObjectId(shopData.accountId),
        'liveMode': false,
        'lastDisputeStatus': newDisputeStatus,
        'dispute': {
            'id': dispute.id,
            'processorId': processorId.toString(),
            'dateReceived': new Date(),
            'dateCreated': new Date(dispute.created * 1000),
            'dateUpdated': null,
            'processor': 'stripe',
            'source': 'stripe',
            'stage': stage.includes(dispute.status)
                ? 'Inquiry'
                : 'Chargeback',
            'transactionId': payment?.id,
            'amount': disputeAmount,
            'currency': disputeCurrency,
            'reason': (reasonConvertor as any)[dispute.reason] || null,
            'processorReason': dispute.reason || null,
            'reasonCode': null,
            'disputeStatuses': [
                newDisputeStatus,
            ],
            'responseDue': new Date(dispute.evidence_details?.due_by * 1000),
            'closedDate': null,
            'submittedCount': Number(dispute.evidence_details?.submission_count || null),
            'disputeFee': disputeFee,
            'statementDescriptor': payment?.statement_descriptor || null,
            'mid': null,
            'isRefundable': dispute.is_charge_refundable,
            'intentionalFraud': false,
            'actions': hasActions.includes(dispute.status) ? actions : [],
            'rawData': {
                'dispute': dispute,
                'webhooks': [],
            },
        },
        'transaction': {
            'id': payment?.id,
            'type': payment?.latest_charge?.outcome?.type || null,
            'dateReceived': new Date(),
            'dateCreated': payment?.created
                ? new Date(payment?.created * 1000)
                : null,
            'dateUpdated': null,
            'amount': transactionAmount,
            'currency': paymentCurrency,
            'source': 'stripe',
            'receiptUrl': paymentIntentCharge?.receipt_url || null,
            'checks': {
                'cvcCheck': {
                    'code': cvcCheck === 'pass' ? 'M' : null,
                },
                'addressLine1Check':
                    paymentIntentPaymentDetails?.card?.checks?.address_line1_check || null,
                'addressZipCheck':
                    paymentIntentPaymentDetails?.card?.checks?.address_postal_code_check || null,
            },
            'billing': {
                'name': paymentIntentBillingDetails?.name || null,
                'city': paymentIntentBillingDetails?.address?.city || null,
                'state': paymentIntentBillingDetails?.address?.state || null,
                'country': paymentIntentBillingDetails?.address?.country || null,
                'line1': paymentIntentBillingDetails?.address?.line1 || null,
                'line2': paymentIntentBillingDetails?.address?.line2 || null,
                'zip': paymentIntentBillingDetails?.address?.postal_code || null,
                'phone': paymentIntentBillingDetails?.phone || null,
            },
            'customerName': customer?.name ? capitalizeWords(customer.name) : null,
            'customerEmail': customer?.email ? capitalizeWords(customer.email) : null,
            'orderId': paymentIntentCharge?.metadata?.order_id || null,
            'risk': {
                'riskLevel': paymentIntentCharge?.outcome?.risk_level || null,
                'riskScore': Number(paymentIntentCharge?.outcome?.risk_score || null),
            },
            'statementDescriptor': payment?.statement_descriptor || null,
            'paymentDetails': {
                'cardBrand': paymentIntentPaymentDetails?.card?.brand || null,
                'country': paymentIntentPaymentDetails?.card?.country || null,
                'expMonth': Number(paymentIntentPaymentDetails?.card?.exp_month),
                'expYear': Number(paymentIntentPaymentDetails?.card?.exp_year),
                'installments': paymentIntentPaymentDetails?.card?.installments || null,
                'network': paymentIntentPaymentDetails?.card?.network || null,
                '3dSecure': {
                    'authenticationFlow': paymentIntentPaymentDetails?.card?.
                        three_d_secure?.authentication_flow || null,
                    'authenticated':
                        paymentIntentPaymentDetails?.card?.three_d_secure?.authenticated || null,
                    'result':
                        (shopify3DToChargeflowResult as any)[paymentIntentPaymentDetails?.card?.
                            three_d_secure?.result] || null,
                    'resultReason': paymentIntentPaymentDetails?.card?.
                        three_d_secure?.result_reason || null,
                    'version': paymentIntentPaymentDetails?.card?.
                        three_d_secure?.version || null,
                },
                'last4': last4 || null,
                'fingerprint': paymentIntentPaymentDetails?.card?.fingerprint || null,
            },
            'rawData': {
                'transaction': payment,
                'webhooks': [],
            },
        },
        'shippingData': [
            {
                'trackingNumber': null,
                'dateCreated':null,
                'dateUpdated': null,
                'source': null,
                'courier': null,
                'status': null,
                'statusDescription': null,
                'deliveryTime': null,
                'destinationCountry': null,
                'originCountry': null,
                'signedBy': null,
                'subtagMessage': null,
                'deliveryType': null,
                'firstAttemptedAt': null,
                'courierTrackingLink': null,
                'onTimeStatus': null,
                'checkpoints': [],
                'rawData': {
                    'shippingData': {},
                    'webhooks': [],
                },
            },
        ],
        'chargeflow': {
            'templates': [],
            'evidences': [],
            'cfActions': {
                'handleByChargeflow': isProcessorDeactivated ? false : ![
                    'warning_under_review',
                    'under_review',
                    'lost',
                    'won',
                    'warning_won',
                    'insured',
                ].includes(mappedStatus),
                'chargeflowPerformedAction': false,
            },
            'resolved': {
                'resolvedDate': null,
                'resolveId': null,
                'isResolved': [ 'lost', 'won' ].includes(dispute.status),
                'isWon': wonStatuses.includes(dispute.status),
                'amountWon': amountWon,
                'successFee': null,
            },
            'chargeScore': {
                score: 0,
                parameters: [],
                history: [],
            },
            submitted:{
                submittedAt: null,
                amountInUSD: null,
            },
        },
        'extensions': {},
    };

    return chargeflowDispute;
}

function extractCustomerInfo({ stripeDispute }: any) {
    const customer = stripeDispute.payment_intent?.customer || stripeDispute.charge?.customer;

    if (!customer || !customer?.id) {
        console.warn(
            `No customer ID found for dispute: ${JSON.stringify(stripeDispute)}`,
        );
        return undefined;
    }

    return { name: customer.name, email: customer.email };
}

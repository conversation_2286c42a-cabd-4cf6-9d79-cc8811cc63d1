# Purpose:
The purpose of this script is to change the email of a shop (and all relevant documents) in the database to a new email address.

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> <chargeflowId> <newEmail> <newUserFullName>`
NOTE: a `.env` file is expected to be present in CWD with

**MONGO_URI**
**MONGO_DB_NAME**
**MERCHANT_POOL_ID**
**MERCHANT_POOL_CLIENT_ID**
**AWS_COGNITO_SECRET**

See `.env.example` for an example.

### How this script was tested
[x] Wet run against dev & prod with data diff validation afterwards
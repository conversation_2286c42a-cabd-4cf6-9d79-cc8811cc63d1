import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { Db, ObjectId } from "mongodb";
import AWS from "aws-sdk";

dotenv.config();

export class Script extends ScriptBase {
  public getName(): string {
    return "Changing a shop's email address";
  }

  public getAuthor(): string {
    return "Ritz Jumola";
  }

  public getTicket(): string {
    return "CHA-7941";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow([
        "MONGO_URI",
        "MONGO_DB_NAME",
        "MERCHANT_POOL_ID",
        "MERCHANT_POOL_CLIENT_ID",
        "AWS_COGNITO_SECRET",
      ]);

      const { chargeflowId, newEmail, newUserFullName } = {
        chargeflowId: ctx.args[1],
        newEmail: ctx.args[2],
        newUserFullName: ctx.args[3],
      };

      console.log("Arguments", { chargeflowId, newEmail, newUserFullName });

      if (!chargeflowId || !newEmail || !newUserFullName) {
        throw new Error(
          "Missing required arguments <chargeflowId> <newEmail> <newUserFullName>"
        );
      }

      const mongo = await loadMongoClient();
      console.log("Mongo client loaded");

      const db = mongo.db(process.env.MONGO_DB_NAME);

      const {
        shopDoc,
        chargeflowIdDoc,
        companyDoc,
        customerDoc,
        settingsDoc,
        accountDoc,
        accountMappingDoc,
      } = await this.getExistingDataOrThrow(db, chargeflowId);

      console.log("Existing data found");

      if (this.mode === 'dry') {
        console.log("Dry run mode, not changing anything. Script ends here!");
        return;
      }

      const idp = new AWS.CognitoIdentityServiceProvider({
        region: "us-east-1",
      });

      const OLD_EMAIL = shopDoc.email;

      if (this.mode === "wet-primary") {
        const creds = await (async () => {
          try {
            const createRes = await this.createCognitoUser(
              idp,
              newEmail,
              newUserFullName,
              shopDoc.chargeflow_id.toString(),
              shopDoc.platform,
              shopDoc.connect?.access_token,
              shopDoc.name
            );

            console.log("Created cognito user %j", createRes);
            return {
              cognitoId: createRes.cognitoId,
              email: createRes.email,
              password: createRes.password,
              isNew: true,
            }
          } catch (err: any) {
            // if the user already exists, we need to update the attributes
            // and return the existing user
            if (err.code === "UsernameExistsException") {
              console.log("User already exists, updating attributes");
              // update first, update custom:chargeflowId
              const updateRes = await idp
                .adminUpdateUserAttributes({
                  UserPoolId: process.env.MERCHANT_POOL_ID as string,
                  Username: newEmail,
                  UserAttributes: [
                    {
                      Name: "custom:chargeflowId",
                      Value: shopDoc.chargeflow_id.toString(),
                    },
                  ],
                })
                .promise();
              console.log("Updated cognito user %j", updateRes);

              // get the existing user
              const userRes = await idp
                .adminGetUser({
                  UserPoolId: process.env.MERCHANT_POOL_ID as string,
                  Username: newEmail,
                })
                .promise();

              console.log("Got cognito user %j", userRes);

              return {
                cognitoId: userRes.Username,
                email: newEmail,
                password: undefined,
                isNew: false,
              }

            } else {
              console.log("Error creating cognito user %j", err);
              throw err;
            }
          }
        })();

        if (!creds?.cognitoId) {
          throw new Error("Cognito user not created");
        }

        await this.runDbUpdates(
          db,
          creds.cognitoId,
          newEmail,
          shopDoc,
          chargeflowIdDoc,
          companyDoc,
          customerDoc,
          settingsDoc,
          accountDoc,
          accountMappingDoc
        );

        // if there's no more account mapping left for the old email, delete the cognito user
        const accountMappingsLeftCount = await db.collection("accountMappings").countDocuments({
          email: OLD_EMAIL,
        });
        console.log(`Account mappings left for ${OLD_EMAIL}: ${accountMappingsLeftCount}`);
        if (!accountMappingsLeftCount) {
          console.log("Deleting cognito user %s", OLD_EMAIL);
          await this.deleteCognitoUser(idp, OLD_EMAIL);
        }

        if (creds.isNew) {
          console.log(
            'New user created with email "%s" and password "%s"',
            creds.email,
            creds.password
          );
        } else {
          console.log(
            'User "%s" already exists, email changed, password not changed',
            creds.email
          );
        }

      }
    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }

  private async getExistingDataOrThrow(
    db: Db,
    chargeflowId: string
  ): Promise<{
    shopDoc: any;
    chargeflowIdDoc: any;
    companyDoc: any;
    customerDoc: any;
    settingsDoc: any;
    accountDoc: any;
    accountMappingDoc: any;
  }> {
    const shops = db.collection("shops");
    const chargeflowIds = db.collection("chargeflow_ids");
    const companies = db.collection("companies");
    const customers = db.collection("customers");
    const settings = db.collection("settings");
    const account = db.collection("account");
    const accountMappings = db.collection("accountMappings");

    const shopDoc = await shops.findOne({ chargeflow_id: new ObjectId(chargeflowId) });
    if (!shopDoc) {
      throw new Error(`Shop with chargeflow id ${chargeflowId} not found`);
    }

    const chargeflowIdDoc = await chargeflowIds.findOne({
      _id: shopDoc.chargeflow_id,
    });
    if (!chargeflowIdDoc) {
      throw new Error(`Chargeflow ID ${shopDoc.chargeflow_id} not found`);
    }

    const companyDoc = await companies.findOne({
      "users.chargeflowId": shopDoc.chargeflow_id,
    });
    if (!companyDoc) {
      throw new Error(
        `Company with chargeflow ID ${shopDoc.chargeflow_id} not found`
      );
    }

    const customerDoc = await customers.findOne({ _id: shopDoc.customer_id });
    if (!customerDoc) {
      throw new Error(`Customer with ID ${shopDoc.customer_id} not found`);
    }

    const settingsDoc = await settings.findOne({
      chargeflow_id: shopDoc.chargeflow_id,
    });
    if (!settingsDoc) {
      throw new Error(
        `Settings for shop with chargeflow ID ${shopDoc.chargeflow_id} not found`
      );
    }

    const accountDoc = await account.findOne({
      "legacy.chargeflowId": shopDoc.chargeflow_id,
    });
    if (!accountDoc) {
      throw new Error(
        `Account with chargeflow ID ${shopDoc.chargeflow_id} not found`
      );
    }

    const accountMappingDoc = await accountMappings.findOne({
      chargeflowId: shopDoc.chargeflow_id,
      email: shopDoc.email,
    });
    if (!accountMappingDoc) {
      throw new Error(
        `Account mapping with chargeflow ID ${shopDoc.chargeflow_id} and email ${shopDoc.email} not found`
      );
    }

    console.log('shop doc id', shopDoc._id);
    console.log('chargeflow id doc id', chargeflowIdDoc._id);
    console.log('company doc id', companyDoc._id);
    console.log('customer doc id', customerDoc._id);
    console.log('settings doc id', settingsDoc._id);
    console.log('account doc id', accountDoc._id);
    console.log('account mapping doc id', accountMappingDoc._id);

    return {
      shopDoc,
      chargeflowIdDoc,
      companyDoc,
      customerDoc,
      settingsDoc,
      accountDoc,
      accountMappingDoc,
    };
  }

  private genPwd(): string {
    const symbols = "!@#$%^&*()_+-=[]{}|'";
    const rand = (size: number) => Math.floor(Math.random() * size);
    const randSymbol = () => symbols[rand(symbols.length)];
    const randNumber = () => Math.floor(Math.random() * 10);
    const randLowerCase = () => String.fromCharCode(rand(26) + 97);
    const randUpperCase = () => String.fromCharCode(rand(26) + 65);
    const password = Math.random().toString(36).slice(2).split("");
    for (let i = 0; i < 3; i++) {
      password.splice(rand(password.length), 0, randSymbol());
    }
    password.splice(rand(password.length), 0, randNumber().toString());
    password.splice(rand(password.length), 0, randLowerCase());
    password.splice(rand(password.length), 0, randUpperCase());
    return password.join("");
  }

  private async createCognitoUser(
    idp: AWS.CognitoIdentityServiceProvider,
    newEmail: string,
    newName: string,
    chargeflowId: string,
    shopPlatform: string,
    shopToken: string,
    shopName: string
  ) {
    const password = this.genPwd();

    let attributes = [
      { Name: "email", Value: newEmail },
      { Name: "name", Value: newName },
      { Name: "custom:chargeflowId", Value: chargeflowId },
    ];

    if (shopPlatform === "shopify") {
      const shopifyAttributes = [
        { Name: "custom:shopifyAccessToken", Value: shopToken },
        { Name: "custom:shopifyShopName", Value: shopName },
      ];
      attributes = [...attributes, ...shopifyAttributes];
    }

    const res = await idp
      .adminCreateUser({
        MessageAction: "SUPPRESS",
        UserPoolId: process.env.MERCHANT_POOL_ID as unknown as string,
        Username: newEmail,
        UserAttributes: attributes,
        TemporaryPassword: password,
      })
      .promise();

    console.log("new cognito user %j", res);

    const cognitoId = res.User?.Username;

    const response = await idp
      .initiateAuth({
        AuthFlow: "USER_PASSWORD_AUTH",
        AuthParameters: {
          USERNAME: newEmail,
          PASSWORD: password,
        },
        ClientId: process.env.MERCHANT_POOL_CLIENT_ID as unknown as string,
        ClientMetadata: {
          cognitoSecret: process.env.AWS_COGNITO_SECRET as unknown as string,
        },
      })
      .promise();

    if (response?.ChallengeName === "NEW_PASSWORD_REQUIRED") {
      await idp
        .adminRespondToAuthChallenge({
          UserPoolId: process.env.MERCHANT_POOL_ID as unknown as string,
          ClientId: process.env.MERCHANT_POOL_CLIENT_ID as unknown as string,
          ChallengeName: "NEW_PASSWORD_REQUIRED",
          ChallengeResponses: {
            USERNAME: newEmail,
            NEW_PASSWORD: password,
          },
          Session: response.Session,
        })
        .promise();
    }

    return { email: newEmail, password, cognitoId };
  }

  private async runDbUpdates(
    db: Db,
    cognitoId: string,
    newEmail: string,
    shopDoc: any,
    chargeflowIdDoc: any,
    companiesDoc: any,
    customerDoc: any,
    settingsDoc: any,
    accountDoc: any,
    accountMappingsDoc: any
  ) {
    const shops = db.collection("shops");
    const chargeflowIds = db.collection("chargeflow_ids");
    const companies = db.collection("companies");
    const customers = db.collection("customers");
    const settings = db.collection("settings");
    const account = db.collection("account");
    const accountMappings = db.collection("accountMappings");

    const updateShopsRes = await shops.updateOne(
      { _id: shopDoc._id },
      { $set: { email: newEmail } }
    );
    console.log("Updated shops %j", updateShopsRes);

    const updateCfIdRes = await chargeflowIds.updateOne(
      { _id: chargeflowIdDoc._id },
      { $set: { account_email: newEmail } }
    );
    console.log("Updated chargeflow_ids %j", updateCfIdRes);

    const updateCompaniesRes = await companies.updateOne(
      { _id: companiesDoc._id },
      {
        $set: {
          "users.email": newEmail,
          "users.contactInformaion.contactEmail": newEmail,
          cognitoUsername: cognitoId,
        },
      }
    );
    console.log("Updated companies %j", updateCompaniesRes);

    const updateCustomersRes = await customers.updateOne(
      { _id: customerDoc._id },
      { $set: { email: newEmail } }
    );
    console.log("Updated customers %j", updateCustomersRes);

    const updateSettingsRes = await settings.updateOne(
      { _id: settingsDoc._id },
      { $set: { "shop.contact_email": newEmail } }
    );
    console.log("Updated settings %j", updateSettingsRes);

    const updateAccountRes = await account.updateOne(
      { _id: accountDoc._id },
      { $set: { email: newEmail } }
    );
    console.log("Updated account %j", updateAccountRes);

    const updateAccountMappingsRes = await accountMappings.updateOne(
      { _id: accountMappingsDoc._id },
      { $set: { email: newEmail } }
    );
    console.log("Updated accountMappings %j", updateAccountMappingsRes);
  }

  private async deleteCognitoUser(
    idp: AWS.CognitoIdentityServiceProvider,
    oldEmail: string
  ) {
    const res = await idp
      .adminDeleteUser({
        UserPoolId: process.env.MERCHANT_POOL_ID as string,
        Username: oldEmail,
      })
      .promise();
    console.log("deleted cognito user %j", res);
  }
}

const script = new Script();
script.main();

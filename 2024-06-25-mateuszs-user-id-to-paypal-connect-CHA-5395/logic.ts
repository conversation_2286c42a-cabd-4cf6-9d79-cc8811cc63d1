import {MongoClient, Db, ObjectId, AnyBulkWriteOperation} from 'mongodb';
import {EnvVariables, PayPalVariables} from "./Types";

export interface Logger {
    log: (message: string) => void;
}

export class AddUserIdToPaypalConnect {
    private readonly DB_NAME = 'chargeflow_api';
    private readonly PROCESSORS_COLLECTION_NAME = 'processors';

    private db: Db

    constructor(
        mongoClient: MongoClient,
        private readonly logger: Logger,
    ) {
        this.db = mongoClient.db(this.DB_NAME);
    }

    async execute(
        envVariables: EnvVariables,
        dryRun: boolean): Promise<void> {
        try {
            this.logger.log('Adding userId for paypal connect started, please wait ...');

            const processorsFilter = {"processor_name": 'paypal', "connect.userId": { $exists: false }};
            const totalItems = await this.db.collection(this.PROCESSORS_COLLECTION_NAME).countDocuments(processorsFilter);
            const totalBatches = Math.ceil(totalItems / envVariables.batchSize);

            this.logger.log(`Total documents to process: ${totalItems}`);
            this.logger.log(`Total batches to process: ${totalBatches}`);

            for (let currentBatch = envVariables.page; currentBatch <= totalBatches; currentBatch++) {
                try {
                    this.logger.log(`Processing batch ${currentBatch} of ${totalBatches}`);
                    const currentPage = (currentBatch - 1) * envVariables.batchSize;
                    const processors = await this.db.collection(this.PROCESSORS_COLLECTION_NAME)
                        .find(processorsFilter)
                        .skip(currentPage)
                        .limit(envVariables.batchSize)
                        .toArray();
                    const results= await Promise.allSettled(processors.map(async processor => {
                        this.logger.log(`Started for processor ${processor._id}`);
                        const userId = await this.fetchUserId(
                            envVariables.payPalVars,
                            processor._id.toString(),
                            processor.connect?.access_token,
                            processor.connect?.refresh_token);
                        this.logger.log(`UserId: ${userId}, for processor ${processor._id}`);
                        return { processorId: processor._id, userId };
                    }));
                    if (dryRun) {
                        this.logger.log('Dry run mode enabled, no changes made to the database');
                        continue;
                    }
                    const updateQueries = this.getUpdateQueries(results);
                    if (updateQueries.length > 0) {
                        const result = await this.db.collection(this.PROCESSORS_COLLECTION_NAME).bulkWrite(updateQueries);
                        this.logger.log(`Updated ${result.modifiedCount} processors with userId.`);
                    } else {
                        this.logger.log('No processors to update.');
                    }
                    this.logger.log(`Batch ${currentBatch} of ${totalBatches} processed successfully`);
                } catch (error: unknown) {
                    if (error instanceof Error) {
                        this.logger.log(`Batch ${currentBatch} of ${totalBatches} failed, reason: ${error.message}`);
                    }
                    throw error;
                }
            }
            this.logger.log('Adding userId for paypal connect finished successfully');
        } catch (error) {
            if (error instanceof Error) {
                this.logger.log(`Adding userId for paypal connect failed, reason: ${error.message}`);
            }
            throw error;
        }
    }

    private getUpdateQueries(results: Array<PromiseSettledResult<Awaited<Promise<{
        processorId: Document extends {
            _id: infer IdType
        } ? (Record<any, never> extends infer IdType ? never : IdType) : (Document extends {
            _id?: infer IdType
        } ? (unknown extends infer IdType ? ObjectId : IdType) : ObjectId);
        userId: String
    }>>>>): AnyBulkWriteOperation[] {
        if (!results || results.length === 0) {
            return [];
        }
        return results.map(result => {
            if (result.status === 'fulfilled') {
                const { processorId, userId } = result.value;
                this.logger.log(`Updating userId for processor ${processorId}`);
                return {
                    updateOne: {
                        filter: { _id: new ObjectId(processorId) },
                        update: {
                            $set: {
                                'connect.userId': userId
                            }
                        },
                        upsert: false
                    }
                };
            }
            this.logger.log(`Failed to prepare update query for processor`);
            return null;
        })
            .filter(query => query !== null) as AnyBulkWriteOperation[];
    }

    private async fetchUserId(payPalVars: PayPalVariables,
                              processorId: string,
                              accessToken: string,
                              refreshToken?: string): Promise<String> {
        try {
            const url = `${payPalVars.payPalBaseUrl}/identity/openidconnect/userinfo?schema=openid`;
            const headers = {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/x-www-form-urlencoded',
            };
            const response = await fetch(url, { method: 'GET', headers: headers });
            this.logger.log(`Response: ${JSON.stringify(response.status)}, processorId: ${processorId}`);
            if (!response.ok) {
                if (response.status === 401) {
                    if (!refreshToken) {
                        this.logger.log(`Missing refreshToken for, processorId: ${processorId}`);
                        throw new Error('Missing refreshToken for the processor');
                    }
                    const encodedClientId = Buffer
                        .from(`${payPalVars.payPalClientId}:${payPalVars.payPalClientSecret}`)
                        .toString('base64');
                    const baseUrl = payPalVars.payPalBaseUrl;
                    const authUrl = `${baseUrl}/identity/openidconnect/tokenservice`;
                    const authOptions = {
                        method: 'POST',
                        headers: {
                            'Authorization': `Basic ${encodedClientId}`,
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `grant_type=refresh_token&refresh_token=${refreshToken}`,
                    };
                    const accessTokenRes = await fetch(authUrl, authOptions)
                        .then(res => res.json())
                    if (accessTokenRes.error) {
                        this.logger.log(`Error: ${accessTokenRes.error}, Error description: ${accessTokenRes.error_description}, processorId: ${processorId}`);
                        throw new Error(accessTokenRes.error || 'An error occurred while fetching data');
                    }
                    return await this.fetchUserId(payPalVars, processorId, accessTokenRes.access_token);
                }
            }
            const { user_id } = await response.json();
            return user_id;
        } catch (error: Error | any) {
            throw new Error(JSON.stringify(error));
        }
    }
}

import dotenv from "dotenv";
import { MongoClient } from "mongodb";
import { ScriptBase } from "../shared/script-base";
import {AddUserIdToPaypalConnect} from "./logic";
import {EnvVariables} from "./Types";

dotenv.config();

export class <PERSON>ript extends ScriptBase {
    public getName(): string {
        return 'Add userId to paypal connect';
    }

    public getAuthor(): string {
        return 'mateuszs';
    }

    public getTicket(): string {
        return 'CHA-5395';
    }

    async dryRun(): Promise<void> {
        const dryRun = true;

        await this.execute(dryRun);
    }

    async wetRunOnPrimary(): Promise<void> {
        const dryRun = false;

        await this.execute(dryRun);
    }

    wetRunOnSecondary(): Promise<void> {
        throw new Error("Method not implemented.");
    }

    private async execute(dryRun: boolean): Promise<void> {
        const envVariables = this.getParamsFromEnvVars();
        const mongoClient = await MongoClient.connect(process.env.MONGO_URI!);
        const logger = {
            log: (message: string) => {
                console.log(message);
            },
        }
        logger.log(`Env Variables: ${JSON.stringify(envVariables)}`);

        const logic = new AddUserIdToPaypalConnect(
            mongoClient,
            logger,
        );

        await logic.execute(envVariables, dryRun);
    }

    private getParamsFromEnvVars(): EnvVariables {
        const batchSize = process.env.BATCH_SIZE
            ? parseInt(process.env.BATCH_SIZE, 10)
            : 1;
        const page = process.env.PAGE
            ? parseInt(process.env.PAGE, 10)
            : 1;
        const payPalBaseUrl = process.env.PAYPAL_BASE_URL!;
        const payPalClientId = process.env.PAYPAL_CLIENT!;
        const payPalClientSecret = process.env.PAYPAL_SECRET!;
        return {
            batchSize, page, payPalVars:{ payPalBaseUrl , payPalClientId, payPalClientSecret }
        };
    }
}

const script = new Script();
script.main();

# Purpose
This script adds userId to existing paypal processors under connect property.

https://linear.app/chargeflow/issue/CHA-5395/improve-the-logic-responsible-for-connecting-the-new-payment-processor

## Motivation
As part of multiprocessor support we have to reject connecting the processor account if already connected. 
For paypal we have to check if the userId already exist.

## Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

1. Set the following environment variables in CWD:
```
MONGO_URI=<db connection string>
BATCH_SIZE=<batch size>
PAGE=<on which migration stops if not provided starting from page 1>
PAYPAL_BASE_URL=<paypal api url>
PAYPAL_CLIENT=<paypal client id>
PAYPAL_SECRET=<paypal api key>
```

2. Install deps from CWD: `npm i`

3. Run the `dry` mode first. See the number of projected items to migrate and adjust the `BATCH_SIZE` env var.

4. Run the migration in `wet-primary` mode once everything is prepared.


### Modes
dry - does not update anything, writes the results to the console
wet-secondary - not implemented
wet-primary - executes the actual migration

### How this script was tested
- [x] test migration against `dev` db
- [x] test migration against `test` db

import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { Db } from "mongodb";
dotenv.config();

export class Script extends ScriptBase {

  public db!: Db;
  public getName(): string {
    return "Update chargeflowPerformedAction for submitted Adyen cases";
  }

  public getAuthor(): string {
    return "<PERSON>iza Jumola";
  }

  public getTicket(): string {
    return "CHA-12994";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow([
        "MONGO_URI",
        "MONGO_DB_NAME",
      ]);

      const mongo = await loadMongoClient();
      console.log("Mongo client loaded");

      this.db = mongo.db(process.env.MONGO_DB_NAME);

      const disputesCollection = this.db.collection('disputes');
      const filter = {
        'chargeflow.submitted.submittedAt': { $ne: null },
        'dispute.processor': 'adyen',
        'chargeflow.cfActions.chargeflowPerformedAction': { $ne: true }
      };
      let count = 0;
      
      if (isDryRun) {
        count = await disputesCollection.countDocuments(filter);
      } else {
        const result = await disputesCollection.updateMany(
          filter,
          {
            $set: {
              'chargeflow.cfActions.chargeflowPerformedAction': true,
            },
          }
        );
        count = result.modifiedCount;
      }
      console.log(`Finished ${isDryRun ? 'dry run' : 'wet run'}. Processed ${count} documents`);

    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }
}

const script = new Script();
script.main();

import { AnyBulkWriteOperation, Document, MongoClient } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext, ScriptMode } from '../shared/script-base';

//update json file
import alerts from './alertId-transactionId.json';

type Alert = {
  alertId: string;
  transactionId: string;
};

class Script extends ScriptBase {

  public getName(): string {
    return 'Update processorTransactionId field in alerts retroactively';
  }

  public getAuthor(): string {
    return 'Orly Moshe';
  }

  public getTicket(): string {
    return 'CHA-5605';
  }

  async dryRun(context: ScriptContext) {
    await this.logic(context);
  }

  wetRunOnSecondary(_context: ScriptContext): Promise<void> {
    throw new Error('Method not implemented.');
  }

  async wetRunOnPrimary(context: ScriptContext) {
    await this.logic(context);
  }

  private async logic(ctx: ScriptContext) {
    const mode = (ctx.args[0] ?? 'dry') as ScriptMode;
    const client: MongoClient = await loadMongoClient();

    const updateProcessorFieldOps = await this.createUpdateProcessorTransactionIdFieldOps(alerts);

    if (mode === 'dry') {
      console.log('Dry run mode');
      console.log('Updating %s alerts: ', updateProcessorFieldOps.length);
      console.log('Updating the following alerts: ', JSON.stringify(updateProcessorFieldOps));
    }
    else {
      const updatedCount = await this.updateAlert(client, updateProcessorFieldOps);
      console.log('Updating the following alerts: ', JSON.stringify(updateProcessorFieldOps));
      console.log('Updated %s alerts', updatedCount);
    }
  }

  private async createUpdateProcessorTransactionIdFieldOps(data: Alert[]): Promise<AnyBulkWriteOperation<Document>[]> {

    const updateProcessorTransactionIdFieldOps: AnyBulkWriteOperation<Document>[] = [];

    for (const item of data) {
      updateProcessorTransactionIdFieldOps.push({
          updateOne: {
            filter: { 'alertId': item.alertId },
            update: {
              $set: {
                'processorTransactionId': item.transactionId,
                'processorSource': 'script'
              }
            }
          }
        });
      }

    return updateProcessorTransactionIdFieldOps;
  }

  private async updateAlert(client: MongoClient, updateOps: AnyBulkWriteOperation<Document>[] | []): Promise<number> {
    try {
      return client
        .db('chargeflow_api')
        .collection('alerts')
        .bulkWrite(
          updateOps,
          { ordered: false })
        .then((result) => result.modifiedCount);
    }
    catch (error) {
      console.error('Failed to update alerts %s', error);
      return 0;
    }
  }
}

const script = new Script();
script.main();

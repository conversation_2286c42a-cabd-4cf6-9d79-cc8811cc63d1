# Purpose
This script updates alerts transactionIds using a JSON with data from AirTable

https://linear.app/chargeflow/issue/CHA-5605/update-transactionids-from-airtable-to-mongodb

## Motivation
The transactionIds are updated manually to AirTable, new alerts transactionIds will be updated by our public API. 
This should update existing alerts.

## Running this script
`npx ts-node ./update-transactionId-in-alerts-retro.ts <dry or wet-secondary or wet-primary> 2>&1 | tee log.txt`

NOTE: 
1. a `.env` file is expected to be present in CWD - using `MONGO_URI`
2. alertId-transactionId.json should be updated by the date we need to update 
example file structure:

`[
  {
    "alertId": "alertId1",
    "transactionId": "transactionId1"
  },
  {
    "alertId": "alertId2",
    "transactionId": "transactionId2"
  }
 ]`

When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console 
wet-secondary - not utilized in this script
wet-primary - updates the ALERTS collection.

### How this script was tested
[x] Alerts have:
  [x] processorTransactionId exists with value
  [x] processorSource exists with value(script)
import { <PERSON><PERSON>tBase, <PERSON>riptContext, <PERSON>riptMode } from "../shared/script-base";
import { GITHUB_TOKEN, ORG_NAME, WEBHOOK_ENDPOINT, WEBHOOK_SECRET } from "./constants";

const scriptFn = async ({ mode } : { mode: ScriptMode})=> {
  const repositories = await fetchRepositories();

  const webhookConfig = {
    url: WEBHOOK_ENDPOINT!,
    secret:WEBHOOK_SECRET!,
    content_type: 'json'
  }

  if (mode === 'dry') {
    const prdemo = repositories.find(repo => repo === 'pr-demo');
    return createWebhook(prdemo!,ORG_NAME,webhookConfig)
  }

  if (mode === 'wet-primary') {
    for (const repo of repositories) {
      await createWebhook(repo,ORG_NAME,webhookConfig)
      delay(2000)
    }
  }
}

const delay = (ms: number) => {
  return new Promise( resolve => setTimeout(resolve, ms) );
}

const fetchRepositories = async () => {
  const query = `
    query {
      organization(login: "chargeflow-team") {
        repositories(first: 100) {
          nodes {
            name
          }
        }
      }
    }
  `;

  const response = await fetch('https://api.github.com/graphql', {
    method: 'POST',
    headers: {
      'Authorization': `bearer ${GITHUB_TOKEN}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ query }),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.statusText}`);
  }

  const data = await response.json();
  const result = data.data.organization.repositories.nodes;
  return result.map((repo: { name: string }) => repo.name) as string[];
};

const createWebhook = async (repo: string, owner: string, config: { url: string, secret: string }) => {
  const response = await fetch(`https://api.github.com/repos/${owner}/${repo}/hooks`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${GITHUB_TOKEN}`,
      'Accept': 'application/vnd.github.v3+json',
      'X-GitHub-Api-Version': '2022-11-28'
    },
    body: JSON.stringify({
      name: 'web',
      active: true,
      events: ['dependabot_alert'],
      config,
    }),
  });

  if (response.ok) {
    const data = await response.json();
    console.log(`Webhook created with id: ${data.id}`);
  } else {
    console.error(`Error creating webhook: ${response.status} ${response.statusText}`);
  }
};

class Script extends ScriptBase {
  getName = () => 'Create webhook config for all repositories';

  getAuthor = () => 'Arnold Ramos';

  getTicket = () => 'CHA-3666';

  async dryRun() {
    await scriptFn({ mode: 'dry' });
  }

  async wetRunOnPrimary(_context: ScriptContext): Promise<void> {
    await scriptFn({ mode: 'wet-primary' });
  }

  async wetRunOnSecondary(_context: ScriptContext): Promise<void> {
    console.log('Not supported. Resending messages to a queue.')
  }
}

const script = new Script();
script.main();
import 'dotenv/config';
import {fromIni} from '@aws-sdk/credential-providers';
import 'aws4';
// @ts-ignore
import {ScriptBase} from '../shared/script-base';
import {EventBridgeClient, PutEventsCommand} from '@aws-sdk/client-eventbridge';
import {ParamsConfig} from '@chargeflow-team/chargeflow-utils-sdk';
import * as fs from 'node:fs';

export class ReconBillingChargeFromEventsLogs extends ScriptBase {
    public getName() {
        return 'Recon billing charge from events logs';
    }

    public getAuthor() {
        return 'Georgy';
    }

    public getTicket(): string {
        return 'CHA-13133';
    }

    public async dryRun(): Promise<void> {
        await ParamsConfig.loadParameters();
        const eventBridge = new EventBridgeClient({
            credentials: fromIni({profile: 'prod_token'}),
            region: 'us-east-1'
        });
        const events = JSON.parse(fs.readFileSync('./input.json', 'utf-8'));
        for (let i = 0; i < events.length; i++) {
            const command = new PutEventsCommand({
                Entries: [
                    {
                        Detail: JSON.stringify(events[i]),
                        Source: 'utility-scripts',
                        EventBusName: 'prod-events-infra-BillingSystemEventBus',
                        Time: new Date(),
                        DetailType: 'BILLING_PAYMENT_CHARGE'
                    }
                ]
            });
            await eventBridge.send(command);
            console.log('Event sent', events[i]);
        }
    }

    public async wetRunOnPrimary(): Promise<void> {
        await ParamsConfig.loadParameters();
        throw new Error('Not implemented');
    }

    public async wetRunOnSecondary(): Promise<void> {
        await ParamsConfig.loadParameters();
        throw new Error('Not implemented');
    }
}

const script = new ReconBillingChargeFromEventsLogs();
script.main();

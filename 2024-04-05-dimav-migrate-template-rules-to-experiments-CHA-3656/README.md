# Purpose:
To update experiments rules based on the templates they use.

# Motivation:
We want to be able to filter out disputes that match experiments precisely. This will allow us to have a more accurate representation of the disputes that are being filtered out by the experiments with a high reliability of whatever is assigned an experiment should also be generated fully.

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**
`experiments_ids.json` in the script folder also expected = array of strings which represent the experimentIds that should be updated.


### How this script was tested
[x] Wet run against local copy of prod DB
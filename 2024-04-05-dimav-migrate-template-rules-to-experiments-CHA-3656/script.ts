import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import experimentsIds from './experiments_ids.json';
import { ObjectId } from 'mongodb';

dotenv.config();

export class ScriptAddPickUpDateToShippingObject extends ScriptBase {
    public getName(): string {
        return 'Update experiments rules based on used templates';
    }

    public getAuthor(): string {
        return 'Dima Vinogradov';
    }

    public getTicket(): string {
        return 'CHA-3656';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {
        const client = await loadMongoClient();
        const templatesCollection = client.db('chargeflow_api').collection('automation_templates');
        const experimentsCollection = client.db('chargeflow_api').collection('experiments');

        const [templatesDict, experiments] = await Promise.all([
            this.getTemplatesDict(templatesCollection),
            this.getExperiments(experimentsCollection)
        ]);

        for (const experiment of experiments) {
            const updatedRules = this.updateRulesForExperiment(experiment, templatesDict);
            if (!isDryRun) {
                await experimentsCollection.updateOne({ _id: experiment._id }, { $set: { rules: updatedRules } });
            }
            console.log(`Experiment ${experiment._id} will have ${updatedRules.length} rules after update.`);
        }

        client.close();
    }

    private async getTemplatesDict(templatesCollection:any) {
        const templates = await templatesCollection.find({}).toArray();
        return templates.reduce((acc:Map<string,any>, tmpl:any) => {
            if (!tmpl.rules) return acc;
            const updatedRules = tmpl.rules.map((rule:any) => ({
                ...rule,
                source: 'template',
                sourceName: tmpl.name,
            }));
            acc.set(tmpl.name, updatedRules);
            return acc;
        }, new Map());
    }

    private async getExperiments(experimentsCollection:any) {
        const query = { _id: { $in: experimentsIds.map(d => new ObjectId(d)) } };
        return experimentsCollection.find(query).toArray();
    }

    private updateRulesForExperiment(experiment:any, templatesDict:Map<string,any>) {
        const experimentTemplates = experiment.variants.flatMap((e:any) => e.data.order);
        const rulesForTemplate = experimentTemplates.flatMap((k:string) => templatesDict.get(k)).filter((r:any) => !!r);
        const cleanRules = experiment.rules.filter((rule:any) => rule.source !== 'template');
        return [...cleanRules, ...rulesForTemplate];
    }
}

const script = new ScriptAddPickUpDateToShippingObject();
script.main();

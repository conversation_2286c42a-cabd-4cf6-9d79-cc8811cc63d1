import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import fs from 'fs';

import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Collection, Filter, MongoClient, Document, UpdateFilter, WithId, ObjectId } from 'mongodb';

dotenv.config();

const defaultDbName = 'chargeflow_api';

interface Dispute extends Document {
    caseId: string;
    shopName: string;
    amount: number;
    customFields: Document[];
    isWon: boolean;
    amountWon: number;
    successFee: number;
}

interface DisputesWithWrongAmount extends Document {
    id: ObjectId;
    caseId: string;
    shopName: string;
    finalAmount: number | null;
    wrongAmount: number;
    isWon: boolean;
    feePercentage: number | null;
}


export class FixAsanaDisputesAmount extends ScriptBase {
    public getName(): string {
        return 'Fix asana disputes amount to be the correct amount';
    }

    public getAuthor(): string {
        return '<PERSON><PERSON>';
    }

    public getTicket(): string {
        return 'CHA-13953';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {

        const handleDryRun = async (disputesWithWrongAmount: DisputesWithWrongAmount[]) => {
            console.log(`Dry run mode. Found ${disputesWithWrongAmount.length} documents to update.`);
            console.log(JSON.stringify(disputesWithWrongAmount, null, 2));
        };

        const handleWetRun = async (collection: Collection, disputesWithWrongAmount: DisputesWithWrongAmount[]) => {
            if (disputesWithWrongAmount.length > 0) {
                console.log(`Wet run mode. Found ${disputesWithWrongAmount.length} documents to update.`);
                const result = await updateDisputes(collection, disputesWithWrongAmount);
                console.log(`Updated ${result.modifiedCount} documents.`);
            }
        };

        try {
            const sourceCollection = 'disputes';
            const mongoClient: MongoClient = await loadMongoClient();
            const collection = mongoClient.db(defaultDbName).collection(sourceCollection);
            const filter: Filter<Document> = { 'dispute.source': 'asana' };
            const project: Document = {
                caseId: '$dispute.id',
                shopName: 1,
                amount: '$dispute.amount',
                isWon: '$chargeflow.resolved.isWon',
                customFields: '$dispute.rawData.dispute.custom_fields',
                amountWon: '$chargeflow.resolved.amountWon',
                successFee: '$chargeflow.resolved.successFee',
            };
            const disputesWithWrongAmountFullList: DisputesWithWrongAmount[] = [];
            for await (const disputes of getDisputesGenerator(collection, filter, project)) {
                const disputesWithWrongAmount = getWrongDisputes(disputes);
                disputesWithWrongAmountFullList.push(...disputesWithWrongAmount);
                if (isDryRun) {
                    await handleDryRun(disputesWithWrongAmount);
                } else {
                    await handleWetRun(collection, disputesWithWrongAmount);
                }
            }
            await fs.promises.writeFile('disputesWithWrongAmount.json', JSON.stringify(disputesWithWrongAmountFullList, null, 2));
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        }
    }
}

const script = new FixAsanaDisputesAmount();
script.main();

async function* getDisputesGenerator(collection: Collection, filter: Filter<Document>, project: Document): AsyncGenerator<WithId<Dispute>[]> {
    let hasMore = true;
    let offset = 0;
    const limit = 100;
    while (hasMore) {
        console.log(`Fetching disputes from offset ${offset} with limit ${limit}`);
        const disputes = await collection.find(filter, { projection: project }).skip(offset).limit(limit).toArray();
        if (disputes.length === 0) {
            hasMore = false;
            continue;
        }
        yield disputes as WithId<Dispute>[];
        offset += limit;
    }
}

const getDisputes = async (collection: Collection, filter: Filter<Document>, project: Document) => {
    const disputes = await collection.find(filter, { projection: project }).toArray();
    return disputes as WithId<Dispute>[];
}

const getAmountCustomFields = (customFields: Document[]) => {
    const amountCustomFieldNames = ['Amount', 'Dispute Amount'];
    const amountCustomFieldsValues = { 'Amount': '', 'Dispute Amount': '' };
    const amountCustomFields = customFields.filter((customField: Document) => {
        return amountCustomFieldNames.includes(customField.name);
    });
    amountCustomFields.forEach((customField: Document) => {
        const customFieldName = customField.name as 'Dispute Amount' | 'Amount';
        amountCustomFieldsValues[customFieldName] = customField.display_value;
    });
    return amountCustomFieldsValues;
}

const getRealAmount = (dispute: WithId<Dispute>) => {
    const disputeAmount = dispute.amount;
    const amountCustomFields = getAmountCustomFields(dispute.customFields);
    const finalAmount = Number(amountCustomFields['Dispute Amount']) || Number(amountCustomFields['Amount']) || null;
    const isAmountValid = Boolean(disputeAmount === finalAmount || (finalAmount === null && disputeAmount === null));
    return { finalAmount, disputeAmount, isAmountValid };
}

const getWrongDisputes = (disputes: WithId<Dispute>[]) => {
    const disputesWithWrongAmount: DisputesWithWrongAmount[] = [];
    disputes.forEach((dispute: WithId<Dispute>) => {
        const { finalAmount, disputeAmount, isAmountValid } = getRealAmount(dispute);
        
        console.log(`Dispute ${dispute._id} - isAmountValid: ${isAmountValid}`);
        if (!isAmountValid) {
            const { amountWon, successFee } = dispute;
            const feePercentage = amountWon ? successFee / amountWon : null;
            console.log()
            disputesWithWrongAmount.push({
                id: dispute._id,
                caseId: dispute.caseId,
                shopName: dispute.shopName,
                finalAmount: finalAmount,
                wrongAmount: disputeAmount,
                isWon: dispute.isWon,
                feePercentage: feePercentage,
            });
        }
    });
    return disputesWithWrongAmount;
}

const updateDisputes = async (collection: Collection, disputesWithWrongAmount: DisputesWithWrongAmount[]) => {
    const bulkOps = disputesWithWrongAmount.map((dispute: DisputesWithWrongAmount) => {
        const amountWon = dispute.isWon ? dispute.finalAmount : 0;
        const successFee = dispute.feePercentage! * amountWon! || null;
        console.log(`Success fee: ${dispute.feePercentage! * 100}%`)
        return {
            updateOne: {
                filter: { _id: dispute.id },
                update: {
                    $set: { 
                        'dispute.amount': dispute.finalAmount,
                        'dispute.amountInUsd': dispute.finalAmount,
                        'chargeflow.submitted.amountInUSD': dispute.finalAmount,
                        'chargeflow.resolved.successFee': successFee,
                        'chargeflow.resolved.amountWon': amountWon,
                     },
                    $unset: { amount: 1 }
                },
            },
        };
    });
    const result = await collection.bulkWrite(bulkOps);
    return result;
}

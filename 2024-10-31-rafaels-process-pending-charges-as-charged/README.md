# Purpose:

Processing pending charges from selected merchants as promotional charges.

# Motivation:

Consider all pending charges from selected merchants as forgiven charges, hence creating promitional charges with 100% discount for each pending charge,

# Running this script

`npx tsx ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with:
**MONGO_URI** 

### How this script was tested
[x] Dry and Wet run against dev
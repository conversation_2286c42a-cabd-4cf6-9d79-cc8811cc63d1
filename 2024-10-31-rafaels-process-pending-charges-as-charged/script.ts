import dotenv from "dotenv";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { Collection, MongoClient } from "mongodb";
import { ObjectId } from "mongodb";
import path from 'path';
import fs from 'fs/promises';

dotenv.config();

type JsonData = {
    chargeflowIds: string[];
  }

type MappedCharge = {
    dateCreated: Date;
    chargeflowId: any;
    billingId: null;
    billingDate: null;
    billingStatus: null;
    disputeId: any;
    caseId: any;
    disputeProcessor: any;
    shopId: any;
    shopName: any;
    shopPlatform: any;
    disputeRecoveredAmount: number | null;
    successAmount: number;
    chargedAmount: number;
    promotionalDiscount: number;
    promotionCode: string;
    currency: string;
    resolvedDate: Date;
    chargeSource: string;
    chargeType: string;
    chargeId: null;
    recurringAppChargeId: null;
    chargeDescription: null;
    rawData: object;
}

let offset = 0;
const PAGE_SIZE = 100;
const MANUAL_CHARGE_SOURCE = 'Manual Promotional Charge';
const MANUAL_DISCOUNT = 'Manual Discount';
const USAGE_CHARGE_TYPE = 'Usage Charge';
const USD_CURRENCY = 'USD';

const toObjectIds = (charges) => charges.map((charge) => new ObjectId(charge.disputeId));

export class ScriptMigratePendingChargesToPromotionalCharges extends ScriptBase {
    public getName(): string {
        return "Migrate Pending Charges to Promotional Charges";
    }

    public getAuthor(): string {
        return "Rafael Schuster";
    }

    public getTicket(): string {
        return "CHA-9721";
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log("Running in dry run mode (not changing anything)");
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error("Method not implemented.");
    }

    private async getCaseIdsFromJsonFile():Promise<JsonData | undefined> {
        try {
          const filePath = path.join(__dirname, "chargeflowIds.json");
          const data = await fs.readFile(filePath, "utf-8");
          return JSON.parse(data);
        } catch (error) {
          console.error("Error reading case ids from file", error);
          console.error(
            "Make sure the file <chargeflowIds.json> exists and is in the same directory as the script"
          );
          return undefined;
        }
      }

    createBulkToUpdateCharges = (mappedCharges) => {
        return mappedCharges.reduce((acc, mappedCharge) => {
            acc.push({
                updateOne: {
                    filter: { caseId: mappedCharge.caseId },
                    update: { $set: mappedCharge },
                    upsert: true,
                },
            });
            return acc;
        }, []);
    };

    async fetchPendingChargesAndDisputesDataToUpdate(chargeflowIds, collection: Collection) {
        const projection = {
            chargeflowId: 1,
            shopName: 1,
            'dispute.id': 1,
            'dispute.amount': 1,
            'dispute.currency': 1,
            'dispute.processor': 1,
            shopId: 1,
        };

        return collection
            .aggregate([
                { $match: { chargeflowId: { $in: chargeflowIds.map(id => new ObjectId(id)) }, dateCreated: { $lt: new Date('2024-10-27T00:00:00.000+00:00') }, manuallyProcessedAsPromotionalCharge: { $ne: true } } },
                { $lookup: { from: 'disputes', localField: 'disputeId', foreignField: '_id', as: 'dispute', pipeline: [{ $project: projection }] } },
                { $unwind: '$dispute' },
                { $lookup: { from: 'shops', localField: 'chargeflowId', foreignField: 'chargeflow_id', as: 'shop', pipeline: [{ $project: { platform: 1 } }] } },
                { $unwind: '$shop' },
                { $skip: offset },
                { $limit: PAGE_SIZE },
            ])
            .toArray()
            .catch((err) => {
                if (err.code === 'ECONNREFUSED') throw new Error(`No internet connection: ${JSON.stringify(err)}`);
                throw err;
            });
    }

    async bulkWriteCharges(bulk, client) {
        return client.db('chargeflow_api').collection('charges').bulkWrite(bulk).catch((err) => {
            console.error('Failed to bulkWriteCharges %s, err: %s', JSON.stringify(err));
            throw err;
        });
    }

    async flagManyPendingCharges(disputeIds, collection: Collection) {
        if (!disputeIds.length) throw new Error('Missing disputeIds');

        return collection.updateMany(
            { disputeId: { $in: disputeIds } },
            { $set: { manuallyProcessedAsPromotionalCharge: true } }
        ).catch((err) => {
            if (err.code === 'ECONNREFUSED') throw new Error(`No internet connection: ${JSON.stringify(err)}`);
            throw err;
        });
    }

    private mapPromotionalManualCharge(pendingChargeShopAndDisputeData) {
        if (!pendingChargeShopAndDisputeData) throw new Error('Missing pendingChargeShopAndDisputeData');

        const dispute = pendingChargeShopAndDisputeData.dispute;
        const finalPrice = pendingChargeShopAndDisputeData.successAmount;
        const shopPlatform = pendingChargeShopAndDisputeData.shop.platform;

        return {
            dateCreated: new Date(),
            chargeflowId: dispute.chargeflowId,
            billingId: null,
            billingDate: null,
            billingStatus: null,
            disputeId: dispute._id,
            caseId: dispute.dispute?.id || null,
            disputeProcessor: dispute.dispute?.processor || null,
            shopId: dispute.shopId || null,
            shopName: dispute.shopName || null,
            shopPlatform: shopPlatform || null,
            disputeRecoveredAmount: dispute.dispute?.amount ? Number(dispute.dispute?.amount) : null,
            successAmount: finalPrice,
            chargedAmount: 0,
            promotionalDiscount: -Math.abs(finalPrice),
            promotionCode: MANUAL_DISCOUNT,
            currency: USD_CURRENCY,
            resolvedDate: new Date(pendingChargeShopAndDisputeData.resolvedDate),
            chargeSource: MANUAL_CHARGE_SOURCE,
            chargeType: USAGE_CHARGE_TYPE,
            chargeId: null,
            recurringAppChargeId: null,
            chargeDescription: null,
            rawData: {},
        };
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {
        const client: MongoClient = await loadMongoClient();
        try {
            console.log('Creating connection to MongoDB...');
            const collection = client
                .db("chargeflow_api")
                .collection("pendingCharges");

            console.log('Processing pending charges and updating database');

            const mappedCharges: MappedCharge[] = [];
            let hasMore = true;
            let pendingChargesAndDisputeDataCount = 0;

            const jsonData = await this.getCaseIdsFromJsonFile();
            if (!jsonData) {
                console.error("No data found in json file");
                return;
              }
            const { chargeflowIds } = jsonData;
            console.log('Chargeflow ids to fetch pending charges: %j', chargeflowIds);

            while (hasMore) {
                console.log('Fetching pending charges with offset %s, page size: %s', offset, PAGE_SIZE);
                const pendingChargesShopAndDisputeData = await this.fetchPendingChargesAndDisputesDataToUpdate(chargeflowIds, collection);
                if (!pendingChargesShopAndDisputeData.length) {
                    hasMore = false;
                    console.log('Has more pending charges: %s', hasMore);
                    break;
                }
                pendingChargesAndDisputeDataCount += pendingChargesShopAndDisputeData.length;
                console.log('Fetched %s pending charges with dispute and shop data', pendingChargesShopAndDisputeData.length);

                for (const pendingChargeShopAndDisputeData of pendingChargesShopAndDisputeData) {
                    try {
                        console.log('Mapping pending charge id: %s', pendingChargeShopAndDisputeData._id);
                        const mappedCharge = this.mapPromotionalManualCharge(pendingChargeShopAndDisputeData);
                        mappedCharges.push(mappedCharge);
                    } catch (err) {
                        console.error('Failed to process pending charge of id: %s, err: %s', pendingChargeShopAndDisputeData._id, JSON.stringify(err));
                    }
                }
                offset += PAGE_SIZE;
                hasMore = pendingChargesShopAndDisputeData.length === PAGE_SIZE;
                console.log('Has more pending charges: %s', hasMore);
            }

            console.log('Successfully mapped %s pending charges to %s promotional charges', pendingChargesAndDisputeDataCount, mappedCharges.length);

            const bulk = this.createBulkToUpdateCharges(mappedCharges);
            if (!bulk.length) {
                console.warn('No bulk to process. Shutting down..');
                return;
            }
            console.log('Successfully created bulk of %s entries', bulk.length);

            if (!isDryRun) {
                console.log(`${new Date().toISOString()}: Not dry run - updating promotional charges and flagging pending charges`);
                const resBulkWrite = await this.bulkWriteCharges(bulk, client);
                console.log('Successfully upserted %s charges from %s pending charges', resBulkWrite.nUpserted, pendingChargesAndDisputeDataCount);

                const objectIds = toObjectIds(mappedCharges);
                const resFlagUpdate = await this.flagManyPendingCharges(objectIds, collection);
                console.log('Successfully flagged %s pending charges from %s total pending charges', resFlagUpdate.modifiedCount, pendingChargesAndDisputeDataCount);
            } else {
                console.log(`${new Date().toISOString()}: Dry run - Would have updated ${pendingChargesAndDisputeDataCount} records`);
            }

        } catch (error) {
            console.error(`${new Date().toISOString()}: Error processing pending charges as promotional charges. error: ${JSON.stringify(error)}`);
            console.log('Closing MongoDB connection');
            await client.close();
            process.exit(1);

        } finally {
            console.log('Successfully processed pending charges');
            console.log('Closing MongoDB connection');
            await client.close();
        }
    }
}

const script = new ScriptMigratePendingChargesToPromotionalCharges();
script.main();
# Purpose:
A recently reverted code introduced a bug where we failed to reconcile disputes for some shops. 

In effect, code ingested malformed dispute objects which this script is aiming to delete.

Post-successful run, reconciliation will run again, and will ingest the disputes properly.

# How to find them
```
{ 'dispute.id': null, 'dispute.source': 'stripe' }
```

# Current count in the DB: `2841`

# Dry run
Script logs a total count of eligible disputes.

# Wet run
Script `deleteMany`'s the eligible disputes.

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary>`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**.

### How this script was tested
[x] Wet run against dev 
[x] Dry run in prod
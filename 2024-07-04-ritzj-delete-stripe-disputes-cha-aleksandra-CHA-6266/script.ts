import dotenv from "dotenv";
import { MongoClient } from "mongodb";
import { ScriptBase } from "../shared/script-base";

dotenv.config();

export class Script extends ScriptBase {
    public getName(): string {
        return 'Delet malformed disputes';
    }

    public getAuthor(): string {
        return 'ritzj';
    }

    public getTicket(): string {
        return 'CHA-6266';
    }

    async dryRun(): Promise<void> {
        const dryRun = true;

        await this.execute();
    }

    async wetRunOnPrimary(): Promise<void> {
        await this.execute(false);
    }

    wetRunOnSecondary(): Promise<void> {
        throw new Error("Method not implemented.");
    }

    private async execute(isDryRun = true): Promise<void> {
        if (!process.env.MONGO_URI) {
            throw new Error('MONGO_URI is required');
        }

        const mongoClient = await MongoClient.connect(process.env.MONGO_URI!);

        // Find eligible disputes 
        const filter = { 'dispute.id': null, 'dispute.source': 'stripe' };
        const disputesCount = await mongoClient
            .db('chargeflow_api')
            .collection('disputes')
            .countDocuments(filter);
    
        console.log('Found disputes:', disputesCount);

        if (isDryRun === false) {
            // Delete disputes
            const result = await mongoClient
                .db('chargeflow_api')
                .collection('disputes')
                .deleteMany(filter);
        
            console.log('Deleted disputes result %j', result);
        }
    }
}

const script = new Script();
script.main();

import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { Db, ObjectId } from "mongodb";
dotenv.config();

export class Script extends ScriptBase {

  public db!: Db;
  public getName(): string {
    return "Create missing processor for stripe users";
  }

  public getAuthor(): string {
    return "Riza Jumola";
  }

  public getTicket(): string {
    return "CHA-15017";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow([
        "MONGO_URI",
        "MONGO_DB_NAME",
      ]);

      const mongo = await loadMongoClient();
      console.log("Mongo client loaded");

      this.db = mongo.db(process.env.MONGO_DB_NAME);
      const chargeflowId = ctx.args[1];
      if (!chargeflowId) {
        throw new Error('Missing chargeflowId');
      }
      console.log(`Processing ${chargeflowId}...`);

      const stripeUserCollection = this.db.collection('stripe_user_account');
      const processorCollection = this.db.collection('processors');

      const result = await stripeUserCollection.aggregate([
        {
          $match: { chargeflowId: new ObjectId(chargeflowId) }
        },
        {
          $lookup: {
            from: 'processors',
            let: { chargeflowId: '$chargeflowId', stripeAccountId: '$stripeAccountId' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ['$chargeflow_id', '$$chargeflowId'] },
                      { $eq: ['$connect.stripe_user_id', '$$stripeAccountId'] }
                    ]
                  }
                }
              }
            ],
            as: 'processors'
          }
        },
      ]).toArray();
      if (!result?.length) {
        throw new Error('Stripe account not available.');
      }
      console.log(`Found ${result.length} stripe accounts.`);
      for (const stripeAccount of result) {
        if (!stripeAccount.processors?.length) {
          const stripeAccountId = stripeAccount.stripeAccountId;
          console.log(`Creating processor document for ${stripeAccountId}`);
          const insertDocument = {
            "date_created": new Date(),
            "date_updated": new Date(),
            "chargeflow_id": new ObjectId(chargeflowId),
            "accountId": new ObjectId(stripeAccount.accountId),
            "processor_name": "stripe",
            "connect": {
              "stripe_user_id": stripeAccount.stripeAccountId,
              "stripe_statement_descriptor": stripeAccount.statementDescriptor,
            },
          };
          console.log(`Creating document: ${JSON.stringify(insertDocument)}`);
          if (!isDryRun) {
            const res = await processorCollection.insertOne(insertDocument);
            console.log(res);
          }
        }
      }
    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }
}

const script = new Script();
script.main();

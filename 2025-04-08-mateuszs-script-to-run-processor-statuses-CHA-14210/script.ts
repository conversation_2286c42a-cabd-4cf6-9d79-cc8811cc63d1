import {loadMongoClient} from "../shared/mongo-config";
import {ScriptBase} from "../shared/script-base";
import {Source} from "@chargeflow-team/data-access";
import {AwsEventBridge, DynamicHttpError} from "@chargeflow-team/chargeflow-utils-sdk";
import {
    PROCESSOR_DEACTIVATING_DETAIL_TYPE,
    ProcessorDeactivatingEventDetail, processorDeactivatingEventDetailSchema,
    ProcessorNameEnum,
    ProcessorNameEnumValues
} from "@chargeflow-team/events-infra";
import {IMongoProcessor} from "@chargeflow-team/data-access/dist/Processor/Types";


export class UpdateAdyenDisputesStatuses extends ScriptBase {

    public getName(): string {
        return "Emit processor deactivating event to proceed with deactivation";
    }

    public getAuthor(): string {
        return "Mateusz Smagiel";
    }

    public getTicket(): string {
        return "cha-14210-fix-processors-statuses-for-which-deactivation-request-stuck";
    }

    async dryRun(): Promise<void> {
        await this.logic(true);
    }

    async wetRunOnPrimary(): Promise<void> {
        try {
            await this.logic(false);
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    async wetRunOnSecondary(): Promise<void> {
        throw new Error("Method is not implemented.");
    }

    async logic(dryRun: boolean) {
        const client = await loadMongoClient();
        const processors = await client
            .db('chargeflow_api')
            .collection<IMongoProcessor>('processors')
            .find({
                "status.status": 'Deactivating'
            })
            .toArray();
        console.log(`Found ${processors.length} processors with deactivating status`);
        for (const processor of processors) {
            const processorDeactivatingEvent: ProcessorDeactivatingEventDetail = {
                chargeflowId: processor.chargeflow_id,
                accountId: processor.accountId,
                processorId: processor._id,
                processorName: this.mapToProcessorEnum(processor.processor_name),
                reason: processor.status?.reason || 'client_request',
                actor: processor.status?.actor || 'system',
                dateCreated: new Date(),
            };
            if (dryRun) {
                console.log('skipping emitting processorDeactivatingEvent %j', processorDeactivatingEvent);
                continue;
            }
            await AwsEventBridge.putEventWithValidation(
                processorDeactivatingEvent,
                processorDeactivatingEventDetailSchema,
                PROCESSOR_DEACTIVATING_DETAIL_TYPE,
                'script',
                process.env.PROCESSORS_EVENT_BUS!,
            );
        }
    }

    private mapToProcessorEnum(processorName: Source): ProcessorNameEnumValues {
        switch (processorName) {
            case Source.Adyen:
                return ProcessorNameEnum.adyen;
            case Source.AfterpayOrClearpay:
                return ProcessorNameEnum.afterpay;
            case Source.Braintree:
                return ProcessorNameEnum.braintree;
            case Source.Klarna:
                return ProcessorNameEnum.klarna;
            case Source.PayPal:
                return ProcessorNameEnum.paypal;
            case Source.Stripe:
                return ProcessorNameEnum.stripe;
            case Source.ShopifyPayments:
                return ProcessorNameEnum.shopify;
            default:
                throw new DynamicHttpError('InternalServerError', `Unsupported processor name ${processorName}`);
        }
    };
}

const script = new UpdateAdyenDisputesStatuses();
script.main();

# Purpose

This script pushes forward the processor deactivation process that was stalled due to an error in the API endpoint.
https://linear.app/chargeflow/issue/CHA-14210/fix-processors-statuses-for-which-deactivation-request-stuck

## Motivation

We need to finish processor deactivation to have consistent data.

## Running this script

`npx ts-node ./script.ts <dry | wet-primary>`

## Modes

- dry - only logs the total number of processor deactivation events that are eligible for emitting
- wet-primary - emits processor deactivation events using AWS EventBridge
## Required env variables

MONGO_URI=<mongo_uri_string>
PROCESSORS_EVENT_BUS=<event bus url>

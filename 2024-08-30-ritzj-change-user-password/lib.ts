import { fromIni } from "@aws-sdk/credential-provider-ini";
import { Db, ObjectId } from "mongodb";
import {
  AdminSetUserPasswordCommand,
  CognitoIdentityProviderClient,
  AdminInitiateAuthCommandInput,
} from "@aws-sdk/client-cognito-identity-provider";

export const getParamsOrThrow = (): {
  email: string;
  awsProfile: string;
} => {
  const args = process.argv.slice(2);
  console.log('args found %j', args);
  const [_mode, email, awsProfile] = args;
  if (!email || !awsProfile) {
    console.error(
      "Please provide email, and aws profile"
    );
    throw new Error(
      'Run example: ts-node script.ts dry "email" "awsProfile"'
    )
  }

  return { email, awsProfile };
};

export const isEnvVarsOkOrThrow = () => {
  const requiredEnvVars = [
    "AWS_MERCHANT_POOL_ID",
    "AWS_MERCHANT_POOL_CLIENT_ID",
    "COGNITO_SECRET",
    "AWS_REGION",
    "USERS_EVENT_BUS_NAME",
  ];
  if (!requiredEnvVars.every((envVar) => process.env[envVar])) {
    throw new Error(
      `Missing required environment variables: ${requiredEnvVars.join(", ")}`
    );
  }
};

export const logAwsProfile = (awsProfile: string) => {
  console.log({
    region: process.env.AWS_REGION,
    credentials: fromIni({
      profile: awsProfile,
    }),
  });
};

export const generatePassword = () => {
  const symbols = "!@#$%^&*()_+-=[]{}|'";
  const rand = (size: number) => Math.floor(Math.random() * size);
  const randSymbol = () => symbols[rand(symbols.length)];
  const randNumber = () => Math.floor(Math.random() * 10);
  const randLowerCase = () => String.fromCharCode(rand(26) + 97);
  const randUpperCase = () => String.fromCharCode(rand(26) + 65);
  const password = Math.random().toString(36).slice(2).split("");
  for (let i = 0; i < 3; i++) {
    password.splice(rand(password.length), 0, randSymbol());
  }
  password.splice(rand(password.length), 0, randNumber().toString());
  password.splice(rand(password.length), 0, randLowerCase());
  password.splice(rand(password.length), 0, randUpperCase());
  return password.join("");
};


export const changeUserPassword = async (input: {
  idp: CognitoIdentityProviderClient,
  email: string;
}): Promise<string> => {
  const newPassword = generatePassword();
  console.log('newPassword', newPassword);

  const { idp, email } = input;

  const params = { // AdminSetUserPasswordRequest
    UserPoolId: process.env.AWS_MERCHANT_POOL_ID, // required
    Username: email, // required
    Password: newPassword, // required
    Permanent: true,
  };
  const command = new AdminSetUserPasswordCommand(params);
  const response = await idp.send(command);
  console.log('response', response);
  return newPassword;
}
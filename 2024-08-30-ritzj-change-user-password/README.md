# Purpose:
The purpose of this script is to change the password of a user in cognito.

<PERSON><PERSON>t creates and logs new password for the user.

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> <new-user-email-address> <local-aws-cred-profile>`
NOTE: a `.env` file is expected to be present in CWD with

**AWS_REGION**
**AWS_MERCHANT_POOL_ID**
**AWS_MERCHANT_POOL_CLIENT_ID**
**COGNITO_SECRET**
**USERS_EVENT_BUS**

See `.env.example` for an example.

### How this script was tested
[x] Wet run against dev & prod with data diff validation afterwards

### Sample command

```bash
npx ts-node ./script.<NAME_EMAIL> chargeflow
```
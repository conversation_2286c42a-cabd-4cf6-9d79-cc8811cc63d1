import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import {
  getParamsOrThrow,
  isEnvVarsOkOrThrow,
  logAwsProfile,
  changeUserPassword,
} from "./lib";
import { fromIni } from "@aws-sdk/credential-provider-ini";
import { CognitoIdentityProviderClient } from "@aws-sdk/client-cognito-identity-provider";

dotenv.config();

export class Script extends ScriptBase {
  public getName(): string {
    return "Changing a cognito user password";
  }

  public getAuthor(): string {
    return "Ritz Jumola";
  }

  public getTicket(): string {
    return "cha-9324";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }
  private async logic(_ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow();
      console.log("env vars are ok");

      const params = getParamsOrThrow();
      console.log("params are ok %", params);

      const { email, awsProfile } = params;

      logAwsProfile(awsProfile);

      const idp = new CognitoIdentityProviderClient({
        region: process.env.AWS_REGION,
        credentials: fromIni({ profile: awsProfile }),
      });

      if (isDryRun) {
        console.log("Dry run mode, skipping changing password");
        return;
      }

      const newPassword = await changeUserPassword({ idp, email });

      console.log('User password changed to: ', newPassword)
    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }
}

const script = new Script();
script.main();

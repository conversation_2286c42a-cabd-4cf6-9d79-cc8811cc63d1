import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { Db } from "mongodb";
dotenv.config();

export class Script extends ScriptBase {

  public db!: Db;
  public getName(): string {
    return "Update existing account mappings docs";
  }

  public getAuthor(): string {
    return "Ritz Jumola";
  }

  public getTicket(): string {
    return "CHA-12074";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow([
        "MONGO_URI",
        "MONGO_DB_NAME",
      ]);

      const mongo = await loadMongoClient();
      console.log("Mongo client loaded");

      this.db = mongo.db(process.env.MONGO_DB_NAME);

      let offset = 0;
      let hasMore = true;

      const accountMappingsCol = this.db.collection('accountMappings');

      while (hasMore) {
        const accountMappings = await accountMappingsCol.aggregate([
          // Match documents where onboardingStatus doesn't exist
          {
            $match: {
              onboardingStatus: { $exists: false },
            },
          },
          // Join with companies collection
          {
            $lookup: {
              from: 'companies',
              let: { chargeflowId: '$chargeflowId', email: '$email' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $eq: ['$users.chargeflowId', '$$chargeflowId'] },
                        { $eq: ['$users.email', '$$email'] },
                      ],
                    },
                  },
                },
                {
                  $project: {
                    'users.firstName': 1,
                    'users.lastName': 1,
                  },
                },
              ],
              as: 'companyDetails',
            },
          },
          // Flatten the joined results
          {
            $unwind: {
              path: '$companyDetails',
              preserveNullAndEmptyArrays: true,
            },
          },
          // Project necessary fields
          {
            $project: {
              _id: 1,
              email: 1,
              chargeflowId: 1,
              name: {
                $cond: {
                  if: {
                    $and: [
                      { $ifNull: ['$companyDetails.users.firstName', false] },
                      { $ifNull: ['$companyDetails.users.lastName', false] },
                    ],
                  },
                  then: {
                    $concat: ['$companyDetails.users.firstName', ' ', '$companyDetails.users.lastName'],
                  },
                  else: 'Chargeflow User',
                },
              },
            },
          },
          // Add pagination with skip and limit
          {
            $skip: offset,
          },
          {
            $limit: 500, // Adjust batch size if needed
          },
        ]).toArray();

        if (accountMappings.length === 0) {
          hasMore = false;
          break;
        }

        if (isDryRun) {
          offset += accountMappings.length;
          continue;
        } else {
          const bulkOps = accountMappings.map(({ _id, name }) => ({
            updateOne: {
              filter: { _id },
              update: {
                $set: {
                  onboardingStatus: 'active',
                  role: 'admin',
                  adminName: 'Chargeflow System',
                  name,
                  isUserMgmtEnriched: true,
                },
              },
            },
          }));

          await accountMappingsCol.bulkWrite(bulkOps);

          offset += accountMappings.length;
        }
      }

      console.log(`Finished ${isDryRun ? 'dry run' : 'wet run'}. Processed ${offset} documents`);

    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }
}

const script = new Script();
script.main();

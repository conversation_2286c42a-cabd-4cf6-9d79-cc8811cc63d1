import { ObjectId } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";

class ConvertProcessorIdToObjectIdScript extends ScriptBase {
  public getName(): string {
    return "Convert processorId from string to ObjectId in processor-meta-data collection";
  }

  public getAuthor(): string {
    return "wsamborski";
  }

  public getTicket(): string {
    return "CHA-15653";
  }

  async dryRun(_context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    const client = await loadMongoClient();

    const results = await client
      .db("chargeflow_api")
      .collection("processor-meta-data")
      .find({ processorId: { $type: "string" } })
      .toArray();

    console.log(`Found ${results.length} documents with string processorId`);

    for (const doc of results) {
      console.log(`Document ${doc._id}: processorId = "${doc.processorId}"`);
    }

    await client.close();
  }

  async wetRunOnSecondary(_context: ScriptContext): Promise<void> {
    await this.doWetRun("processor-meta-data-test");
  }

  async wetRunOnPrimary(_context: ScriptContext): Promise<void> {
    await this.doWetRun("processor-meta-data");
  }

  private async doWetRun(targetCollectionName: string): Promise<void> {
    const client = await loadMongoClient();
    const session = client.startSession();

    try {
      session.startTransaction();

      const sourceCollection = client.db("chargeflow_api").collection("processor-meta-data");
      const targetCollection = client.db("chargeflow_api").collection(targetCollectionName);

      // Find all documents with string processorId
      const documents = await sourceCollection.find({ processorId: { $type: "string" } }).toArray();
      console.log(`Found ${documents.length} documents to update`);

      if (documents.length === 0) {
        console.log("No documents to process");
        await session.commitTransaction();
        return;
      }

      // Convert each document
      const bulkOps = documents.map((doc: any) => {
        if (targetCollectionName === "processor-meta-data") {
          // Update original collection
          return {
            updateOne: {
              filter: { _id: doc._id },
              update: { $set: { processorId: new ObjectId(doc.processorId) } }
            }
          };
        } else {
          // Insert into test collection
          return {
            replaceOne: {
              filter: { _id: doc._id },
              replacement: { ...doc, processorId: new ObjectId(doc.processorId) },
              upsert: true
            }
          };
        }
      });

      const result = await targetCollection.bulkWrite(bulkOps, { session });
      console.log(`Updated ${result.modifiedCount || result.upsertedCount} documents`);

      await session.commitTransaction();

    } catch (error) {
      console.warn("Aborting transaction");
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
      await client.close();
    }
  }
}

const script = new ConvertProcessorIdToObjectIdScript();
script.main();

import { ObjectId, MongoClient } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";

interface ProcessorMetaDataDocument {
  _id: ObjectId;
  processorId: string | ObjectId;
  [key: string]: any;
}

class ConvertProcessorIdToObjectIdScript extends ScriptBase {
  private readonly BATCH_SIZE = 1000;
  private readonly DB_NAME = "chargeflow_api";
  private readonly COLLECTION_NAME = "processor-meta-data";
  private readonly TEST_COLLECTION_NAME = "processor-meta-data-test";

  public getName(): string {
    return "Convert processorId from string to ObjectId in processor-meta-data collection";
  }

  public getAuthor(): string {
    return "wsamborski";
  }

  public getTicket(): string {
    return "CHA-15653";
  }

  private isValidObjectId(id: string): boolean {
    try {
      return ObjectId.isValid(id) && new ObjectId(id).toString() === id;
    } catch {
      return false;
    }
  }

  private async getDocumentCount(client: MongoClient): Promise<number> {
    const collection = client.db(this.DB_NAME).collection(this.COLLECTION_NAME);
    return await collection.countDocuments({
      processorId: { $type: "string" }
    });
  }

  private async getDocumentsBatch(
    client: MongoClient,
    skip: number,
    limit: number
  ): Promise<ProcessorMetaDataDocument[]> {
    const collection = client.db(this.DB_NAME).collection(this.COLLECTION_NAME);

    const documents = await collection.find({
      processorId: { $type: "string" }
    })
    .skip(skip)
    .limit(limit)
    .toArray();

    return documents;
  }

  async dryRun(_context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");

    const client = await loadMongoClient();
    try {
      const totalCount = await this.getDocumentCount(client);
      console.log(`Found ${totalCount} documents with string processorId`);

      if (totalCount === 0) {
        console.log("No documents to process.");
        return;
      }

      let validCount = 0;
      let invalidCount = 0;
      let processedCount = 0;

      for (let skip = 0; skip < totalCount; skip += this.BATCH_SIZE) {
        const batch = await this.getDocumentsBatch(client, skip, this.BATCH_SIZE);
        console.log(`\nProcessing batch ${Math.floor(skip / this.BATCH_SIZE) + 1}/${Math.ceil(totalCount / this.BATCH_SIZE)} (${batch.length} documents)`);

        for (const doc of batch) {
          const processorIdStr = doc.processorId as string;
          processedCount++;

          if (this.isValidObjectId(processorIdStr)) {
            validCount++;
            console.log(`✓ Document ${doc._id}: processorId "${processorIdStr}" is valid ObjectId`);
          } else {
            invalidCount++;
            console.log(`✗ Document ${doc._id}: processorId "${processorIdStr}" is NOT a valid ObjectId`);
          }
        }
      }

      console.log("\n--- Summary ---");
      console.log(`Total documents found: ${totalCount}`);
      console.log(`Documents processed: ${processedCount}`);
      console.log(`Valid ObjectId strings: ${validCount}`);
      console.log(`Invalid ObjectId strings: ${invalidCount}`);
      console.log(`Documents that would be updated: ${validCount}`);

    } finally {
      await client.close();
    }
  }

  async wetRunOnSecondary(_context: ScriptContext): Promise<void> {
    console.log("Running wet run on secondary collection");
    await this.doWetRun(this.TEST_COLLECTION_NAME);
  }

  async wetRunOnPrimary(_context: ScriptContext): Promise<void> {
    console.log("Running wet run on PRIMARY collection - USE WITH CAUTION!");
    await this.doWetRun(this.COLLECTION_NAME);
  }

  private async doWetRun(targetCollectionName: string): Promise<void> {
    const client = await loadMongoClient();
    const session = client.startSession();

    try {
      session.startTransaction();

      const targetCollection = client.db(this.DB_NAME).collection(targetCollectionName);

      const totalCount = await this.getDocumentCount(client);
      console.log(`Found ${totalCount} documents with string processorId`);

      if (totalCount === 0) {
        console.log("No documents to process.");
        await session.commitTransaction();
        return;
      }

      let totalUpdatedCount = 0;
      let totalSkippedCount = 0;

      // Process in batches
      for (let skip = 0; skip < totalCount; skip += this.BATCH_SIZE) {
        const batch = await this.getDocumentsBatch(client, skip, this.BATCH_SIZE);
        console.log(`\nProcessing batch ${Math.floor(skip / this.BATCH_SIZE) + 1}/${Math.ceil(totalCount / this.BATCH_SIZE)} (${batch.length} documents)`);

        const bulkOperations: any[] = [];
        let batchUpdatedCount = 0;
        let batchSkippedCount = 0;

        for (const doc of batch) {
          const processorIdStr = doc.processorId as string;

          if (this.isValidObjectId(processorIdStr)) {
            if (targetCollectionName === this.COLLECTION_NAME) {
              // Update the original document
              bulkOperations.push({
                updateOne: {
                  filter: { _id: doc._id },
                  update: { $set: { processorId: new ObjectId(processorIdStr) } }
                }
              });
            } else {
              // Insert/update in test collection
              const updatedDoc = {
                ...doc,
                processorId: new ObjectId(processorIdStr)
              };
              bulkOperations.push({
                replaceOne: {
                  filter: { _id: doc._id },
                  replacement: updatedDoc,
                  upsert: true
                }
              });
            }

            batchUpdatedCount++;
            console.log(`✓ Prepared update for document ${doc._id}: "${processorIdStr}" -> ObjectId("${processorIdStr}")`);
          } else {
            batchSkippedCount++;
            console.log(`✗ Skipped document ${doc._id}: invalid ObjectId string "${processorIdStr}"`);
          }
        }

        // Execute bulk operations for this batch
        if (bulkOperations.length > 0) {
          const result = await targetCollection.bulkWrite(bulkOperations, { session });
          console.log(`Batch bulk write result: ${result.modifiedCount || result.upsertedCount} documents processed`);
        }

        totalUpdatedCount += batchUpdatedCount;
        totalSkippedCount += batchSkippedCount;

        console.log(`Batch summary: ${batchUpdatedCount} updated, ${batchSkippedCount} skipped`);
      }

      console.log("\n--- Final Results ---");
      console.log(`Total documents updated: ${totalUpdatedCount}`);
      console.log(`Total documents skipped (invalid ObjectId): ${totalSkippedCount}`);

      console.log("Committing transaction...");
      await session.commitTransaction();

    } catch (error) {
      console.warn("Aborting transaction due to error");
      await session.abortTransaction();
      console.error(error);
      throw error;
    } finally {
      session.endSession();
      await client.close();
    }
  }
}

const script = new ConvertProcessorIdToObjectIdScript();
script.main();

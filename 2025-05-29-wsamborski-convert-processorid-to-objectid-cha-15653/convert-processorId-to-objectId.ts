//TODO

import { ObjectId } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";

class ConvertProcessorIdToObjectIdScript extends ScriptBase {
  public getName(): string {
    return "Convert processorId from string to ObjectId in processor-meta-data collection";
  }

  public getAuthor(): string {
    return "wsamborski";
  }

  public getTicket(): string {
    return "CHA-15653";
  }

  async dryRun(_context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    const client = await loadMongoClient();

    const results = await client
      .db("chargeflow_api")
      .collection("processor-meta-data")
      .find({ processorId: { $type: "string" } })
      .toArray();

    console.log(`Found ${results.length} documents with string processorId`);

    for (const doc of results) {
      console.log(`Document ${doc._id}: processorId = "${doc.processorId}"`);
    }

    await client.close();
  }

  async wetRunOnPrimary(_context: ScriptContext): Promise<void> {
    await this.doWetRun();
  }

  async wetRunOnSecondary(_context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async doWetRun(): Promise<void> {
    const client = await loadMongoClient();
    const session = client.startSession();

    try {
      session.startTransaction();

      const collection = client.db("chargeflow_api").collection("processor-meta-data");

      const documents = await collection.find({ processorId: { $type: "string" } }).toArray();
      console.log(`Found ${documents.length} documents to update`);

      if (documents.length === 0) {
        console.log("No documents to process");
        await session.commitTransaction();
        return;
      }

      const bulkOps = documents.map((doc: any) => ({
        updateOne: {
          filter: { _id: doc._id },
          update: { $set: { processorId: new ObjectId(doc.processorId) } }
        }
      }));

      const result = await collection.bulkWrite(bulkOps, { session });
      console.log(`Updated ${result.modifiedCount} documents`);

      await session.commitTransaction();

    } catch (error) {
      console.warn("Aborting transaction");
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
      await client.close();
    }
  }
}

const script = new ConvertProcessorIdToObjectIdScript();
script.main();

import { MongoClient, Db } from 'mongodb';
import { DynamoDB } from "@aws-sdk/client-dynamodb";
import { AttributeValue } from "@aws-sdk/client-dynamodb/dist-types/models";

export interface Logger {
    log: (message: string) => void;
}

export class AddProcessorIdToAfterpayCachedDisputeKey {
    private readonly DB_NAME = 'chargeflow_api';
    private readonly DISPUTES_COLLECTION_NAME = 'disputes';

    private readonly db: Db
    private readonly ddbClient: DynamoDB

    constructor(
        mongoClient: MongoClient,
        ddbClient: DynamoDB,
        private readonly logger: Logger,
    ) {
        this.db = mongoClient.db(this.DB_NAME);
        this.ddbClient = ddbClient;
    }

    async execute(ddbTableName: string, dryRun: boolean): Promise<void> {
        const descr = await this.ddbClient.describeTable({ TableName: ddbTableName });
        if (!descr.Table) {
            throw new Error(`Table ${ddbTableName} does not exist.`);
        }
        this.logger.log(`Table ${ddbTableName} has ${descr.Table.ItemCount} items.`);
        const table = await this.ddbClient.scan({ TableName: ddbTableName });
        const items = table.Items || []
        await Promise.allSettled(items.map(async (item) => {
            const chargeflowId =  item.chargeflowId?.S || item.chageflowId?.S;
            if (!item.combinedDisputeKey || !chargeflowId || !item.nativeDisputeId) {
                this.logger.log(`Skipping processing item ${JSON.stringify(item)}`);
                return;
            }
            const processorId = await this.getProcessorId(item.nativeDisputeId.S!);
            if (!processorId){
                return;
            }
            const newCombinedKey = `${item.chargeflowId.S}/${processorId}/afterpay/${item.nativeDisputeId.S}`;
            this.logger.log(`Old combinedDisputeKey ${JSON.stringify(item.combinedDisputeKey.S)}`);
            this.logger.log(`New combinedKey ${JSON.stringify(newCombinedKey)}`);
            const newItem = {
                ...item,
                chargeflowId: { S: chargeflowId },
                combinedDisputeKey: { S: newCombinedKey },
                processorId: { S: processorId }
            };
            this.logger.log(`New combinedItem ${JSON.stringify(newItem)}`);
            const putItemParams = {
                TableName: ddbTableName,
                Item: newItem
            };
            const deleteItemParams = {
                TableName: ddbTableName,
                Key: {
                    'combinedDisputeKey': item.combinedDisputeKey
                }
            };
            if (dryRun){
                this.logger.log(`Dry mode enabled, no changes made`);
                return;
            }
            this.logger.log(`Putting item`);
            await this.ddbClient.putItem(putItemParams);
            this.logger.log(`Put successful, deleting old item`);
            await this.ddbClient.deleteItem(deleteItemParams);
            this.logger.log(`Deleted old item`);
        }));
    }

    private async getProcessorId(nativeDisputeId: string): Promise<string | null> {
        const query = {
            "dispute.id": nativeDisputeId,
            "dispute.processorId": { $exists: true, $ne: null }
        };
        const result = await this.db
            .collection(this.DISPUTES_COLLECTION_NAME)
            .findOne(query, { projection: { "dispute.processorId": 1, _id: 0 } });
        if (!result?.dispute.processorId){
            this.logger.log(`Couldn't find processorId for ${nativeDisputeId}`)
            return null;
        }
        this.logger.log(`Found ${result?.dispute.processorId} for ${nativeDisputeId}`)
        return result!.dispute.processorId;
    }
}

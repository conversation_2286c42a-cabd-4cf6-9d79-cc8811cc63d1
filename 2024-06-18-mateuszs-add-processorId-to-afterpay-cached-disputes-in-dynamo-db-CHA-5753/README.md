# Purpose
This script adds processorId to existing afterpay cached dispute keys in DynamoDB.

https://linear.app/chargeflow/issue/CHA-5753/add-processorid-to-created-dispute-afterpay-leftover

## Motivation
Afterpay dispute ingestion relay on cached disputes in DynamoDB. The processorId is required to be added to the cached disputes to be able to identify the disputes that are ingested by the Afterpay processor.

## Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

1. Set the following environment variables in CWD:
```
MONGO_URI=<db connection string>
DDB_TABLE_NAME=<table name>
AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, (AWS_SESSION_TOKEN if required) - AWS access
```

2. Install deps from CWD: `npm i`

3. Run the migration in `wet-primary` mode once everything is prepared.


### Modes
dry - does not update anything, writes the results to the console
wet-secondary - not implemented
wet-primary - executes the actual migration

### How this script was tested
- [x] test migration against `dev` db
- [x] test migration against `test` db

import dotenv from "dotenv";
import { MongoClient } from "mongodb";
import { ScriptBase } from "../shared/script-base";
import {AddProcessorIdToAfterpayCachedDisputeKey} from "./logic";
import {loadMongoClient} from "../shared/mongo-config";
import {DynamoDB} from "@aws-sdk/client-dynamodb";

dotenv.config();

export class Script extends ScriptBase {
    public getName(): string {
        return "Add processorId to afterpay cached disputes in DynamoDB";
    }

    public getAuthor(): string {
        return 'mateuszs';
    }

    public getTicket(): string {
        return "CHA-5753";
    }

    async dryRun(): Promise<void> {
        const dryRun = true;

        await this.execute(dryRun);
    }

    async wetRunOnPrimary(): Promise<void> {
        const dryRun = false;

        await this.execute(dryRun);
    }

    wetRunOnSecondary(): Promise<void> {
        throw new Error("Method not implemented.");
    }

    private async execute(dryRun: boolean): Promise<void> {
        const { ddbTableName} = this.getParamsFromEnvVars();
        console.log(ddbTableName)
        const mongoClient = await loadMongoClient();
        const ddbClient = new DynamoDB();
        const logger = {
            log: (message: string) => {
                console.log(message);
            },
        }

        const logic = new AddProcessorIdToAfterpayCachedDisputeKey(
            mongoClient,
            ddbClient,
            logger,
        );

        await logic.execute(ddbTableName, dryRun);
    }

    private getParamsFromEnvVars(): { ddbTableName: string } {
        const ddbTableName = process.env.DDB_TABLE_NAME!;
        return {
            ddbTableName
        };
    }
}

const script = new Script();
script.main();

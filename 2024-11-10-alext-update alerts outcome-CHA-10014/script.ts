import { MongoClient, WithId, Document } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext, ScriptMode } from "../shared/script-base";
import { AwsEventBridge } from "@chargeflow-team/chargeflow-utils-sdk";
import { AlertUpdatedEventDetailSchema, UpdateAlertOutcomePayloadSchema } from "@chargeflow-team/events-infra";

export class SendToUpdateAlertOutcome extends ScriptBase {
    public getName(): string {
        return 'Send to update alert outcome';
    }

    public getAuthor(): string {
        return 'Alex Tkach';
    }
    
    public getTicket(): string {
        return 'CHA-10014';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.logic(context);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context);
    }

    async wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method is not implemented.');
    }

    async logic(ctx: ScriptContext) {
        const mode = (ctx.args[0] ?? 'dry') as ScriptMode;
        try {
            const batchSize = process.env.BATCH_SIZE ? parseInt(process.env.BATCH_SIZE) : 100;
            const client: MongoClient = await loadMongoClient();

            let skip = 0;
            let totalAlertsToSend = 0;
            let totalAlertsSent = 0;
            let alerts;
            
            do {
                alerts = await this.getAlerts(client, skip, batchSize);
                if (alerts.length === 0) {
                    break;
                }
                totalAlertsToSend += alerts.length;

                if (mode !== 'dry') {
                    const promises = alerts.map((alert) => {
                        return AwsEventBridge.putEventWithValidation(
                            {
                                alertId: alert.alertId,
                                airtableStatus: alert.airtableStatus,
                                type: alert.type,
                                dateCreated: alert.dateCreated.toString(),
                            },
                            AlertUpdatedEventDetailSchema,
                            'ALERT_UPDATED',
                            'utility-scripts',
                            process.env.ALERTS_EVENT_BUS_NAME as string,
                        );
                    });
                    const results = await Promise.allSettled(promises);

                    const errors = results.reduce((acc: any[], result) => {
                        if (result.status === 'rejected') {
                          acc.push(result.reason);
                        }
                        return acc;
                      }, []);
                    
                    const alertsSentAmount = results.length - errors.length;
                    totalAlertsSent += alertsSentAmount;
                }
                skip += batchSize;
            } while (alerts.length > 0);

            if (mode === 'dry') {
                console.log('Total alerts to send: %s', totalAlertsToSend);
            } else {
                console.log('Total alerts to send: %s', totalAlertsToSend);
                console.log('Total disputes sent: %s', totalAlertsSent);
            }
        } catch (error) {
            console.error('Error: ', error);
        }
    }

    private async getAlerts(client: MongoClient, skip: number, batchSize: number): Promise<WithId<Document>[]> {
        try {
            const tenDaysAgo = new Date();
            tenDaysAgo.setDate(tenDaysAgo.getDate() - 10); 
    
            return client
                .db('chargeflow_api')
                .collection('alerts')
                .find(
                    {
                        accountId: null,
                        history: {
                            $elemMatch: {
                                field: "airtableStatus",
                                date: { $gte: tenDaysAgo }
                            }
                        },
                        service: {$ne: 'rdr'},
                        type: {$ne: 'fraud-notification'},
                    },
                    { 
                        projection: {
                            "alertId": 1,
                            "chargeflowId": 1,
                            "accountId": 1,
                            "businessUnitId": 1,
                            "service": 1,
                            "transactionId": 1,
                            "airtableStatus": 1,
                            "type": 1,
                            "dateCreated": 1,
                            "reason": 1
                          }
                          
                    }
                )
                .skip(skip)
                .limit(batchSize)
                .toArray();
        } catch (error) {
            console.error('Error fetching alerts: ', error);
            throw error;
        }
    }
}

const script = new SendToUpdateAlertOutcome();
script.main();
# Purpose
This script gathers and puts relevant alerts to ALerts Event bus thus triggering UpdateAlertOutcomeFunction of chargeback-hit stack

https://linear.app/chargeflow/issue/CHA-10014/write-a-script-to-update-alerts-outcome-in-cb-hit

## Motivation
Retroactively update outcome of alerts having accoundId: null

## Running this script
`npx ts-node ./script.ts <dry or wet-primary>`

## Required env variables
MONGO_URI=<mongo_uri_string>
BATCH_SIZE=100
ALERTS_EVENT_BUS_NAME=prod-events-infra-AlertsBus

### Modes
dry: does not send anything, writes to console the total amount of alerts to be sent
wet-primary: puts alerts events on Alerts Event bus for futher redirection to UpdateAlertOutcomeFunction

### How this script was tested
Both modes were tested on dev mongo DB cluster
dry mode also tested on prod mongo DB cluster
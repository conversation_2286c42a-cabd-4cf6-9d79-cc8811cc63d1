
import { MongoClient } from 'mongodb';

function throwMissingParameter(parameterName: string): never {
    throw new Error(`${parameterName} is not set`);
}

function checkParameters(params: {}): void {
    Object.entries(params).forEach(([key, value]) => {
        if (!value) {
            throwMissingParameter(key);
        }
    });
}

export async function loadMongoClient(uri?: string): Promise<MongoClient> {
    const MONGO_URI = uri || process.env.MONGO_URI;
    if (!MONGO_URI) {
        throw new Error('MONGO_URI is not set');
    }
    console.log(`Connecting to uri`);
    const client = await MongoClient.connect(MONGO_URI)
        .catch((err: Error) => {
            console.trace("MongoClient.connect failed: %s", err);
            throw err;
        });
    console.log(`Connected to uri`);
    return client;
};

export async function loadMongoClientWithAuthParameters(uri?: string): Promise<MongoClient> {
    const MONGO_HOST_URI = process.env.MONGO_HOST_URI as string;
    const AWS_SESSION_TOKEN = process.env.AWS_SESSION_TOKEN as string;
    const ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID as string;
    const SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY as string;
    checkParameters({ MONGO_HOST_URI, AWS_SESSION_TOKEN, ACCESS_KEY_ID, SECRET_ACCESS_KEY});
    console.log(`Connecting to ${MONGO_HOST_URI}`);
    const client = await MongoClient.connect(MONGO_HOST_URI, {
        auth: { username: ACCESS_KEY_ID, password: SECRET_ACCESS_KEY },
        authMechanism: 'MONGODB-AWS', authMechanismProperties: { AWS_SESSION_TOKEN: AWS_SESSION_TOKEN }
    })
        .catch((err: Error) => {
            console.trace("MongoClient.connect failed: %s", err);
            throw err;
        });
    console.log(`Connected to ${MONGO_HOST_URI}`)
    return client;
};

// This function is used in the script 2024-05-29-aleksandran-link-afterpay-orders-CHA-5090/script.ts, otherwise MONGO_URI is used as production URI
export async function loadMongoClientProdAfterpay(): Promise<MongoClient> {
    const MONGO_URI_PROD = process.env.PROD_MONGO_URI ?? '';
    console.log(`Connecting to ${MONGO_URI_PROD}`);
    const client = await MongoClient.connect(MONGO_URI_PROD)
        .catch((err: Error) => {
            console.trace("MongoClient.connect failed: %s", err);
            throw err;
        });
    console.log(`Connected to ${MONGO_URI_PROD}`)
    return client;
};

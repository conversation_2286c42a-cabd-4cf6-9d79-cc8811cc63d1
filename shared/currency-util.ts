import { getConversionRates } from "./s2s-service";

export async function getAmountInUsd(amount: number, currency: string): Promise<number>{
  if (!amount) {
      throw new Error(`Missing amount`);
  }

  if (!currency) {
      throw new Error(`Missing currency`);
  }

  let usdQuote = 1;
  if (currency !== "USD") {
      const rates = await getConversionRates(currency);
      if (!rates || !rates[currency]) {
          throw new Error(
              `Failed to get USD quote` + ` currency: ${currency}`
          );
      }
      usdQuote = rates[currency];
  }


  const result = getTwoDigitsNumber(Number(amount) / Number(usdQuote));
  return result
}

function getTwoDigitsNumber(amount: number) {
  return Number(Number(amount).toFixed(2));
}
const relevantOrderSource = "shopify";
const irrelevantOrderProcessor = "afterpay";

export async function extractDate(dateArg: string): Promise<Date> {
    if (!dateArg) {
        throw new Error('Missing date argument');
    }
    const date = new Date(dateArg);
    if (date.toString() === 'Invalid Date') {
        throw new Error(`Invalid date: ${dateArg}`);
    }
    return date;
}

export async function getLinkedDisputesQuery(dateCreatedFrom: Date, dateCreatedTo: Date, processorName?: string) {
    const query: any = {
        'dateCreated': {
            '$gte': dateCreatedFrom,
            '$lte': dateCreatedTo,
        },
        'order.id': {
            $ne: null
        },
        'order.source': relevantOrderSource,
        'dispute.processor': {
            $ne: irrelevantOrderProcessor,
        }
    };
    
    if (processorName) {
        query['dispute.processor']['$eq'] = processorName;
    }
    
    return query;
}

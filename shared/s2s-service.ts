import { Logger, S2SRequest } from '@chargeflow-team/chargeflow-utils-sdk';

export async function sendS2SRequest({
    url,
    method,
    body = undefined,
}: {
    url: string;
    method: string;
    body?: unknown;
}) {
    const { AWS_REGION } = process.env;
    try {
        const s2sRequest = new S2SRequest();
        const typedBody = body as BodyInit;
        const response = await s2sRequest.send(
            url,
            { method, body: typedBody, region: AWS_REGION },
        );
        return response;

    } catch (error) {
        Logger.error('BadRequestError', `An error occurred ${JSON.stringify(error)}`);
        throw error;
    }
}

export async function getConversionRates(currency: string) {
    console.log('getConversionRates', process.env.AWS_CONVERSION_APIG_SERVICE_URL);
    const url = `${process.env.AWS_CONVERSION_APIG_SERVICE_URL}?currencies=${currency}`;
    return await sendS2SRequest({
        url,
        method: 'GET',
    }).then(res => res.rates);
}

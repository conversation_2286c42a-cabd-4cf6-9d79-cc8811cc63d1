import { randomUUID } from "crypto";
import dotenv from 'dotenv';

export interface ScriptContext {
  execution_id: string;
  name: string;
  author: string;
  ticket: string;
  runDate: Date;
  args: string[];
}

export type ScriptMode = "dry" | "wet-secondary" | "wet-primary";

export abstract class ScriptBase {
  protected mode: ScriptMode = "dry";
  public abstract getName(): string;
  public abstract getAuthor(): string;
  public abstract getTicket(): string;

  abstract dryRun(context: ScriptContext): Promise<void>;
  abstract wetRunOnSecondary(context: ScriptContext): Promise<void>;
  abstract wetRunOnPrimary(context: ScriptContext): Promise<void>;

  public main() {
    const context: ScriptContext = {
      execution_id: randomUUID(),
      name: this.getName(),
      author: this.getAuthor(),
      ticket: this.getTicket(),
      runDate: new Date(),
      args: process.argv.slice(2),
    };
    const mode = (process.argv[2] || "dry").toLowerCase() as ScriptMode

    this.mode = mode;

    let entryPoint = this.dryRun;
    if (mode === "wet-secondary") {
      entryPoint = this.wetRunOnSecondary;
    } else if (mode == "wet-primary") {
      entryPoint = this.wetRunOnPrimary;
    } else if (mode != "dry") {
      console.error(
        'Invalid mode. Use "dry" or "wet-secondary" or "wet-primary"'
      );
      process.exit(1);
    }
    try {
      console.log(`Running in ${mode} mode`);
      console.log("\tExecution ID:", context.execution_id);
      console.log("\tName:", context.name);
      console.log("\tAuthor:", context.author);
      console.log("\tTicket:", context.ticket);
      console.log("\tRun date:", context.runDate);
      console.log("\tArgs:", context.args);
      console.log("----------------------------------------");
    dotenv.config();
      entryPoint.apply(this,[context])
        .then(() => {
          console.log("\tEnd date:", new Date());
          console.log(`Script ${context.execution_id} completed successfully`);
          process.exit(0);
        })
        .catch((ex) => {
          console.log("\tEnd date:", new Date());
          //Promise rejected
          console.log(
            `Script ${context.execution_id} exited with error: ${ex}`
          );
          debugger;
          console.error(ex);
          process.exit(1);
        });
    } catch (ex) {
      console.log("\tEnd date:", new Date());
      //Error in the entry point
      console.log(
        `Script ${context.execution_id} exited with entry-point error: ${ex}`
      );
      debugger;
      console.error(ex);
      process.exit(1);
    }
  }
}
import { Logger, RDSClient } from "@chargeflow-team/chargeflow-utils-sdk";

interface IDbService {
  query<T>(queryString: string, params: any[]): Promise<T[]>
}


export class PgService implements IDbService {
  private rdsClient: RDSClient;

  constructor() {
    const connectionString = process.env.PG_READER_CONNECTION_STRING_DEV as string;

    this.rdsClient = new RDSClient({ connectionString });
  }

  public async query<T>(queryString: string, params: any[] = []): Promise<T[]> {
    try {
      await this.rdsClient.connect();
      const result = await this.rdsClient.query<T>(queryString, params);
      return result;
    } catch (error) {
      Logger.error('InternalServerError', 'Error running query', { error, queryString });
      throw new Error('No data returned from Postgres');
    } finally {
      await this.rdsClient.disconnect();
    }
  }
}
import rockset, { MainApi } from '@rockset/client';
import { knex, Knex } from 'knex';

export default class RocksetClient {
  private rocksetClient: MainApi;
  public readonly knexClient: Knex;
  constructor(rocksetApiKey?: string, rocksetEndpoint?: string) {
    this.rocksetClient = rockset(
      rocksetApiKey ?? process.env.ROCKSET_API_KEY ?? '',
      rocksetEndpoint ?? process.env.ROCKSET_ENDPOINT ?? ''
    );
    this.knexClient = knex({
      client: 'pg',
    });
  }

  public queryBuilder() {
    return this.knexClient.queryBuilder();
  }

  public async executeQuery<T = any>(query: string): Promise<T> {
    const queryResult = await this.rocksetClient.queries.query({
      sql: {query}
    });

    const result = await queryResult.results ?? [];
    return result as T
  }
}


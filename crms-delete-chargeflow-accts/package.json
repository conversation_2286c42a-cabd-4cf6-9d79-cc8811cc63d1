{"name": "data-unification", "description": "Data Unification service to upload manually collected disputes and synchronize them with one source of thruth", "version": "1.0.0", "private": true, "scripts": {"delete-chargeflow-crms": "ts-node delete-chargeflow-accounts-in-crm.ts"}, "devDependencies": {"aws4": "^1.12.0", "dotenv": "^16.4.1", "npm-check-updates": "^16.10.12", "npm-run-all": "^4.1.5", "proxyquire": "^2.1.3", "ts-migrate": "^0.1.35", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "optionalDependencies": {"mongodb": "^5.6.0"}}
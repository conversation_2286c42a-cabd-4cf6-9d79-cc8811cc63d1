import dotenv from 'dotenv';
import {  MongoClient  } from 'mongodb';

dotenv.config();

const MONGO_URI = process.env.MONGO_URI ?? '';

export async function loadMongoClient(): Promise<MongoClient> {
    const client = await MongoClient.connect(MONGO_URI)
        .catch((err: Error) => {
            console.trace("MongoClient.connect failed: %s", err);
            throw err;
        });
    return client;
};

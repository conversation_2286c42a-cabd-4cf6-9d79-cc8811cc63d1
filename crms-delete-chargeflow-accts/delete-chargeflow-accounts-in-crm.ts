import { loadMongoClient } from "./mongo-config";

async function deleteChargeflowCrms() {
    const client = await loadMongoClient();
    const session = client.startSession();
    const collection = client.db('chargeflow_api').collection('crms');
    try {
        session.startTransaction();
        const query = {'Email 1': { $regex: '.*@chargeflow.io' }};
        const count = await collection.countDocuments(query);
        console.log('Found',count,'documents.');
        const deleteResult = await collection.deleteMany(query);
        await session.commitTransaction();
        console.log('Deleted', deleteResult.deletedCount, 'documents');
        return deleteResult;
    } catch (ex) {
        await session.abortTransaction();
        throw ex;
    } finally {
        session.endSession();
    }
}

deleteChargeflowCrms();

{
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Debug Current TypeScript File with ts-node",
        "type": "node",
        "request": "launch",
        "program": "${workspaceFolder}/node_modules/ts-node/dist/bin.js",
        "args": [
          "${file}",
          "dry"
        ],
        "runtimeArgs": [
          "--nolazy",
        ],
        "sourceMaps": true,
        "cwd": "${fileDirname}",
        "internalConsoleOptions": "openOnSessionStart",
        "skipFiles": ["<node_internals>/**", "node_modules/**"]
      }
    ]
  }
  
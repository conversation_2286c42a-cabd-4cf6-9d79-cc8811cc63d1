import { ScriptBase } from "../shared/script-base";
import {
  SchedulerClient,
  ListSchedulesCommand,
  DeleteScheduleCommand,
  GetScheduleCommand,
  ListSchedulesCommandOutput,
  ScheduleSummary,
  GetScheduleOutput,
} from "@aws-sdk/client-scheduler";

const TARGET_FILTER = "prod-disputes-service";
const NAME_PREFIX = "CreateRepeatableTask-";

export class Script extends ScriptBase {
  public getName(): string {
    return "Clean up completed task schedules";
  }

  public getAuthor(): string {
    return "dominikj";
  }

  public getTicket(): string {
    return "CHA-6053";
  }

  async dryRun(): Promise<void> {
    const dryRun = true;
    await this.execute(dryRun);
  }

  async wetRunOnPrimary(): Promise<void> {
    const dryRun = false;
    await this.execute(dryRun);
  }

  wetRunOnSecondary(): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async execute(isDryRun = true): Promise<void> {
    console.log(`Looking for Schedules with target: ${TARGET_FILTER}`);

    // Find Schedules for removal
    const schedulerClient = new SchedulerClient();
    let nextToken: string | undefined = undefined;

    let schedules: ScheduleSummary[] = [];

    do {
      const response: ListSchedulesCommandOutput = await schedulerClient.send(
        new ListSchedulesCommand({
          NamePrefix: NAME_PREFIX,
          NextToken: nextToken,
        })
      );
      schedules = schedules.concat(response.Schedules || []);
      nextToken = response.NextToken;
    } while (nextToken);

    const schedulesWithTarget = schedules.filter((schedule) =>
      schedule?.Target?.Arn?.includes(TARGET_FILTER)
    );

    console.log(
      `Found ${schedulesWithTarget.length} target schedules (before end date filter)`
    );

    console.log(
      "Filtering out schedules with end date in the future (this might take a moment)"
    );

    const completedSchedules: GetScheduleOutput[] = [];

    // Not using promise all to avoid hitting the rate limit
    for (const schedule of schedulesWithTarget) {
      const response = await schedulerClient.send(
        new GetScheduleCommand({
          Name: schedule.Name,
          GroupName: schedule.GroupName,
        })
      );

      if (response?.EndDate && response.EndDate < new Date()) {
        completedSchedules.push(response);
      }

      // Show progress
      process.stdout.write(
        ` ${Math.round(
          (completedSchedules.length / schedulesWithTarget.length) * 100
        )}%\r`
      );
    }

    console.log("");

    console.log("Found schedules for deletion:", completedSchedules.length);

    for (const schedule of completedSchedules) {
      console.log(
        `Name: ${schedule.Name}, GroupName: ${schedule.GroupName}, EndDate: ${schedule.EndDate}`
      );
      if (!isDryRun) {
        await schedulerClient.send(
          new DeleteScheduleCommand({
            Name: schedule.Name,
            GroupName: schedule.GroupName,
          })
        );
      }
    }
  }
}

const script = new Script();
script.main();

# Purpose
This script removes isActive field from all docs found in merchantProfiles collection

https://linear.app/chargeflow/issue/CHA-5586/remove-isactive-field-from-merchantprofiles-collection

## Motivation
Removing unused field

## Running this script
`npx ts-node ./script.ts <dry or wet-primary>`

## Required env variables
MONGO_URI=<mongo_uri_string>
BATCH_SIZE=100

### Modes
dry: does not update anything, writes the total amount of docs to update to the console 
wet-primary: unsets isActive field from all docs of merchantProfiles collection

### How this script was tested
Both modes were tested on dev mongo DB cluster
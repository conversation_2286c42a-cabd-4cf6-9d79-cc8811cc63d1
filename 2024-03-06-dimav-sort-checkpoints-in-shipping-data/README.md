# Purpose:
The purpose of this script is to sort checkpoints data in disputes shippingData after we found out sometimes it is not sorted.

# Motivation:
Often in the templates and the code in general we refer to the first or last element of the checkpoints and also we show the package journey in a chronological order.
This script is intended to be executed following system updates that ensure the ongoing sorting of the checkpoints array in the shippingData object. (which was already done)

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**


### How this script was tested
[x] Wet run against dev & test
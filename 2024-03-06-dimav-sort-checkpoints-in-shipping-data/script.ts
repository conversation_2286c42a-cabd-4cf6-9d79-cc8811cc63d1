import dotenv from 'dotenv';
import { AnyBulkWriteOperation, Document, ObjectId } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';

dotenv.config();

const QUERY_GET_DISPUTES_WITH_RAW_SHIPMENT_PICKUP_DATE = {
    'shippingData.0.rawData.shippingData.shipment_pickup_date': {
        $exists: 1,
        $type: 'string',
    },
};

const BATCH_SIZE = 1000;
type Checkpoint = {
    checkpointTime: Date
}
export class ScriptAddPickUpDateToShippingObject extends ScriptBase {
    public getName(): string {
        return 'Sort Checkpoints in Shipping Data';
    }

    public getAuthor(): string {
        return 'Dima Vinogradov';
    }

    public getTicket(): string {
        return 'CHA-3060';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log("Running in dry run mode (not changing anything)");
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {
        let lastId: ObjectId | undefined;
        let processedCount = 0;
        const client = await loadMongoClient();
        const collection = client.db('chargeflow_api').collection('disputes');

        const isCheckpointsSorted = (checkpoints: any[]): boolean => {
            for (let i = 0; i < checkpoints.length - 1; i++) {
                if (new Date(checkpoints[i].checkpointTime) > new Date(checkpoints[i + 1].checkpointTime)) {
                    return false;
                }
            }
            return true;
        };

        const sortCheckpoints = (checkpoints: any[]): any[] => {
            return checkpoints.sort((a, b) => new Date(a.checkpointTime).getTime() - new Date(b.checkpointTime).getTime());
        };

        while (true) {
            const query: any = { "shippingData.0.checkpoints.0": { $exists: true } };
            if (lastId) {
                query._id = { $gt: lastId };
            }

            const documents = await collection
                .find(query, {
                    projection: {
                        _id: 1,
                        'shippingData': 1,
                    },
                })
                .sort({ _id: 1 })
                .limit(BATCH_SIZE)
                .toArray();

            if (documents.length === 0) {
                console.log("No more documents to process.");
                break;
            }

            let bulkOps: AnyBulkWriteOperation<Document>[] = [];

            documents.forEach(doc => {
                const checkpoints = doc.shippingData[0].checkpoints;
                if (!isCheckpointsSorted(checkpoints)) {
                    if (isDryRun) {
                        console.log(`Document ID: ${doc._id}`);
                        console.log('Current order:', checkpoints.map((cp:Checkpoint) => cp.checkpointTime));
                        console.log('Correct order:', sortCheckpoints(checkpoints).map((cp:Checkpoint) => cp.checkpointTime));
                        console.log('-'.repeat(50));
                    } else {
                        bulkOps.push({
                            updateOne: {
                                filter: { _id: doc._id },
                                update: {
                                    $set: {
                                        'shippingData.0.checkpoints': sortCheckpoints(checkpoints),
                                    },
                                },
                            },
                        });
                    }
                }
            });

            if (!isDryRun && bulkOps.length > 0) {
                const result = await collection.bulkWrite(bulkOps);
                processedCount += result.modifiedCount;
                console.log(
                    `${new Date().toISOString()}: Processed ${documents.length} documents. Total corrected so far: ${processedCount}`,
                );
            }

            lastId = documents[documents.length - 1]._id;
        }

        if (!isDryRun) {
            console.log(`Total documents corrected: ${processedCount}`);
        }
    }

}

const script = new ScriptAddPickUpDateToShippingObject();
script.main();

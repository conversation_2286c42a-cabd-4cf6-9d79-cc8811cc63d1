import {ScriptBase, ScriptContext} from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import fs from 'fs';


const filter: any = {
    "dispute.processor": "afterpay",
}
const updateAction =  [
    {
      $set: {
        "dispute.stage": "Inquiry"
      }
    }
  ]

export class TransformDisputes extends ScriptBase {
    public getName(): string {
       return 'Capitalize first letter on dispute.stage'
    }
    public getAuthor(): string {
       return '<PERSON><PERSON>';
    }
    public getTicket(): string {
        return 'CHA-4291'
    }

    private storeDisputesInFile(disputes: any[], folder: string) {
        this.createFolder(folder)
    
        disputes.forEach(item => {
            const fileContent = JSON.stringify(item, null, 4);
            const fileName = `${item._id}.json`;
            fs.writeFileSync(folder + '/' + fileName, fileContent);
        });
    }
    
    private createFolder(folderName: string, subFolderName: string | null = null) {
        if (!fs.existsSync(folderName)) {
            fs.mkdirSync(folderName, { recursive: true });
            if (subFolderName) {
                fs.mkdirSync( folderName + '/' + subFolderName);
            }
        } else if (subFolderName) {
            fs.mkdirSync( folderName + '/' + subFolderName);
        }
    }

    async dryRun(context: ScriptContext): Promise<void> {
        const client = await loadMongoClient();
        const session = client.startSession();
        try {
            session.startTransaction();
            const results = await client
                .db("chargeflow_api")
                .collection("disputes")
                .find(filter).toArray();

            this.storeDisputesInFile(results, "Afterpay backup");

            results.forEach( dispute => {
                console.log(JSON.stringify(dispute, null, 2));
            });

            // This is here because it is easier to see
            console.log(`Found ${results.length} disputes to be transformed`);

        } catch (err) {
            console.warn('Aborting transaction');
            await session.abortTransaction();
            console.error(err);
            throw err;
        } finally {
            await session.endSession();
        }
    }
    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error("Method not implemented.");
    }
    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        const client = await loadMongoClient();
        const session = client.startSession();
        try {
            session.startTransaction();
            const disputes = await client
                .db("chargeflow_api")
                .collection("disputes")
                .find(filter).toArray();

            this.storeDisputesInFile(disputes, "Afterpay backup");

            const results = await client
                .db("chargeflow_api")
                .collection("disputes")
                .updateMany(filter, updateAction);



            console.log(`transformed ${results.modifiedCount} records, successful = ${results.acknowledged}`);
        } catch (err) {
            console.warn('Aborting transaction');
            await session.abortTransaction();
            console.error(err);
            throw err;
        } finally {
            await session.endSession();
        }
    }
}

const script = new TransformDisputes();
script.main();
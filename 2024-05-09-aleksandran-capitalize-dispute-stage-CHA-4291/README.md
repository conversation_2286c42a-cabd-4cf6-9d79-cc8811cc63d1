# Purpose
This script should capitalize first letter on dispute.stage field for afterpay disputes.

https://linear.app/chargeflow/issue/CHA-4291/filter-afterpay-disputes-by-stage

## Motivation
In order for data to be consistent, and also in order for filtering on the webapp to work, we need to change dispute.stage from "inquiry" to "Inquiry"

## Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console
wet-secondary - does not update the source table, writes results to another collection (disputes_test), adding documents as needed
wet-primary - UPDATES THE DISPUTES TABLE. Use with extreme caution!

### How this script was tested
[x] Run locally with development database
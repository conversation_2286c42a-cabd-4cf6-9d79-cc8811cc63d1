import dotenv from "dotenv";
import { MongoClient } from "mongodb";
import { DataAccess } from '@chargeflow-team/data-access';
import { ScriptBase } from "../shared/script-base";
import packageJson from './package.json';
import { SetProperShopRegistrationTypeMigrationLogic } from "./logic";

dotenv.config();

export class Script extends ScriptBase {
    public getName(): string {
        return packageJson.name;
    }

    public getAuthor(): string {
        return packageJson.author;
    }

    public getTicket(): string {
        return 'CHA-2082';
    }

    async dryRun(): Promise<void> {
        const dryRun = true;
        const batchSize = this.getBatchSize();

        await this.execute(batchSize, dryRun);
    }
    
    async wetRunOnPrimary(): Promise<void> {
        const dryRun = false;
        const batchSize = this.getBatchSize();

        await this.execute(batchSize, dryRun);
    }

    wetRunOnSecondary(): Promise<void> {
        throw new Error("Method not implemented.");
    }

    private async execute(batchSize: number, dryRun: boolean): Promise<void> {
        const mongoClient = await MongoClient.connect(process.env.MONGO_URI!);
        const dataAccess = await DataAccess.initialize(mongoClient);
        const logger = {
            log: (message: string) => {
                console.log(message);
            },
        }

        const logic = new SetProperShopRegistrationTypeMigrationLogic(
            dataAccess,
            logger,
        );

        await logic.execute(batchSize, dryRun);
    }  
    
    private getBatchSize(): number {
        const batchSize = process.env.BATCH_SIZE
            ? parseInt(process.env.BATCH_SIZE, 10)
            : 1;

        return batchSize;
    }
}

const script = new Script();
script.main();

import { DataAccessUnitOfWork } from '@chargeflow-team/data-access';

export interface Logger {
    log: (message: string) => void;
}

export class SetProperShopRegistrationTypeMigrationLogic {
    constructor(
        private readonly dataAccess: DataAccessUnitOfWork,
        private readonly logger: Logger,
    ) {}

    async execute(batchSize: number, dryRun: boolean): Promise<void> {
        try {
            this.logger.log('Migrating shop registration type started, please wait ...');

            const pageSizeForInitialFind = 1;
            const limitForInitialFind = 1;

            const registrationType = 'stripe-app';
            const { pagination } = await this.dataAccess.shop.findByRegistrationType(
                registrationType,
                pageSizeForInitialFind,
                limitForInitialFind,
            );

            const totalBatches = Math.ceil(pagination.totalItems / batchSize);

            this.logger.log(`Total shops to process: ${pagination.totalItems}`);
            this.logger.log(`Total batches to process: ${totalBatches}`);

            if (dryRun) {
                this.logger.log('Dry run mode enabled, no changes made to the database');

                return;
            }

            for (let currentBatch = 1; currentBatch <= totalBatches; currentBatch++) {
                try {
                    const session = await this.dataAccess.startTransaction();
                    const page = 1;
                    
                    const { items } = await this.dataAccess.shop.findByRegistrationType(
                        registrationType,
                        page,
                        batchSize,
                        session,
                    );

                    await Promise.all(
                        items.map(async shop => {
                            if (!shop || shop.registrationType !== 'stripe-app') {
                                return;
                            }

                            let properRegistrationType;

                            if (shop.platform === 'shopify') {
                                properRegistrationType = 'shopify';
                            } else if (shop.platform === 'stripe') {
                                properRegistrationType = 'stripe-app';
                            } else {
                                properRegistrationType = 'manual';
                            }

                            return this.dataAccess.shop.update(shop._id, { registrationType: properRegistrationType }, session);
                        }),
                    );
                } catch (error) {
                    await this.dataAccess.abortTransaction();

                    throw error;
                }

                await this.dataAccess.commitTransaction();
            }

            this.logger.log('Migrating shop registration type finished successfully');

        } catch (error) {
            if (error instanceof Error) {
                this.logger.log(`Migrating shop registration type failed, reason: ${error.message}`);
            }

            throw error;
        }
    }
}

# Purpose
This script sets proper shop registration type property.

https://linear.app/chargeflow/issue/CHA-2845/discrepancies-within-the-shop-collection-for-users-from-the-stripe-app#comment-0cc836e7

## Motivation
Before the migration some of the shops have wrong registration type. The goal of this migration is to fix it.

## Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

1. Set the following environment variables in CWD:
```
MONGO_URI=<db connection string>
MONGO_AUTH={"username":"<db username>","password":"<db password>"}
DISPUTES_EVENT_BUS="test"
STACK_NAME="test"
BATCH_SIZE=<batch size>
```

2. Install deps from CWD: `npm i`

3. Run the `dry` mode first. See the number of projected shops to migrate and adjust the `BATCH_SIZE` env var.

4. Run the migration in `wet-primary` mode once everything is prepared.


### Modes
dry - does not update anything, writes the results to the console
wet-secondary - not implemented
wet-primary - executes the actual migration

### How this script was tested
- [x] test migration against `dev` db
- [x] test migration against `test` db

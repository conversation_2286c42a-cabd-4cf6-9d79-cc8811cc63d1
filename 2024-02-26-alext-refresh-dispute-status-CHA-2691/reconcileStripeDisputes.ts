import { MongoClient, ObjectId } from "mongodb";
import { Stripe } from "stripe";
import { IDiscrepancy, IDisputeDocument, StatusRefreshScriptBase } from "./shared";
const fs = require("fs");
require("dotenv").config();

const INTERMITTENT_STRIPE_ERRORS = [
  "StripeRateLimitError",
  "StripeConnectionError",
];

const stripeStatusConverter: Record<string,string> = {
  won: "won",
  lost: "lost",
  warning_won: "won",
  protected: "insured",
  charge_refunded: "refunded",
  under_review: "under_review",
  warning_closed: "warning_won",
  needs_response: "needs_response",
  warning_under_review: "warning_under_review",
  warning_needs_response: "warning_needs_response",
};

const resolvedStatues = ["won", "lost", "warning_won"];

class Script extends StatusRefreshScriptBase {
  public getName(): string {
    return "Updates dispute status and processorStatus from Stripe";
  }
  public getAuthor(): string {
    return "<EMAIL>, <EMAIL>, <EMAIL>";
  }
  public getTicket(): string {
    return "CHA-2691";
  }

  private stripeClient?: Stripe = undefined;

  private getStripeClient() {
    if (!process.env.STRIPE_SECRET_KEY) {
      throw new Error("STRIPE_SECRET_KEY is not set");
    }
    const stripeSecret = process.env.STRIPE_SECRET_KEY;
    if (!this.stripeClient) {
      this.stripeClient = new Stripe(stripeSecret, { telemetry: false });
    }
    return this.stripeClient;
  }

  public async getDisputesWithDicrepancies(client: MongoClient): Promise<IDisputeDocument[]>  {
    console.log("Getting disputes with discrepancies");
    const query = 
    {
        "chargeflow.imported": {
          $exists: false,
        },
        "dispute.processor": "stripe",
        dateCreated: {
          $gt: new Date("2023-12-01T00:00:00.000+00:00"),
        },
    };

    const allDisputes = await this.getDisputes(client, query);
    return allDisputes.filter(d=>this.findDiscrepancy(d));
  }

  public async getDisputesWithIncorrectResolvedData(client: MongoClient): Promise<IDisputeDocument[]> {

    console.log("Getting disputes with incorrect resolved data");
    const query = 
    {
        "lastDisputeStatus.status": {
          $nin: resolvedStatues,
        },
        "chargeflow.resolved.isResolved": true, 
        "dispute.processor": 'stripe' ,
        "chargeflow.imported": {
          $exists: false
        }
    };
      
    return await this.getDisputes(client, query);
  }

  private async getDisputes(client: MongoClient, query: object): Promise<IDisputeDocument[]> {
    const disputes =  await client
      .db("chargeflow_api")
      .collection("disputes")
      .find(
        query,
        {
          projection: {
            "dispute.disputeStatuses": 1,
            lastDisputeStatus: 1,
            shopName: 1,
            "dispute.id": 1,
            chargeflowId: 1,
            "chargeflow.resolved": 1,
          },
        }
      )
      .toArray();
    return <IDisputeDocument[]>disputes;
  }

  public async reconcileDispute(client: MongoClient, dispute: IDiscrepancy, dryRun: boolean) {
    try {
      const stripe_user_id = await this.getStripeConnect(client, dispute.chargeflowId);
      if (!stripe_user_id) {
        throw new Error(`No stripe user id found for dispute id ${dispute._id.toString()} with chargeflowId ${dispute.chargeflowId.toString()}`);
      }
      const stripeDisputeResult = await this.getStripeDispute(
        stripe_user_id,
        dispute.caseId
      );;

      const updateQuery = this.getUpdateQuery(stripeDisputeResult.status, stripeDisputeResult.amount);
      if (dryRun) {
        this.updateOneStripeDispute_dryRun(dispute._id, updateQuery);
        return true;
      }
      else {
        return await this.updateOneStripeDispute(client, dispute._id, updateQuery);
      }
    } catch (error) {
      console.error(`Failed to reconcile dispute ${dispute._id} ${error}`);
      throw error;
    }
  }

  private async getStripeConnect(client:MongoClient, chargeflowId: string | ObjectId) {
    try {
      return await client
        .db("chargeflow_api")
        .collection("processors")
        .findOne(
          {
            chargeflow_id: new ObjectId(chargeflowId),
            processor_name: "stripe"
          },
          {
            projection: {
              "connect.stripe_user_id": 1,
              _id: 0,
            },
          }
        )
        .then((data) => data?.connect.stripe_user_id);
    } catch (error) {
      throw error;
    }
  }

  private findDiscrepancy(dispute: IDisputeDocument) {
    return !!(
      this.findDiscrepancyInDisputeStatusesList(dispute) ||
      this.findDiscrepancyInLastDisputeStatus(dispute)
    );
  }

  private findDiscrepancyInDisputeStatusesList(dispute: IDisputeDocument) {
    return dispute.dispute.disputeStatuses.find((status) => {
      return status.status !== stripeStatusConverter[status.processorStatus];
    });
  }

  private findDiscrepancyInLastDisputeStatus(dispute: IDisputeDocument) {
    return dispute.lastDisputeStatus.status !==
      stripeStatusConverter[dispute.lastDisputeStatus.processorStatus]
      ? dispute.lastDisputeStatus
      : false;
  }

  private async getStripeDispute(stripe_user_id: string, caseId: string) {
    const stripeClient = this.getStripeClient();
    let backoffInterval = 1000;
    do {
      try {
        return await stripeClient.disputes.retrieve(caseId, { stripeAccount: stripe_user_id });
      } catch (err: any) {
        if (!err.type || !INTERMITTENT_STRIPE_ERRORS.includes(err.type)) {
          throw err;
        }
        console.error(
          `Intermittent error while fetching dispute ${caseId} for stripe account ${stripe_user_id} %j`,
          err
        );
        console.info(`Retrying in ${backoffInterval}ms`);
        await new Promise((resolve) => setTimeout(resolve, backoffInterval));
        backoffInterval *= 2;
      }
    } while (true);
  }

  private getUpdateQuery(stripeDisputeStatus:string, amount: number) {
    const mappedStatus = stripeStatusConverter[stripeDisputeStatus];
    const wonStatuses = ["won", "warning_won"];

    const isResolved = resolvedStatues.includes(mappedStatus);

    const newDisputeStatus = {
      dateCreated: new Date(),
      statusDate: null,
      status: mappedStatus,
      processorStatus: stripeDisputeStatus,
      source: "script",
    };

    return {
      $set: {
        dateUpdated: new Date(),
        "chargeflow.resolved.isResolved": isResolved || false,
        "chargeflow.resolved.isWon":
          wonStatuses.includes(mappedStatus) || false,
        "chargeflow.resolved.amountWon": wonStatuses.includes(mappedStatus)
          ? Number(amount || null)
          : null,
        ...(!isResolved && { "chargeflow.resolved.resolvedDate": null }),
        lastDisputeStatus: newDisputeStatus,
      },
      $addToSet: {
        "dispute.disputeStatuses": newDisputeStatus,
      },
    };
  }

  private async updateOneStripeDispute_dryRun(disputeId: ObjectId, updateQuery: any) {
    console.log(`Dispute ${disputeId} will be updated with the following query: ${JSON.stringify(updateQuery)}`);
  }

  private async updateOneStripeDispute(client: MongoClient, disputeId: ObjectId, updateQuery: any) {
    return await client
      .db("chargeflow_api")
      .collection("disputes")
      .findOneAndUpdate({ _id: disputeId }, updateQuery, {
        projection: {
          "chargeflow.resolved": 1,
          lastDisputeStatus: 1,
          _id: 0,
        },
        returnDocument: "after",
      });
  }
}

const script = new Script();
script.main();


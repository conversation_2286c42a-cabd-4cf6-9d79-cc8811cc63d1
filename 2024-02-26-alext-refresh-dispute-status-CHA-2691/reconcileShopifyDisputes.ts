import { MongoClient, ObjectId } from "mongodb";
import {
  IDiscrepancy,
  IDisputeDocument,
  StatusRefreshScriptBase,
} from "./shared";
require("dotenv").config();
//import { FetchError } from "node-fetch";

const shopifyStatusConverter: Record<string, string> = {
  accepted: "lost",
  charge_refunded: "lost",
  lost: "lost",
  needs_response: "needs_response",
  under_review: "under_review",
  won: "won",
  warning_won: "won",
};

const resolvedStatues = ["won", "lost"];

function isRetryableHttpError(err: any) {
  return (
    err?.errno && // instanceof FetchError &&
    ["EAI_AGAIN", "ECONNREFUSED", "ECONNRESET"].includes(err.errno!)
  );
}

class Script extends StatusRefreshScriptBase {
  public getName(): string {
    return "Updates dispute status and processorStatus from Shopify";
  }
  public getAuthor(): string {
    return "<EMAIL>, <EMAIL>, <EMAIL>";
  }
  public getTicket(): string {
    return "CHA-2691";
  }

  private async getDisputes(client: MongoClient, query: object): Promise<IDisputeDocument[]> {
    const disputes = await client
      .db("chargeflow_api")
      .collection("disputes")
      .find(
        query,
        {
          projection: {
            "dispute.disputeStatuses": 1,
            lastDisputeStatus: 1,
            shopName: 1,
            "dispute.id": 1,
            chargeflowId: 1,
            "chargeflow.resolved": 1,
          },
        }
      )
      .toArray();
    return <IDisputeDocument[]>disputes;
  }

  public async getDisputesWithIncorrectResolvedData(client: MongoClient): Promise<IDisputeDocument[]> {

    console.log("Getting disputes with incorrect resolved data");
    const query = 
    {
        "lastDisputeStatus.status": {
          $nin: resolvedStatues,
        },
        "chargeflow.resolved.isResolved": true, 
        "dispute.processor": 'shopify' ,
        "chargeflow.imported": {
          $exists: false
        }
    };
      
    return await this.getDisputes(client, query);
  }

  public async getDisputesWithDicrepancies(
    client: MongoClient
  ): Promise<IDisputeDocument[]> {

    const query = {
      "chargeflow.imported": {
        $exists: false,
      },
      "dispute.processor": "shopify",
      dateCreated: {
        $gt: new Date("2023-12-01T00:00:00.000+00:00"),
      },
    };

    const disputes = await this.getDisputes(client, query);
    return disputes.filter(d=>this.findDiscrepancies(d));
  }

  public async reconcileDispute(
    client: MongoClient,
    dispute: IDiscrepancy,
    dryRun: boolean
  ) {
    try {
      const shopifyAccessToken = await this.getShopifyConnectData(
        client,
        dispute.shopName
      );
      const shopifyDispute = await this.fetchShopifyDispute(
        dispute.shopName,
        dispute.caseId,
        shopifyAccessToken
      );
      const updateQuery = this.getUpdateQuery(
        shopifyDispute.dispute.status,
        shopifyDispute.dispute.finalized_on,
        shopifyDispute.dispute.amount
      );
      if (dryRun) {
        this.updateOneShopifyDispute_dryRun(dispute._id, updateQuery);
        return true;
      } else {
        return await this.updateOneShopifyDispute(
          client,
          dispute._id,
          updateQuery
        );
      }
    } catch (error) {
      console.error(`Failed to reconcile dispute ${dispute._id} ${error}`);
      throw error;
    }
  }
  private async getShopifyConnectData(client: MongoClient, shopName: string) {
    return await client
      .db("chargeflow_api")
      .collection("shops")
      .findOne(
        {
          name: shopName,
        },
        {
          projection: {
            "connect.access_token": 1,
            _id: 0,
          },
        }
      )
      .then((data) => data?.connect?.access_token);
  }

  private findDiscrepancies(dispute: IDisputeDocument) {
    return (
      this.findDiscrepancyInDisputeStatusesList(dispute) ||
      this.findDiscrepancyInLastDisputeStatus(dispute)
    );
  }

  private findDiscrepancyInDisputeStatusesList(dispute: IDisputeDocument) {
    return dispute.dispute.disputeStatuses.find((status) => {
      return status.status !== shopifyStatusConverter[status.processorStatus];
    });
  }

  private findDiscrepancyInLastDisputeStatus(dispute: IDisputeDocument) {
    return dispute.lastDisputeStatus.status !==
      shopifyStatusConverter[dispute.lastDisputeStatus.processorStatus]
      ? dispute.lastDisputeStatus
      : false;
  }

  private async fetchShopifyDispute(
    shopName: string,
    caseId: string,
    accessToken: string
  ): Promise<IShopifyDispute> {
    const baseUrl = `https://${shopName}.myshopify.com/admin/api/2023-07`;
    const url = `${baseUrl}/shopify_payments/disputes/${caseId}.json`;

    const options = {
      method: "GET",
      headers: {
        "X-Shopify-Access-Token": accessToken,
        "Content-Type": "application/json",
      },
    };

    try {
      const res = await fetch(url, options);

      switch (res.status) {
        case 200: {
          return res.json();
        }
        case 429:
        case 520: {
          return new Promise((resolve) =>
            setTimeout(
              () =>
                resolve(
                  this.fetchShopifyDispute(shopName, caseId, accessToken)
                ),
              Number((res.headers.get("Retry-After") as string) ?? 2) * 1000
            )
          );
        }
        default: {
          const body = await res.text();
          throw new Error(
            "Failed to fetch Shopify url: " + `${res.status} ${url} ${body}`
          );
        }
      }
    } catch (err: any) {
      if (isRetryableHttpError(err)) {
        return new Promise((resolve) =>
          setTimeout(
            () =>
              resolve(this.fetchShopifyDispute(shopName, caseId, accessToken)),
            2000
          )
        );
      }
      throw err;
    }
  }

  private getUpdateQuery(
    shopifyDisputeStatus: string,
    finalizedOn: string,
    amount: string
  ) {
    const mappedStatus = shopifyStatusConverter[shopifyDisputeStatus];
    const wonStatuses = ["won", "warning_won"];

    const resolved = {
      resolvedDate: finalizedOn ? new Date(finalizedOn) : null,
      resolveId: null,
      isResolved:
        resolvedStatues.includes(mappedStatus) || false,
      isWon: wonStatuses.includes(mappedStatus) || false,
      amountWon: wonStatuses.includes(mappedStatus)
        ? Number(amount || null)
        : null,
      successFee: null,
    };

    const newDisputeStatus = {
      dateCreated: new Date(),
      statusDate: null,
      status: mappedStatus,
      processorStatus: shopifyDisputeStatus,
      source: "script",
    };

    return {
      $set: {
        "dispute.closedDate": finalizedOn ? new Date(finalizedOn) : null,
        dateUpdated: new Date(),
        "chargeflow.resolved": resolved,
        lastDisputeStatus: newDisputeStatus,
      },
      $addToSet: {
        "dispute.disputeStatuses": newDisputeStatus,
      },
    };
  }

  private async updateOneShopifyDispute(
    client: MongoClient,
    disputeId: ObjectId,
    updateQuery: any
  ) {
    return await client
      .db("chargeflow_api")
      .collection("disputes")
      .findOneAndUpdate({ _id: disputeId }, updateQuery, {
        projection: {
          "chargeflow.resolved": 1,
          lastDisputeStatus: 1,
          _id: 0,
        },
        returnDocument: "after",
      });
  }

  private updateOneShopifyDispute_dryRun(
    disputeId: ObjectId,
    updateQuery: any
  ) {
    console.log(
      `Dispute ${disputeId} will be updated with the following query: ${JSON.stringify(
        updateQuery
      )}`
    );
  }
}

interface IShopifyDispute {
  dispute: {
    status: string;
    finalized_on: string;
    amount: string;
  };
}


const script = new Script();
script.main();
import { MongoClient, ObjectId } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { mkdir, writeFileSync } from "fs";
export abstract class StatusRefreshScriptBase extends ScriptBase {
  async dryRun(context: ScriptContext): Promise<void> {
    const client = await loadMongoClient();
    await this.fix(client, true, context?.args[1]);
  }
  async wetRunOnSecondary(context: ScriptContext): Promise<void> {
    const client = await loadMongoClient();
    await this.fix(client, false, context?.args[1]);
  }
  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    const client = await loadMongoClient();
    await this.fix(client, false, context?.args[1]);
  }

  abstract getDisputesWithDicrepancies(
    client: MongoClient
  ): Promise<IDisputeDocument[]>;
  abstract getDisputesWithIncorrectResolvedData(
    client: MongoClient
  ): Promise<IDisputeDocument[]>;
  abstract reconcileDispute(
    client: MongoClient,
    dispute: IDiscrepancy,
    dryRun: boolean
  ): Promise<any>;

  async fix(
      client: MongoClient, 
      dryRun: boolean = true, 
      isResolvedFix: string = "false" ) {
    console.log("Starting to reconcile disputes");
    
    const disputes = isResolvedFix.toLowerCase() === "true" ? 
      await this.getDisputesWithIncorrectResolvedData(client): 
      await this.getDisputesWithDicrepancies(client);
    
    const disputesWithDiscrepancies: IDiscrepancy[] = [];
    disputes.forEach((dispute) => {
      disputesWithDiscrepancies.push(this.formatDisputes(dispute));
    });

    console.log(
      "Amount of disputes to reconcile: %s",
      disputesWithDiscrepancies?.length
    );

    let processedDisputes = { total: 0, disputesPerShop: {} };
    let disputesFailedToReconcile = { total: 0, disputesPerShop: {} };

    for (const dispute of disputesWithDiscrepancies) {
      try {
        const updatedDispute = await this.reconcileDispute(
          client,
          dispute,
          dryRun
        );
        if (updatedDispute) {
          this.addToResults(
            "success",
            processedDisputes,
            dispute,
            updatedDispute.value
          );
        } else {
          this.addToResults(
            "failure",
            disputesFailedToReconcile,
            dispute,
            undefined
          );
        }
      } catch (err) {
        this.addToResults(
          "failure",
          disputesFailedToReconcile,
          dispute,
          undefined
        );
      }
    }

    this.createReport(processedDisputes, "successfullyReconciled");
    this.createReport(disputesFailedToReconcile, "failedToReconcile");

    console.info(
      `Successfully reconciled ${processedDisputes.total} disputes out of ${disputesWithDiscrepancies.length} total, ${disputesFailedToReconcile.total} failed`
    );
  }

  private formatDisputes(dispute: IDisputeDocument): IDiscrepancy {
    return {
      _id: dispute._id,
      caseId: dispute.dispute.id,
      lastDisputeStatus: dispute.lastDisputeStatus,
      disputeStatuses: dispute.dispute.disputeStatuses,
      shopName: dispute.shopName,
      chargeflowId: dispute.chargeflowId,
      resolved: dispute.chargeflow.resolved,
      discrepancy: true,
    };
  }

  private addToResults(
    result: "success" | "failure",
    report: { total: number; disputesPerShop: Record<string, any> },
    dispute: IDiscrepancy,
    updatedDispute: any
  ) {
    const disputeSummary =
      result === "success"
        ? {
            id: dispute._id.toString(),
            changes: {
              status: {
                statusWithDiscrepancy: dispute.discrepancy,
                updatedStatus: updatedDispute?.lastDisputeStatus,
              },
              resolvedObject: {
                previous: dispute.resolved,
                updated: updatedDispute?.chargeflow.resolved,
              },
            },
          }
        : dispute._id.toString();

    report.disputesPerShop[`${dispute.shopName}`]
      ? report.disputesPerShop[`${dispute.shopName}`].push(disputeSummary)
      : (report.disputesPerShop[`${dispute.shopName}`] = [disputeSummary]);

    report.total++;
  }

  private createReport(data: any, name: string) {
    mkdir(`./reports`, { recursive: true }, (err: any) => console.log(err));
    writeFileSync(
      `./reports/${name}_${new Date()}.json`,
      JSON.stringify(data, null, 2)
    );
  }
}

export interface IDisputeStatus {
  processorStatus: string;
  status: string;
}

export interface IResolvedDisputeData {
  isResolved: boolean;
  isWon: boolean;
  amountWon: number;
  resolvedDate: Date;
}

export interface IDisputeDocument {
  _id: ObjectId;
  dispute: {
    id: string;
    disputeStatuses: IDisputeStatus[];
  };
  lastDisputeStatus: IDisputeStatus;
  shopName: string;
  chargeflowId: ObjectId;
  chargeflow: {
    resolved: IResolvedDisputeData;
  };
}

export interface IDiscrepancy {
  _id: ObjectId;
  caseId: string; //dispute.dispute.id,
  lastDisputeStatus: IDisputeStatus; //dispute.lastDisputeStatus,
  disputeStatuses: IDisputeStatus[];
  shopName: string;
  chargeflowId: ObjectId;
  resolved: IResolvedDisputeData;
  discrepancy: boolean;
}

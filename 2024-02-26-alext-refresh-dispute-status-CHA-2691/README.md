# Purpose
These scripts refresh disputes "status", "procesorStatus" and "resolved" properties following a data corruption bug.

## Motivation
Data correctness, making sure that we handle those disputes.

## Running these scripts

To FIX statues dicrepancies:

`npx ts-node ./reconcileShopifyDisputes.ts <dry or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

`npx ts-node ./reconcileStripeDisputes.ts <dry or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

To FIX incorrect Resolved data: (Need to send one more arg as 'true')

`npx ts-node ./reconcileShopifyDisputes.ts <dry or wet-primary> true | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

`npx ts-node ./reconcileStripeDisputes.ts <dry or wet-primary> true | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)


Need in `.env` file:
MONGO_URI - Mongo connection string
STRIPE_SECRET_KEY

### Modes
dry - does not update anything, writes the results to the console
wet-primary - UPDATES THE DISPUTES TABLE. Use with extreme caution!

### How this script was tested
[x] Dev DB
[x] Prod DB (commented out the commit part)
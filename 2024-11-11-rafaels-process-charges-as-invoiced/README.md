# Purpose:

Processing charges as collected invoices.

# Motivation:

Consider all charges as collected invoices, hence creating paid invoices for every charge

# Running this script

`npx tsx ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with 
**PG_READER_CONNECTION_STRING_DEV** , 
**MONGO_URI** , 

### How this script was tested
[x] Dry and Wet run against dev
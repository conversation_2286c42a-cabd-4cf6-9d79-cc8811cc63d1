import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Collection, Db } from 'mongodb';
import knex, { Knex } from 'knex';
import { Pool, PoolClient } from 'pg';

dotenv.config();

type MappedCharge = {
  dateCreated: Date;
  finalAmount: number;
  caseId: string;
  chargeflowId: string;
  accountId: string;
};

type InvoiceData = {
  accountId: number;
  paymentMethodId: number;
  status: string;
  amount: number;
  productReference: string;
  currency: string;
  description: string;
  productType: string;
  issueDate: Date;
};

interface DbResult {
  id: number;
}

const SHOPIFY_PAYMENT_TYPE = 'Shopify';
const PAGE_SIZE = 100;

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export class ProcessChargesAsPaidInvoices extends ScriptBase {
  db: Db | undefined = undefined;

  public getName(): string {
    return "Create paid invoices for all charges";
  }

  public getAuthor(): string {
    return "Rafael Schuster";
  }

  public getTicket(): string {
    return "CHA-7492";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async upsertInvoice(invoiceData: InvoiceData, client: PgService, knexClient: Knex): Promise<DbResult> {
    try {
      console.log('Checking for existing invoices for caseId: %s', invoiceData.productReference);
      const existingInvoiceQuery = knexClient
        .queryBuilder()
        .select('*')
        .from("Invoice AS i")
        .where("i.productReference", invoiceData.productReference);

      const existingInvoices = await client.query(existingInvoiceQuery.toQuery());

      let query;
      if (existingInvoices.length > 0) {
        console.log('Invoice already exists, updating for caseId: %s', invoiceData.productReference);
        query = knexClient
          .queryBuilder()
          .update(invoiceData)
          .from("Invoice AS i")
          .where("i.productReference", invoiceData.productReference)
          .returning('id');
      } else {
        console.log('Invoice does not exist, inserting for caseId: %s', invoiceData.productReference);
        query = knexClient
          .queryBuilder()
          .insert(invoiceData)
          .into("Invoice")
          .returning('id');
      }

      const res = await client.query(query.toQuery());
      return res[0] as DbResult;
    } catch (error) {
      console.error('Error upserting invoice', error);
      throw new Error('Error upserting invoice');
    }
  }

  private async updatePaymentCollection(invoiceData: any, client: PgService, knexClient: Knex): Promise<DbResult> {
    console.log('Checking for existing payment collection for invoiceId: %s', invoiceData.id);
    try {
      const existingPaymentCollectionQuery = knexClient
        .queryBuilder()
        .select('*')
        .from("PaymentCollections AS p")
        .where("p.invoiceId", invoiceData.id);

      const existingPaymentCollection = await client.query(existingPaymentCollectionQuery.toQuery());

      let query;
      if (existingPaymentCollection.length > 0) {
        console.log('Payment collection already exists, updating for invoiceId: %s', invoiceData.id);
        query = knexClient
          .queryBuilder()
          .update({
            status: 'paid',
            dateUpdated: new Date(),
          })
          .from("PaymentCollections AS p")
          .where("p.invoiceId", invoiceData.id)
          .returning('id');
      } else {
        console.log('Payment collection does not exist, inserting for invoiceId: %s', invoiceData.id);
        query = knexClient
          .queryBuilder()
          .insert({
            invoiceId: invoiceData.id,
            status: 'paid',
            dateUpdated: new Date(),
          })
          .into("PaymentCollections")
          .returning('id');
      }

      const res = await client.query(query.toQuery());
      return res[0] as DbResult;
    } catch (error) {
      console.error('Error upserting payment collection: %s', error.toString());
      throw new Error('Error upserting payment collection');
    }
  }

  private async fetchCharges(collection: Collection, offset: number, retries = 3): Promise<MappedCharge[]> {
    for (let attempt = 1; attempt <= retries; attempt++) {
        try {
          const charges = await collection.aggregate([
            {
              $match: {
                manuallyProcessedAsInvoiced: { $ne: true }
              }
            },
            {
              $lookup: {
                from: "shops",
                localField: "chargeflowId",
                foreignField: "chargeflow_id",
                as: "shopDetails"
              }
            },
            {
              $unwind: "$shopDetails"
            },
            {
              $project: {
                dateCreated: 1,
                successAmount: 1,
                chargedAmount: 1,
                caseId: 1,
                chargeflowId: 1,
                accountId: 1,
                "shopDetails.accountId": 1
              }
            },
            {
              $skip: offset
            },
            {
              $limit: PAGE_SIZE
            }
          ]).toArray();
      
          const mappedCharges = charges.map((charge) => ({
            dateCreated: charge.dateCreated,
            finalAmount: charge.chargedAmount ?? charge.successAmount,
            caseId: charge.caseId,
            chargeflowId: charge.chargeflowId.toString(),
            accountId: charge.accountId?.toString() || charge.shopDetails?.accountId?.toString(),
          }));
      
          return mappedCharges;
          
        } catch (error) {
            console.error(`Attempt ${attempt} failed to fetch charges:`, error);
            if (attempt === retries) throw error;
            console.log(`Retrying in 5 seconds...`);
            await delay(5000);
        }
    }
    throw new Error('Failed to fetch charges after all retries');
}

  private toInvoiceData(caseId: string, accountId: number, paymentMethodId: number, finalAmount: number, description: string, issueDate: Date): InvoiceData {
    return {
      accountId,
      paymentMethodId,
      status: 'paid',
      amount: finalAmount,
      productReference: caseId,
      issueDate,
      currency: 'USD',
      description,
      productType: 'dispute'
    };
  }

  private buildDescription(caseId: string, calculatedProductAmount: number, issueDate: Date): string {
    function formatDate(issueDate: Date): string {
      const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: '2-digit' };
      return issueDate.toLocaleDateString('en-US', options).replace(',', '');
    }
    const submittedAt = formatDate(issueDate);

    return `An invoice for a recovered dispute, ID: ${caseId}, which was submitted by Chargeflow on ${submittedAt}, for ${calculatedProductAmount} USD and charge processed as invoiced.`;
  }

  private generateRandomAlphanumeric(length: number): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    const charactersLength = characters.length;

    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }

    return result;
  }

  private async upsertOrFindAccount(chargeflowId: string, client: PgService, knexClient: Knex, mongoAccountId: string): Promise<DbResult> {
    const query = knexClient
      .queryBuilder()
      .select(['a.id'])
      .from("Account AS a")
      .where("a.mongoAccountId", mongoAccountId);

    const result = await client.query(query.toQuery());

    if (!result.length) {
      console.log('Creating new account for mongoAccountId: %s', mongoAccountId);
      const insertQuery = knexClient('Account').insert({
        chargeflowId,
        mongoAccountId,
      }).returning('id');

      const insertResult = await client.query(insertQuery.toQuery());
      return insertResult[0] as DbResult;
    }

    return result[0] as DbResult;
  }

  async flagManyCharges(caseIds: string[], collection: Collection): Promise<any> {
    if (!caseIds.length) throw new Error('Missing caseIds');
    return collection.updateMany(
      { caseId: { $in: caseIds } },
      { $set: { manuallyProcessedAsInvoiced: true } }
    ).catch((err) => {
      if (err.code === 'ECONNREFUSED') throw new Error(`No internet connection: ${JSON.stringify(err)}`);
      throw err;
    });
  }

  private async upsertOrFindPaymentMethodId(accountId: number, client: PgService, knexClient: Knex, paymentMethodType: string, mongoAccountId: string, chargeflowId: string): Promise<DbResult> {
    const query = knexClient
      .queryBuilder()
      .select(['p.id'])
      .from("PaymentMethod AS p")
      .where("p.accountId", accountId);

    const result = await client.query(query.toQuery());

    if (!result.length) {
      console.log('Creating payment method for %s accountId', accountId);
      const insertQuery = knexClient('PaymentMethod').insert({
        accountId,
        type: paymentMethodType,
        settings: {},
        mongoAccountId,
        chargeflowId,
      }).returning('id');

      const insertResult = await client.query(insertQuery.toQuery());
      return insertResult[0] as DbResult;
    }

    return result[0] as DbResult;
  }

  private toCents(amount: number): number | undefined {
    if (amount === undefined || amount === null) {
      console.info('Amount is missing');
      return;
    }
    return Math.round(amount * 100);
  }

  private async processChargeWithTransaction(
    charge: MappedCharge,
    client: PgService,
    knexClient: Knex
  ): Promise<void> {
    let transactionStarted = false;
    try {
      await client.query('BEGIN');
      transactionStarted = true;
      
      const invoiceData = await this.processCharge(charge, client, knexClient);
      if (invoiceData) {
        const invoiceRes = await this.upsertInvoice(invoiceData, client, knexClient);
        const pcRes = await this.updatePaymentCollection(invoiceRes, client, knexClient);
        console.log('Processed charge: %s, Invoice id: %s, Payment Collection id: %s', 
          charge.caseId, invoiceRes.id, pcRes.id);
      }
      
      await client.query('COMMIT');
      transactionStarted = false;
    } catch (error) {
      if (transactionStarted) {
        await client.query('ROLLBACK').catch(rollbackError => {
          console.error('Rollback failed:', rollbackError);
        });
      }
      throw error;
    }
  }

  private async processCharge(charge: MappedCharge, client: PgService, knexClient: Knex): Promise<InvoiceData | null> {
    try {
      console.log('Processing charge data for caseId: %s', charge.caseId);
      
      const mongoAccountId: string = charge.accountId || `manually-added-acccount-${this.generateRandomAlphanumeric(24)}`;
      const account = await this.upsertOrFindAccount(charge.chargeflowId, client, knexClient, mongoAccountId);
      console.log('Account processed for caseId: %s, accountId: %s', charge.caseId, account.id);

      const paymentMethod = await this.upsertOrFindPaymentMethodId(
        account.id,
        client,
        knexClient,
        SHOPIFY_PAYMENT_TYPE,
        mongoAccountId,
        charge.chargeflowId
      );
      console.log('Payment method processed for caseId: %s, paymentMethodId: %s', charge.caseId, paymentMethod.id);

      const finalAmount = this.toCents(charge.finalAmount);
      if (finalAmount === undefined) {
        console.error('Missing final amount for caseId: %s', charge.caseId);
        return null;
      }

      const description = this.buildDescription(charge.caseId, charge.finalAmount, new Date(charge.dateCreated));
      return this.toInvoiceData(
        charge.caseId,
        account.id,
        paymentMethod.id,
        finalAmount as number,
        description,
        new Date(charge.dateCreated)
      );
    } catch (error) {
      console.error('Error processing charge: %s', error);
      return null;
    }
  }

  private async verifyCharges(collection: Collection): Promise<number> {
    const totalCount = await collection.countDocuments({
      manuallyProcessedAsInvoiced: { $ne: true }
    });
  
    const detailedCount = await collection.aggregate([
      {
        $match: {
          manuallyProcessedAsInvoiced: { $ne: true }
        }
      },
      {
        $lookup: {
          from: "shops",
          localField: "chargeflowId",
          foreignField: "chargeflow_id",
          as: "shopDetails"
        }
      },
      {
        $project: {
          hasShopDetails: { $size: "$shopDetails" },
          hasAmount: { $ne: ["$successAmount", null] },
          hasCaseId: { $ne: ["$caseId", null] },
          hasChargeflowId: { $ne: ["$chargeflowId", null] },
          hasAccountId: {
            $or: [
              { $ne: ["$accountId", null] },
              { $ne: [{ $arrayElemAt: ["$shopDetails.accountId", 0] }, null] }
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          withoutShopDetails: { $sum: { $cond: [{ $eq: ["$hasShopDetails", 0] }, 1, 0] } },
          withoutAmount: { $sum: { $cond: ["$hasAmount", 0, 1] } },
          withoutCaseId: { $sum: { $cond: ["$hasCaseId", 0, 1] } },
          withoutChargeflowId: { $sum: { $cond: ["$hasChargeflowId", 0, 1] } },
          withoutAccountId: { $sum: { $cond: ["$hasAccountId", 0, 1] } }
        }
      }
    ]).toArray();
  
    console.log('\nVerification Results:');
    console.log('Total unprocessed charges:', totalCount);
    
    if (detailedCount.length > 0) {
      const details = detailedCount[0];
      console.log('Breakdown of missing data:');
      console.log('- Charges without shop details:', details.withoutShopDetails);
      console.log('- Charges without amount:', details.withoutAmount);
      console.log('- Charges without caseId:', details.withoutCaseId);
      console.log('- Charges without chargeflowId:', details.withoutChargeflowId);
      console.log('- Charges without accountId:', details.withoutAccountId);
  
      const expectedProcessable = totalCount - details.withoutShopDetails;
      console.log('\nExpected processable charges:', expectedProcessable);
      console.log('Actual processed charges:', totalCount - details.withoutShopDetails);
      
      if (expectedProcessable !== (totalCount - details.withoutShopDetails)) {
        console.log('\nWARNING: Discrepancy detected in processable charges!');
      }
      return expectedProcessable;
    } else {
        return totalCount;
    }
  }
  

  private async logic(context: ScriptContext, isDryRun: boolean): Promise<void> {
    console.log('Generating PG and Mongo clients');
    const client = new PgService();
    const knexClient = knex({ client: "pg" });
    await client.connect();
    const mongoClient = await loadMongoClient();
    const chargesMongoCollection = mongoClient.db("chargeflow_api").collection("charges");
    console.log('Successfully generated DB clients');

    try {
      let hasMore = true;
      let offset = 0;
      let totalCasesProcessed = 0;
      const errors: Array<{caseId: string, error: string}> = [];
    
      console.log('\nRunning data verification...');
      const totalCharges =  await this.verifyCharges(chargesMongoCollection);

      while (hasMore) {
        console.log(
          'Progress: %d/%d (%d%%)', 
          totalCasesProcessed, 
          totalCharges, 
          ((totalCasesProcessed / totalCharges) * 100).toFixed(2)
        );        
        console.log('Fetching charges from MongoDB with offset: %s', offset);
        const charges = await this.fetchCharges(chargesMongoCollection, offset);
        console.log('Successfully fetched %s charges', charges.length);

        if (!isDryRun  && charges.length > 0) {
          for (const charge of charges) {
            try {
              await this.processChargeWithTransaction(charge, client, knexClient);
            } catch (error) {
              errors.push({ caseId: charge.caseId, error: error.message });
              console.error('Error processing charge %s: %s', charge.caseId, error);
            }
          }

          const caseIds = charges.map(c => c.caseId);
          if (caseIds.length > 0) {
            const resFlagUpdate = await this.flagManyCharges(caseIds, chargesMongoCollection);
            console.log('Flagged %s charges as processed', resFlagUpdate.modifiedCount);
          }
        }

        totalCasesProcessed += charges.length;
        hasMore = charges.length === PAGE_SIZE;
        if (hasMore) {
            await delay(2000);
            offset += PAGE_SIZE;
        }
      }

      console.log(isDryRun ? 
        `Dry run - would have processed ${totalCasesProcessed} charges` :
        `Successfully processed ${totalCasesProcessed} charges`
      );
      if (errors.length > 0) {
        console.log('\nError Summary:');
        console.log('Total Errors:', errors.length);
        errors.forEach(({caseId, error}) => 
          console.log(`Case ${caseId}: ${error}`)
        );
      }
    } catch (error) {
      console.error('Error in main processing loop:', error);
      throw error;
    } finally {
      console.log('Cleaning up connections');
      await mongoClient.close();
      await client.disconnect();
    }
  }

}

interface IDbService {
  query<T>(queryString: string, params: any[]): Promise<T[]>
}

class PgService implements IDbService {
  private pool: Pool;
  private client: PoolClient | null = null;

  constructor() {
    const connectionString = process.env.PG_READER_CONNECTION_STRING_DEV!
    if (!connectionString) {
      console.error('InternalServerError', 'No connection string provided for Postgres');
    }
    this.pool = new Pool({connectionString, ssl: {rejectUnauthorized: false}, max: 1});
  }

  public async connect() {
    try {
      this.client = await this.pool.connect();
    } catch (error) {
      console.error('InternalServerError', 'Error connecting to Postgres', {error});
      throw new Error('Error connecting to Postgres');
    }
  }

  public async disconnect() {
    if (this.client) {
      await this.client.release();
      this.client = null;
    }
  }

  public async query<T>(queryString: string, params: any[] = []): Promise<T[]> {
    try {
      const result = await this.client?.query(queryString, params);
      return result?.rows || [];
    } catch (error) {
      console.error('InternalServerError', 'Error running query', { error, queryString });
      throw new Error('No data returned from Postgres');
    }
  }
}



const script = new ProcessChargesAsPaidInvoices();
script.main();
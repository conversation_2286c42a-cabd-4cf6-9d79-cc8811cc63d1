import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';

const getProcessorName = (processor: string): string => {
  return processor === 'shopify_payments'
    ? 'shopify payments'
    : processor?.toLowerCase();
};

export class cleanupAlertsRegistry extends ScriptBase {
  public getName(): string {
    return 'Delete migrated fields from alertsRegistry ';
  }

  public getAuthor(): string {
    return 'Ben Weber';
  }

  public getTicket(): string {
    return 'CHA-3112';
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log('Running in dry run mode (not changing anything)');
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error('Method not implemented.');
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    const client = await loadMongoClient();
    const db = client.db('chargeflow_api');
    const alertsRegistryCollection = db.collection('alertsRegistry');
    const merchantProfilesCollection = db.collection('merchantProfiles');

    const alertRegistries = await alertsRegistryCollection
      .find({ merchantAlertProfile: { $exists: true } })
      .toArray();
    let updatedAlertRegistries = 0;

    const findMatchesAndUpdate = async (registry: any) => {
      let allMatched = true;

      for (const profile of registry.merchantAlertProfile) {
        const matchCount = await merchantProfilesCollection.countDocuments({
          chargeflowId: registry.chargeflowId,
          processor: getProcessorName(profile.processor),
          statementDescriptor: profile.statementDescriptor,
        });

        if (matchCount === 0) {
          allMatched = false;
          break;
        }
      }

      if (allMatched) {
        updatedAlertRegistries++;
        console.log(
          `All profiles matched!!! For registry with chargeflowId: ${registry.chargeflowId}`
        );
        return true;
      }
      return false;
    };

    const handleDryRun = async (registry: any) => {
      console.log(
        `(Dry Run) Would delete 'merchantAlertProfile', 'dateAuthorized' and 'isEnrolled' fields for registry with chargeflowId: ${registry.chargeflowId}.`
      );
      console.log('-'.repeat(50));
    };

    const handleWetRun = async (registry: any) => {
      await alertsRegistryCollection.updateOne(
        { chargeflowId: registry.chargeflowId },
        {
          $unset: {
            merchantAlertProfile: '',
            isEnrolled: '',
            dateAuthorized: '',
          },
        }
      );
      console.log(
        `Deleted 'merchantAlertProfile', 'dateAuthorized' & 'isEnrolled' fields from registry with chargeflowId: ${registry.chargeflowId}`
      );
      console.log('-'.repeat(50));
    };

    try {
      console.log(
        `Looking for matches across ${alertRegistries.length} Alert Registries...`
      );
      for (const registry of alertRegistries) {
        console.log(`Inspecting registry: ${registry.chargeflowId.toString()}`);
        const matched = await findMatchesAndUpdate(registry);
        if (matched) {
          if (isDryRun) {
            await handleDryRun(registry);
          } else {
            await handleWetRun(registry);
          }
        }
      }
      console.log(`Total updated alert registries: ${updatedAlertRegistries}`);
    } catch (err) {
      console.error(err);
      throw err;
    } finally {
      client.close();
    }
  }
}

const script = new cleanupAlertsRegistry();
script.main();

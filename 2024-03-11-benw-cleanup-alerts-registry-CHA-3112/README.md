# Purpose
This script deletes unused fields from previous data structure: `isEnrolled`, `dateAuthorized` & `merchantAlertsProfile`

https://linear.app/chargeflow/issue/CHA-2905/data-structure-changes

## Motivation
Alerts is a subscription-based service which we are rolling out to our clients. We had a prelimenary data structure which was initiated as POC. Now that we are migrated to the new data structure, we are deleting fields which are no longer relevant

## Running this script
`npx ts-node ./script.ts <dry or wet-primary> 2>&1 | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD  
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console  
wet-secondary - not utilized in this script
wet-primary - DELETES FIELDS THE ALERTS REGISTRY COLLECTION. Use with extreme caution!

### How this script was tested
[x] `isEnrolled`, `dateAuthorized` & `merchantAlertsProfile` fields were deleted from alertsRegistry if those fields are matching & present in merchantProfiles collection
[x] Document in merchantProfiles exists with matching fields:
  [x] `chargeflowId`
  [x] `processor`
  [x] `statementDescriptor`
[x] Document in alertsRegistry exists with matching fields:
  [x] `isEnrolled`
  [x] `merchantAlertsProfile`
  [x] `authorizedAt`
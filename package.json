{"scripts": {"test": "jest"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.637.0", "@aws-sdk/client-scheduler": "^3.637.0", "@aws-sdk/client-sfn": "^3.658.1", "@aws-sdk/client-sqs": "^3.637.0", "@chargeflow-team/chargeflow-utils-sdk": "1.0.250", "@chargeflow-team/data-access": "^1.0.350", "@chargeflow-team/events-infra": "0.0.399", "@rockset/client": "^0.9.1", "@supercharge/promise-pool": "^3.2.0", "@types/lodash": "^4.17.4", "aws-crt": "^1.25.1", "aws-sdk": "^2.1687.0", "aws4": "^1.12.0", "axios": "^1.9.0", "dotenv": "^16.4.5", "json2csv": "^5.0.7", "knex": "^3.1.0", "lodash": "^4.17.21", "mongodb": "^5.6.0", "neat-csv": "^7.0.0", "pg": "^8.12.0", "stripe": "^17.5.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/json2csv": "^5.0.7", "@types/pg": "^8.11.10", "jest": "^29.7.0", "jest-mock-extended": "^3.0.5", "ts-jest": "^29.1.2"}}
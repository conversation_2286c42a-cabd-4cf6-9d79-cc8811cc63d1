import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import fs from 'fs';

import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Collection, Document, WithId, ObjectId } from 'mongodb';

dotenv.config();

const defaultDbName = 'chargeflow_api';

interface PendingCharge extends Document {
    disputeId: string;
    shopId: string;
    disputeRecoveredAmount: number;
    successAmount: number;
    realAmount?: number;
    realSuccessFee?: number;
}

interface DisputeValidData extends Document {
    disputeId: string;
    amount: number;
    successFee?: number;
}

export class FixAsanaPendingChargesAmount extends ScriptBase {
    private shopsCache: Record<string, number> = {};
    private disputesCollection!: Collection;
    private pendingChargesCollection!: Collection;
    private shopsCollection!: Collection;

    public getName(): string {
        return 'Fix asana pending charges amount to be the correct amount';
    }

    public getAuthor(): string {
        return '<PERSON><PERSON>';
    }

    public getTicket(): string {
        return 'CHA-14174';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.initializeCollections();
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.initializeCollections();
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async initializeCollections() {
        const client = await loadMongoClient();
        const db = client.db(defaultDbName);
        this.disputesCollection = db.collection('disputes');
        this.pendingChargesCollection = db.collection('pendingCharges');
        this.shopsCollection = db.collection('shops');
    }

    private async createUpdatedPendingCharge(charge: WithId<PendingCharge>, disputeAmounts: DisputeValidData[]): Promise<WithId<PendingCharge>> {
        const dispute = disputeAmounts.find(dispute => dispute.disputeId.toString() === charge.disputeId.toString());
        if (!dispute) {
            console.log(`Dispute ${charge.disputeId} not found`);
            return charge;
        }
        const disputeSuccessRate = await this.getChargeflowSuccessRate(charge.shopId);
        const disputeSuccessFee = disputeSuccessRate && dispute ? Math.round(dispute.amount * disputeSuccessRate) / 100 : undefined;
        console.log(`Dispute ${dispute?.disputeId} has amount ${dispute?.amount} and success fee ${disputeSuccessFee}`);
        return {
            ...charge,
            realAmount: dispute?.amount,
            realSuccessFee: disputeSuccessFee
        };
    }

    private async processDisputesBatch(
        disputes: string[]
    ): Promise<WithId<PendingCharge>[]> {
        console.log(`Processing ${disputes.length} disputes`);
        const pendingCharges: WithId<PendingCharge>[] = await this.getPendingCharges(disputes);
        console.log(`Found ${pendingCharges.length} pending charges`);
        const disputeIds = pendingCharges.map(charge => charge.disputeId.toString());
        const disputeAmounts: DisputeValidData[] = await this.getDisputes(disputeIds);
        const createUpdatedPendingCharges = pendingCharges.map(charge => this.createUpdatedPendingCharge(charge, disputeAmounts));
        const updatedPendingChargesList = await Promise.all(createUpdatedPendingCharges);
        return updatedPendingChargesList.filter(charge => 
            charge.realAmount !== charge.disputeRecoveredAmount || 
            charge.realSuccessFee !== charge.successAmount
        );
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {
        const handleDryRun = async (pendingChargesToUpdate: WithId<PendingCharge>[]) => {
            console.log(`Would update ${pendingChargesToUpdate.length} pending charges`);
            for (const charge of pendingChargesToUpdate) {
                console.log(`Would update charge ${charge.disputeId} with recovered amount ${charge.realAmount} and success fee ${charge.realSuccessFee}`);
            }
        };

        const handleWetRun = async (pendingChargesToUpdate: WithId<PendingCharge>[]) => {
            const bulkOps = pendingChargesToUpdate.map(charge => ({
                updateOne: {
                    filter: { _id: charge._id },
                    update: {
                        $set: {
                            successAmount: charge.realSuccessFee,
                            disputeRecoveredAmount: charge.realAmount
                        }
                    }
                }
            }));

            if (bulkOps.length > 0) {
                const result = await this.pendingChargesCollection.bulkWrite(bulkOps);
                console.log(`Expected to update ${pendingChargesToUpdate.length} pending charges`);
                console.log(`Successfully updated ${result.modifiedCount} pending charges`);
                if (result.modifiedCount !== pendingChargesToUpdate.length) {
                    console.log(`Warning: ${pendingChargesToUpdate.length - result.modifiedCount} charges were not updated`);
                }
            }
        };

        try {
            const allWrongCharges: WithId<PendingCharge>[] = [];
            const disputesList = await this.readDisputesListFromFile('./disputes.json');
            for await (const disputes of this.disputesListGenerator(disputesList)) {
                const pendingChargesToUpdate = await this.processDisputesBatch(disputes);
                allWrongCharges.push(...pendingChargesToUpdate);

                if (isDryRun) {
                    await handleDryRun(pendingChargesToUpdate);
                } else {
                    await handleWetRun(pendingChargesToUpdate);
                }
            }

            const wrongChargesFile = 'wrongPendingCharges.json';
            await fs.promises.writeFile(wrongChargesFile, JSON.stringify(allWrongCharges, null, 2));
            console.log(`Wrote ${allWrongCharges.length} wrong charges to ${wrongChargesFile}`);

        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        }
    }

    private async getChargeflowSuccessRate(shopId: string) {
        if (shopId in this.shopsCache) {
            return this.shopsCache[shopId];
        }
        const project: Document = {
            chargeflow_success_rate: 1,
        };
        const shop = await this.shopsCollection.findOne({_id: new ObjectId(shopId)}, {projection: project});
        if (!shop) return null;
        
        const { chargeflow_success_rate: chargeflowSuccessRate } = shop;
        this.shopsCache[shopId] = chargeflowSuccessRate;
        return chargeflowSuccessRate as number;
    }

    private async getDisputes(disputesList: string[]) {
        const objectIds = disputesList.map(id => new ObjectId(id));
        const project: Document = {
            disputeId: '$_id',
            amount: '$dispute.amountInUsd',
            _id: 0
        }
        const disputes = await this.disputesCollection.find({_id: {$in: objectIds}}, {projection: project})
            .toArray()
            .then(docs => docs as unknown as DisputeValidData[]);
        return disputes;
    }

    private async getPendingCharges(disputesList: string[]) {
        const objectIds = disputesList.map(id => new ObjectId(id));
        const project: Document = {
            disputeId: 1,
            shopId: 1,
            disputeRecoveredAmount: 1,
            successAmount: 1
        };
        const pendingCharges = await this.pendingChargesCollection.find({ disputeId: { $in: objectIds } }, { projection: project }).toArray() as WithId<PendingCharge>[];
        return pendingCharges;
    }

    private async* disputesListGenerator(disputesList: string[], bulkSize: number = 100) {
        for (let i = 0; i < disputesList.length; i += bulkSize) {
            yield disputesList.slice(i, i + bulkSize);
        }
    }

    private async readDisputesListFromFile(filePath: string) {
        const disputes = await fs.promises.readFile(filePath, 'utf8');
        return JSON.parse(disputes);
    }
}

const script = new FixAsanaPendingChargesAmount();
script.main();


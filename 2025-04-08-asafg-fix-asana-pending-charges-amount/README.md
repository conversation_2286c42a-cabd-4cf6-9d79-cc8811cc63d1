# Purpose:
Fix all Asana pending charges to their original amount

# Motivation:
In the Asana migration process, we mapped the wrong field as dispute amount (`Amount` instead of `Dispute Amount`).
In the previous script we fixed the amounts for all of these disputes.
In this script we should fix the amount for the relevant pending charges.


# Running this script
`npx ts-node ./script.ts <dry or wet-primary>`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**
This file will create an output file called `wrongPendingCharges.json` with data about all the wrong items found.

### How this script was tested
Tested on dry mode on prod, and wet-primary mode on dev.
import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Document, MongoClient  } from 'mongodb';
import { PgService } from '../shared/pg-service';
import knex from 'knex';

dotenv.config();

const pgService = new PgService();
interface InvoiceData {
  issueDate?: Date;
  invoiceExternalId: string;
  amount: number;
  currency: string;
  accountId: number;
  paymentMethodId: number;
  status?: string;
  description?: string;
  productReference: string;
  productType?: string;
}

export class InsertMissingInvoices extends ScriptBase {
    public getName(): string {
        return 'Insert missing invoices';
    }

    public getAuthor(): string {
        return 'Asaf Gamliel';
    }

    public getTicket(): string {
        return 'CHA-9644';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

  private async addInvoiceToDB(invoiceData: InvoiceData): Promise<void> {
    const knexClient = knex({ client: 'pg' });
    const query = knexClient('Invoice').insert({
      issueDate: invoiceData.issueDate,
      invoiceExternalId: invoiceData.invoiceExternalId,
      amount: invoiceData.amount,
      currency: invoiceData.currency,
      accountId: invoiceData.accountId,
      status: invoiceData.status,
      description: invoiceData.description,
      productReference: invoiceData.productReference,
      productType: invoiceData.productType,
      paymentMethodId: invoiceData.paymentMethodId
    });
    console.log('QUERY GENERATED', query.toString());
    await pgService.query(query.toQuery());
  }

  private async fetchPGAccountId(mongoAccountId: string): Promise<number> {
    const query = `SELECT id FROM public."Account" WHERE "mongoAccountId" = '${mongoAccountId}'`;
    console.log('QUERY GENERATED', query);
    const result = await pgService.query<{id: number}>(query);
    return result[0].id;
  }

  private async fetchPaymentMethodId(accountId: number): Promise<number> {
    const knexClient = knex({ client: 'pg' });
    const query = knexClient('PaymentMethod').select('id').where('accountId', accountId);
    console.log('QUERY GENERATED', query.toString());
    const result = await pgService.query<{ id: number }>(query.toQuery()) as any;
    return result[0].id;
  }

  private async fetchInvoiceData(caseId: string, invoiceExternalId: string): Promise<InvoiceData> {
    const mongoClient: MongoClient = await loadMongoClient();
    const db = mongoClient.db('chargeflow_api');
    const collection = db.collection('disputes');
    const query = { 'dispute.id': caseId };
    const projection: Document = { "dispute.id": 1, "dispute.amount": 1, "dispute.currency": 1, "accountId": 1 };
    const data = await collection.find(query, projection).toArray();
    const accountId = await this.fetchPGAccountId(data[0].accountId);
    console.log("accountId from fetchPGAccountId", accountId);
    const paymentMethodId = await this.fetchPaymentMethodId(accountId);
    console.log("paymentMethodId from fetchPaymentMethodId", paymentMethodId);
    return this.createInvoiceDataObject({
      productReference: data[0].dispute.id,
      amount: data[0].dispute.amount,
      currency: data[0].dispute.currency,
      accountId: accountId,
      invoiceExternalId: invoiceExternalId,
      paymentMethodId: paymentMethodId
    });

    }

    private createInvoiceDataObject(invoiceBaseData: InvoiceData): InvoiceData {
      return {
        productReference: invoiceBaseData.productReference,
        amount: invoiceBaseData.amount,
        currency: invoiceBaseData.currency,
        accountId: invoiceBaseData.accountId,
        description: `Chargeflow resolved dispute ${invoiceBaseData.productReference} success-fee`,
        status: 'paid',
        issueDate: new Date(),
        invoiceExternalId: invoiceBaseData.invoiceExternalId,
        productType: 'dispute',
        paymentMethodId: invoiceBaseData.paymentMethodId
      };
    }

    private async logic(context: ScriptContext, isDryRun: boolean) {
      const caseId = context.args[1];
      const stripeInvoiceId = context.args[2];

      const handleDryRun = async () => {
        const invoiceData = await this.fetchInvoiceData(caseId, stripeInvoiceId);
        console.log('Invoice data', invoiceData);
      };
      const handleWetRun = async () => {
        const invoiceData = await this.fetchInvoiceData(caseId, stripeInvoiceId);
        await this.addInvoiceToDB(invoiceData);
      };
      try {
        if (isDryRun) {
          await handleDryRun();
        } else {
          await handleWetRun();
        }
      } catch (err) {
        console.error(err);
        if (err instanceof Error) {
          throw err;
        }
      }
    }

    
}

const script = new InsertMissingInvoices();
script.main();

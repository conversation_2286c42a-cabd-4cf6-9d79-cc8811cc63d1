# Purpose:
Deletes pending charges of all paid invoices

# Motivation:
This is to fix an issue from the new billing to address the failed charges

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> <dispute-id> <stripe-invoice-id>`
NOTE: a `.env` file is expected to be present in CWD with **PG_READER_CONNECTION_STRING_DEV** and **MONGO_URI** 

### Example
```
$ npx ts-node ./script.ts wet-primary du_1P79LrCHjjFwITIMZGfCETw9 in_1Q0KBxFB8zXks4nkq32QFZka
Connecting to mongodb+srv://......mongodb.net/
Connected to mongodb+srv://......mongodb.net/
QUERY GENERATED SELECT id FROM public."Account" WHERE "mongoAccountId" = '662919b9050e6085fe1f5f2e'
{"level":"INFO","message":"Connected to Postgres","service":"service_undefined","timestamp":"2024-11-04T17:06:06.035Z"}
{"level":"INFO","message":"Disconnecting from Postgres","service":"service_undefined","timestamp":"2024-11-04T17:06:06.186Z"}
accountId from fetchPGAccountId 914
QUERY GENERATED select "id" from "PaymentMethod" where "accountId" = 914
{"level":"INFO","message":"Connected to Postgres","service":"service_undefined","timestamp":"2024-11-04T17:06:06.191Z"}
{"level":"INFO","message":"Disconnecting from Postgres","service":"service_undefined","timestamp":"2024-11-04T17:06:06.358Z"}
paymentMethodId from fetchPaymentMethodId 607
QUERY GENERATED insert into "Invoice" ("accountId", "amount", "currency", "description", "invoiceExternalId", "issueDate", "paymentMethodId", "productReference", "productType", "status") values (914, 71.99, 'GBP', 'Chargeflow resolved dispute du_1P79LrCHjjFwITIMZGfCETw9 success-fee', 'in_1Q0KBxFB8zXks4nkq32QFZka', '2024-11-04 19:06:06.359', 607, 'du_1P79LrCHjjFwITIMZGfCETw9', 'dispute', 'paid')
{"level":"INFO","message":"Connected to Postgres","service":"service_undefined","timestamp":"2024-11-04T17:06:06.361Z"}
{"level":"INFO","message":"Disconnecting from Postgres","service":"service_undefined","timestamp":"2024-11-04T17:06:06.569Z"}
```

### How this script was tested

 - [x] Wet run against dev


import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import {
    CognitoIdentityProviderClient,
    AdminDeleteUserCommand,
    AdminDisableUserCommand,
    ListUsersCommand
} from "@aws-sdk/client-cognito-identity-provider";
import { Db, MongoClient, ObjectId } from 'mongodb';
import fs from 'fs';

dotenv.config();

export class ScriptDeleteUser extends ScriptBase {
    db: Db | undefined = undefined;

    public getName(): string {
        return 'Delete a user';
    }

    public getAuthor(): string {
        return '<PERSON><PERSON>la';
    }

    public getTicket(): string {
        return 'CHA-4407';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async getCognitoUser(email: string) {
        const client = new CognitoIdentityProviderClient();
        const input = {
            UserPoolId: process.env.MERCHANT_POOL_ID,
            Filter: `email = "${email}"`,
            Limit: 1,
        };
        const command = new ListUsersCommand(input);
        const response = await client.send(command);

        return response?.Users?.[0];
    }

    private async deleteCognitoUser(email: string) {
        console.log('Deleting cognito user', email);
        if (this.mode !== 'dry') {
            const client = new CognitoIdentityProviderClient();
            const input = {
                UserPoolId: process.env.MERCHANT_POOL_ID,
                Username: email,
            };
            await client.send(new AdminDisableUserCommand(input));
            const command = new AdminDeleteUserCommand(input);
            const response = await client.send(command);
            console.log(response);
            console.log('Successfully delete cognito user', email);
        }
    }

    private async deleteDoc(
        db: Db,
        collectionName: string,
        filter: object
    ): Promise<void> {
        const collection = db.collection(collectionName);
        const item = await collection.findOne(filter);
        console.log('Deleting ', collectionName, item);
        // Write items to a JSON file
        fs.writeFile(`${collectionName}.json`, JSON.stringify(item, null, 2), console.log);

        if (this.mode !== 'dry')  {
            const res = await collection.deleteOne(filter);
            console.log(res);
        }
    }

    private async deleteDocs(
        db: Db,
        collectionName: string,
        filter: object
    ) {
        const collection = db.collection(collectionName);
        const items = await collection.find(filter).toArray();
        const itemsCount = items.length;

        console.log('Deleting ', collectionName, itemsCount);

        if (this.mode !== 'dry') {
            const res = await collection.deleteMany(filter);
            console.log(res);
        }

        // Write items to a JSON file
        fs.writeFile(`${collectionName}.json`, JSON.stringify(items, null, 2), console.log);
    }

    private async deleteAddonUser(
        db: Db,
        email: string,
        shop: any
    ) {
        await this.deleteCognitoUser(email);
        await this.deleteDoc(db, 'accountMappings', { email, chargeflowId: shop.chargeflow_id });
        await this.deleteDoc(db, 'companies',
            { 'users.email': email, 'users.chargeflowId': shop.chargeflow_id }
        );
    }

    private async deleteShop(
        db: Db,
        shop: any
    ) {
        const cognitoUser = await this.getCognitoUser(shop.email);
        console.log(cognitoUser);
        if (!cognitoUser) {
            throw new Error('No cognito user found.');
        }

        await this.deleteCognitoUser(shop.email);

        await this.deleteDoc(db, 'accountMappings', { email: shop.email, chargeflowId: shop.chargeflow_id });
        await this.deleteDoc(db, 'chargeflow_ids', { _id: shop.chargeflow_id });
        await this.deleteDoc(db, 'billings', { chargeflowId: shop.chargeflow_id });
        await this.deleteDocs(db, 'companies', {
            'users.email': shop.email,
            'users.chargeflowId': shop.chargeflow_id
        });
        await this.deleteDoc(db, 'shops', { name: shop.name });
        await this.deleteDoc(db, 'crms', { 'Chargeflow ID': shop.chargeflow_id.toString() });
        await this.deleteDoc(db, 'customers', { _id: shop.customer_id });
        await this.deleteDoc(db, 'userInfos', { chargeflowId: shop.chargeflow_id });
        await this.deleteDoc(db, 'settings', { chargeflow_id: shop.chargeflow_id });

        await this.deleteDocs(db, 'disputes', { chargeflowId: shop.chargeflow_id });
        await this.deleteDocs(db, 'processors', { chargeflow_id: shop.chargeflow_id });
        await this.deleteDocs(db, 'campaign', { cognitoUsername: cognitoUser.Username });
        await this.deleteDocs(db, 'businessUnits', { chargeflowId: shop.chargeflow_id });
    }

    private async runChecksShop(db: Db, shop: any) {
        const billingsCollection = db.collection('billings');
        const processorsCollection = db.collection('processors');
        const disputesCollection = db.collection('disputes');

        const cognitoUser = await this.getCognitoUser(shop.email);
        if (!cognitoUser) {
            throw new Error('No cognito user found.');
        }

        const billing = await billingsCollection.findOne({
            chargeflowId: shop.chargeflow_id
        });
        if (billing?.activeStatus) {
            throw new Error('User contains active billing!');
        }

        const processors = await processorsCollection.find({
            'chargeflow_id': shop.chargeflow_id
        }).toArray()
        if (processors.length) {
            console.warn('User already registered processor', processors.length, processors.map(
                p => p.processor_name
            ));
        }

        const disputesCount = await disputesCollection.countDocuments({
            chargeflowId: shop.chargeflow_id
        });
        console.warn('User ingested disputes.', disputesCount);
    }

    private async runChecksAddon(db: Db, email: string, shop: any) {
        const cognitoUser = await this.getCognitoUser(email);
        if (!cognitoUser) {
            throw new Error('No cognito user found.');
        }

        const companiesCollection = db.collection('companies');
        const accountMappings = db.collection('accountMappings');

        const company = await companiesCollection.findOne({
            'users.email': email,
            'users.chargeflowId': shop.chargeflow_id
        });
        if (!company) {
            throw new Error('No company found.');
        }

        const accountMapping = await accountMappings.findOne({
            email, chargeflowId: shop.chargeflow_id
        });

        if (!accountMapping) {
            throw new Error('No account mapping found.');
        }
    }

    private async logic(context: ScriptContext, isDryRun: boolean) {
        const shopName = context.args[1];
        const email = context.args[2];
        const isAddOn = context.args[3] === 'add-on';
        const client = await loadMongoClient();
        const db = client.db('chargeflow_api');

        if (!shopName) {
            throw new Error('Shop name is required.');
        }

        const shopsCollection = db.collection('shops');

        const shop = await shopsCollection.findOne({ name: shopName });
        if (!shop) {
            throw new Error('No shop found.');
        }

        if (isAddOn) {
            if (!email) {
                throw new Error('Email is required for add-on user.');
            }

            await this.runChecksAddon(db, email, shop);
        } else {
            await this.runChecksShop(db, shop);
        }

        try {
            if (isAddOn) {
                await this.deleteAddonUser(db, email, shop);
            } else {
                await this.deleteShop(db, shop);
            }
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        } finally {
            client.close();
        }
    }
}

const script = new ScriptDeleteUser();
script.main();

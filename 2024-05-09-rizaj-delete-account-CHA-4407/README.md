# Purpose:
Delete a user that signed up
and make sure the deleted items where backed up.

# Motivation:
To delete a user completely from the system

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> <shop name> <email address> <'add-on' for add-on user>| tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**, **MERCHANT_POOL_ID**
User must also have an active AWS session with permission to delete and deactivate user from the merchant cognito pool.

### How this script was tested
[x] Wet run against dev & test
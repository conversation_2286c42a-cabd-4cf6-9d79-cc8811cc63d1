import { ObjectId } from "mongodb";
import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { Db } from "mongodb";

dotenv.config();

export class Script extends ScriptBase {

  public db!: Db;
  public getName(): string {
    return "Fix null transaction.id for Wix disputes & run retro migration to fill value";
  }

  public getAuthor(): string {
    return "Ritz Jumola";
  }

  public getTicket(): string {
    return "CHA-13317";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow([
        "MONGO_URI",
        "MONGO_DB_NAME",
        "CHARGEFLOW_ID",
      ]);

      const mongo = await loadMongoClient();
      console.log("Mongo client loaded");

      this.db = mongo.db(process.env.MONGO_DB_NAME);

      const DISPUTES = this.db.collection("disputes");

      const FILTER = {
        chargeflowId: new ObjectId(process.env.CHARGEFLOW_ID),
        'transaction.id': null,
        'transaction.rawData.transaction': { $ne: null },
        'dispute.source': 'stripe',
      };

      console.log("Filter", FILTER);

      if (isDryRun) {
        console.log("Dry run mode");
        const currentCount = await DISPUTES.countDocuments(FILTER);
        console.log(`Found ${currentCount} items`);
      } else {
        const updateManyRes = await DISPUTES.updateMany(FILTER, [
            { $set: { "transaction.id": "$transaction.rawData.transaction" } },
        ]);
        console.log('updateManyRes %j', updateManyRes);
        console.log(`Updated ${updateManyRes.modifiedCount} items`);
      }
    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }
}

const script = new Script();
script.main();

# Purpose:

Backfill accountId on imported disputes by looking up the corresponding account via chargeflowId.

# Motivation:

Imported disputes have null accountId values that need to be populated from the accounts collection for consistency and proper account association.

# Desired state
- All imported disputes should have their accountId field populated based on the matching account's legacy.chargeflowId
- The accountId should match the _id from the accounts collection

# Running this script

`npx tsx ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with

```
MONGO_URI='MONGO_URI'
MONGO_DB_NAME='MONGO_DB_NAME'
```

### How this script was tested
[ ] Dry run against dev to verify correct accountId mapping
[ ] Wet run against dev & prod with data diff validation afterwards
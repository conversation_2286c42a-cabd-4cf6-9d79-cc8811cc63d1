import dotenv from "dotenv";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { Db } from "mongodb";

dotenv.config();

// Updated filter for imported disputes with null or non-existent accountId
const importedDisputesFilter = {
  "dispute.source": "import",
  $or: [
    { accountId: null },
    { accountId: { $exists: false } }
  ]
};

export class ScriptBackfillAccountId extends ScriptBase {
  db: Db | undefined = undefined;

  public getName(): string {
    return "Backfill accountId for imported disputes";
  }

  public getAuthor(): string {
    return "Dominik <PERSON>k";
  }

  public getTicket(): string {
    return "CHA-11689";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(context: ScriptContext, isDryRun: boolean) {
    const client = await loadMongoClient();
    const disputesCollection = client
      .db("chargeflow_api")
      .collection("disputes");

    while (true) {
      const pipeline = [
        {
          $match: importedDisputesFilter,
        },
        {
          $limit: 200,
        },
        {
          $lookup: {
            from: "account",
            let: { chargeflowId: "$chargeflowId" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ["$legacy.chargeflowId", "$$chargeflowId"],
                  },
                },
              },
            ],
            as: "account",
          },
        },
        {
          $match: {
            "account.0": { $exists: true }, // Only keep disputes where we found an account
          },
        },
        {
          $project: {
            _id: 1,
            accountId: { $arrayElemAt: ["$account._id", 0] },
          },
        },
      ];

      if (isDryRun) {
        const disputes = await disputesCollection.aggregate(pipeline).toArray();
        console.log(`Will update ${disputes.length} disputes`);
        console.log("Sample updates:");
        disputes.slice(0, 5).forEach((dispute) => {
          console.log(
            `Dispute ${dispute._id}: Will set accountId to ${dispute.accountId}`
          );
        });
      } else {
        const disputes = await disputesCollection.aggregate(pipeline).toArray();

        if (isDryRun || disputes.length === 0) {
          console.log(isDryRun ? "Finished first iteration - Ending dry run" : "No more disputes to update - Ending script");
          return;
        }

        // Create bulk operations for updating
        const bulkOps = disputes.map((dispute) => ({
          updateOne: {
            filter: { _id: dispute._id },
            update: {
              $set: { accountId: dispute.accountId },
            },
          },
        }));

        const result = await disputesCollection.bulkWrite(bulkOps);
        console.log(`Updated ${result.modifiedCount} disputes`);
      }
    }
  }
}

const script = new ScriptBackfillAccountId();
script.main();

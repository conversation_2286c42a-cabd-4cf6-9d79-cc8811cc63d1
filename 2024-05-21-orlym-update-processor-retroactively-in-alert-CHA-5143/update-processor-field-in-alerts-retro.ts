import { ObjectId, AnyBulkWriteOperation, Document, MongoClient } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext, ScriptMode } from '../shared/script-base';
import processorMapper from './processorMapper.json';

type processorMapperType = typeof processorMapper;
type AcquirerBin = keyof processorMapperType;
type Alert = {
  _id: ObjectId;
  acquirerBin: string | null;
};

class Script extends ScriptBase {

  public getName(): string {
    return 'Update processor field in alerts retroactively';
  }

  public getAuthor(): string {
    return 'Orly Moshe';
  }

  public getTicket(): string {
    return 'CHA-5143';
  }

  async dryRun(context: ScriptContext) {
    await this.logic(context);
  }

  wetRunOnSecondary(_context: ScriptContext): Promise<void> {
    throw new Error('Method not implemented.');
  }

  async wetRunOnPrimary(context: ScriptContext) {
    await this.logic(context);
  }

  private async logic(ctx: ScriptContext) {
    const mode = (ctx.args[0] ?? 'dry') as ScriptMode;
    const client: MongoClient = await loadMongoClient();

    const alerts = await this.findAlertsWithoutProcessor(client);
    const updateProcessorFieldOps = await this.createUpdateProcessorFieldOps(alerts);

    if (mode === 'dry') {
      console.log('Dry run mode');
      console.log('Updating %s alerts: ', updateProcessorFieldOps.length);
      console.log('Updating the following alerts registries: ', JSON.stringify(updateProcessorFieldOps));
    }
    else {
      const updatedCount = await this.updateAlert(client, updateProcessorFieldOps);
      console.log('Updating the following alerts registries: ', JSON.stringify(updateProcessorFieldOps));
      console.log('Updated %s alerts', updatedCount);
    }
  }

  private async findAlertsWithoutProcessor(client: MongoClient): Promise<Alert[] | []> {
    try {
      return client
        .db('chargeflow_api')
        .collection('alerts')
        .find(
          {
            'processor': null,
            'acquirerBin': { $ne: null }
          },
          {
            'projection': {
              '_id': 1,
              'acquirerBin': 1,
            },
          },
        )
        .toArray()
        .then((alerts) => alerts.map((alert) => (
          { _id: alert._id, acquirerBin: alert.acquirerBin })));
    }
    catch (error) {
      console.error('Failed to find alerts %s', error);
      return [];
    }
  }

  private async createUpdateProcessorFieldOps(data: Alert[]): Promise<AnyBulkWriteOperation<Document>[]> {

    const updateProcessorFieldOps: AnyBulkWriteOperation<Document>[] = [];

    for (const item of data) {
      const processor = item.acquirerBin ? processorMapper[item.acquirerBin as AcquirerBin] : null;
      if (processor) {
        updateProcessorFieldOps.push({
          updateOne: {
            filter: { '_id': item._id as ObjectId },
            update: {
              $set: {
                'processor': item.acquirerBin ? processorMapper[item.acquirerBin as AcquirerBin] : null,
              }
            }
          }
        });
      }
    }

    return updateProcessorFieldOps;
  }

  private async updateAlert(client: MongoClient, updateOps: AnyBulkWriteOperation<Document>[] | []): Promise<number> {
    try {
      return client
        .db('chargeflow_api')
        .collection('alerts')
        .bulkWrite(
          updateOps,
          { ordered: false })
        .then((result) => result.modifiedCount);
    }
    catch (error) {
      console.error('Failed to update alerts %s', error);
      return 0;
    }
  }
}

const script = new Script();
script.main();

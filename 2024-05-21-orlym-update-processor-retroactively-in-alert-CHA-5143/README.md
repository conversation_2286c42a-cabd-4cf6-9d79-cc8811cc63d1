# Purpose
This script updates alert's processor by using a map sent by CBHit

https://linear.app/chargeflow/issue/CHA-5143/write-a-script-for-updating-retro-alerts

## Motivation
Right now we don't get in the alert obejct from CBHit the processor so we need to update it using a map. 

## Running this script
`npx ts-node ./update-processor-field-in-alerts-retro.ts <dry or wet-secondary or wet-primary> 2>&1 | tee log.txt`

NOTE: a `.env` file is expected to be present in CWD - using `MONGO_URI`
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console 
wet-secondary - not utilized in this script
wet-primary - updates the ALERTS collection.

### How this script was tested
[x] Alerts have:
  [x] processor != null
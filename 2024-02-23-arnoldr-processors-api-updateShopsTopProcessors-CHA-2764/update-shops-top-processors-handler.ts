
import { SQSClient, SendMessageBatchCommand } from "@aws-sdk/client-sqs";
import { randomUUID } from 'crypto'
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { loadMongoClient } from "../shared/mongo-config";

import dotenv from 'dotenv';
dotenv.config();

const SHOP_GATEWAYS_QUEUE_URL =
  process.env.SHOP_GATEWAYS_QUEUE_URL
interface FailedChargeflowId {
  chargeflowIds: string[];
  reason: string;
}
const failedChargeflowIds: FailedChargeflowId[] = [];

type RunMode = 'dry' | 'wet'

const scriptFn = async ({ mode = 'dry' }: { mode: RunMode}) => {
  console.log("Fetching shops....");

  const shopsChargeflowId = await fetchShopifyShops();
  const shopsFound = shopsChargeflowId.length;
  console.log("Shops found:", shopsFound);

  const chargeflowIdsBatches = chunkArray(shopsChargeflowId, 10);

  let totalSent = 0;
  console.log("Sending to Queue....");

  for (const chargeflowIds of chargeflowIdsBatches) {
    if (mode === 'wet') {
      await sendToSaveShopifyGatewaysQueue(chargeflowIds);
    }
    
    totalSent += chargeflowIds.length;

    console.log(
      `Sent ${
        totalSent === 0 ? chargeflowIds.length : totalSent
      } out of ${shopsFound} shops to queue...`
    );
  }

  if (failedChargeflowIds.length) {
    console.log("Failed to send to queue:", failedChargeflowIds);
  }

  console.log("Done!");

  process.exit(0);
};

const sendToSaveShopifyGatewaysQueue = async (chargeflowIds: string[]) => {
  try {
    const client = new SQSClient();
    const input = {
      Entries: chargeflowIds.map((chargeflowId) => ({
        Id: randomUUID(),
        MessageBody: JSON.stringify(chargeflowId),
      })),
      QueueUrl: SHOP_GATEWAYS_QUEUE_URL,
    };
    const command = new SendMessageBatchCommand(input);
    const response = await client.send(command);
    const { Failed } = response;

    if (Failed?.length) {
      const errorMsg = Failed.map((f) => f.Message).join(", ");
      failedChargeflowIds.push({ chargeflowIds, reason: errorMsg });
    }
  } catch (error: any) {
    const errorMsg = error.message;
    failedChargeflowIds.push({ chargeflowIds, reason: errorMsg });
    throw error;
  }
};

const fetchShopifyShops = async (): Promise<string[]> => {
  const client = await loadMongoClient();
  const collection = await client
    .db("chargeflow_api")
    .collection("shopifyShopsGateways");

  const result = await collection
    .find({}, { projection: { chargeflowId: 1, _id: 0 } })
    .toArray();

  return result.map((res:any) => String(res.chargeflowId));
};

const chunkArray = (array: string[], chunkSize: number) => {
  const result: string[][] = [];

  for (let i = 0; i < array.length; i += chunkSize) {
    result.push(array.slice(i, i + chunkSize));
  }

  return result;
};


class Script extends ScriptBase {
  getName = () => 'Update Shopify Shops Top Processors data which is being tracked in shopifyGateways collection';

  getAuthor = () => 'Arnold Ramos';

  getTicket = () => 'CHA-2764';

  async dryRun() {
    await scriptFn({ mode: 'dry' });
  }

  async wetRunOnPrimary(_context: ScriptContext): Promise<void> {
    await scriptFn({ mode: 'wet' });
  }

  async wetRunOnSecondary(_context: ScriptContext): Promise<void> {
    console.log('Not supported. Resending messages to a queue.')
  }
}

const script = new Script();
script.main();
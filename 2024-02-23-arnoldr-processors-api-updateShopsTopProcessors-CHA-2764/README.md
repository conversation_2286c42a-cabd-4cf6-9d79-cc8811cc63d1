## Resending shops to queue to refetch and update top gateways / processors

This script updates/syncs the top processors used by a shopify shop in `shopifyGateways` collection.

How to run the script (from crm-reconciliation-script folder):
```
    1. run `npm install`
    2. In your .env file, within this folder, set value for MONGO_URI and SHOP_GATEWAYS_QUEUE_URL
    3. run 'npm run update-shops-top-processors-handler
```

Steps to use:

```
# summary
- dry run in test 
- wet run in test 
- dry run in prod
- wet run in prod

# commands
while connected to TEST env
ts-node update-shops-top-processors-handler.ts dry
ts-node update-shops-top-processors-handler.ts wet-primary

while connected to PROD env
ts-node update-shops-top-processors-handler.ts dry
ts-node update-shops-top-processors-handler.ts wet-primary
```
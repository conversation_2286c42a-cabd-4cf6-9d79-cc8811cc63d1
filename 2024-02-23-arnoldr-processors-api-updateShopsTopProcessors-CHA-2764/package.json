{"name": "shops-top-processors", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"update-shops-top-processors-handler": "ts-node update-shops-top-processors-handler.ts"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"dotenv": "^16.4.5", "npm-check-updates": "^16.10.12", "npm-run-all": "^4.1.5", "proxyquire": "^2.1.3", "ts-migrate": "^0.1.35", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "optionalDependencies": {"mongodb": "^5.6.0"}, "dependencies": {"@aws-sdk/client-sqs": "^3.521.0"}}
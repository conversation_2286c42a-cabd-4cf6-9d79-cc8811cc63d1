import { MongoClient, Db, ObjectId, AnyBulkWriteOperation, Document, UpdateFilter } from 'mongodb';

export interface Logger {
    log: (message: string) => void;
}

interface Processor {
    _id: ObjectId;
    accountId: ObjectId;
    processor_name: string;
}

interface Shop {
    _id: ObjectId;
    accountId: ObjectId;
    platform: string;
}

export class AssignProcessorsToShops {
    private readonly DB_NAME = 'chargeflow_api';
    private readonly SHOPS_COLLECTION_NAME = 'shops';
    private readonly PROCESSORS_COLLECTION_NAME = 'processors';

    private db: Db;

    constructor(
        mongoClient: MongoClient,
        private readonly logger: Logger,
    ) {
        this.db = mongoClient.db(this.DB_NAME);
    }

    async execute(batchSize: number, page: number, dryRun: boolean): Promise<void> {
        try {
            this.logger.log('Setting processors for shops started, please wait ...');
            const projection = {
                _id: 1,
                accountId: 1,
                platform: 1,
            };
            const totalItems = await this.db.collection<Shop>(this.SHOPS_COLLECTION_NAME).countDocuments({});
            const totalBatches = Math.ceil(totalItems / batchSize);

            this.logger.log(`Total documents to process: ${totalItems}`);
            this.logger.log(`Total batches to process: ${totalBatches}`);

            if (dryRun) {
                this.logger.log('Dry run mode enabled, no changes made to the database');
                return;
            }

            for (let currentBatch = page; currentBatch <= 1; currentBatch++) {
                try {
                    this.logger.log(`Processing batch ${currentBatch} of ${totalBatches}`);
                    const currentPage = (currentBatch - 1) * batchSize;
                    const shops = await this.db.collection<Shop>(this.SHOPS_COLLECTION_NAME)
                        .find({})
                        .project(projection)
                        .skip(currentPage)
                        .limit(batchSize)
                        .toArray();
                    this.logger.log(`Shops size: ${shops.length}`);

                    const accountIds = shops.map(shop => new ObjectId(shop.accountId));
                    const processorsItemFilter = { accountId: { $in: accountIds } };
                    const processors = await this.db.collection<Processor>(this.PROCESSORS_COLLECTION_NAME)
                        .find(processorsItemFilter)
                        .project({ _id: 1, accountId: 1, processor_name: 1 })
                        .toArray();

                    const processorsByAccountId = processors.reduce((acc, processor) => {
                        if (!acc.has(processor.accountId.toString())) {
                            acc.set(processor.accountId.toString(), []);
                        }

                        acc.get(processor.accountId.toString()).push(processor);
                        return acc;
                    }, new Map<string, Processor[]>());
                    const bulkOperations: AnyBulkWriteOperation<Document>[] = [];

                    for (const shop of shops) {
                        const matchingProcessors = processorsByAccountId.get(shop.accountId?.toString()) || [];
                        const matchingProcessorsByType = matchingProcessors.reduce((acc: Map<string, Processor[]>, processor: Processor) => {
                            if (!acc.has(processor.processor_name)) {
                                acc.set(processor.processor_name, []);
                            }
                            acc.get(processor.processor_name)!.push(processor);
                            return acc;
                        }, new Map<string, Processor[]>());
                        for (const [processorType, processors] of matchingProcessorsByType) {
                            const processorIds = processors.map((processor: Processor)  => processor._id.toString());
                            const updateFilter = {
                                updateOne: {
                                    filter: { _id: new ObjectId(shop._id) },
                                    update: [
                                        {
                                            $set: {
                                                processors: {
                                                    $cond: {
                                                        if: {
                                                            $in: [processorType, { $ifNull: ["$processors.type", []] }]
                                                        },
                                                        then: {
                                                            $map: {
                                                                input: { $ifNull: ["$processors", []] },
                                                                as: "processor",
                                                                in: {
                                                                    $cond: {
                                                                        if: { $eq: ["$$processor.type", processorType] },
                                                                        then: {
                                                                            $mergeObjects: [
                                                                                "$$processor",
                                                                                {
                                                                                    ids: { $setUnion: ["$$processor.ids", processorIds] }
                                                                                }
                                                                            ]
                                                                        },
                                                                        else: "$$processor"
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        else: {
                                                            $concatArrays: [
                                                                { $ifNull: ["$processors", []] },
                                                                [{ type: processorType, ids: processorIds }]
                                                            ]
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    ]
                                }
                            };
                            bulkOperations.push(updateFilter);
                        }
                    }

                    if (bulkOperations.length > 0) {
                        const bulkResult = await this.db.collection(this.SHOPS_COLLECTION_NAME).bulkWrite(bulkOperations);
                        this.logger.log(`Updated ${bulkResult.modifiedCount} shops with processorIds.`);
                    } else {
                        this.logger.log('No shops to update.');
                    }

                    this.logger.log(`Batch ${currentBatch} of ${totalBatches} processed successfully`);
                } catch (error) {
                    this.logger.log(`Batch ${currentBatch} of ${totalBatches} failed, reason: ${error instanceof Error ? error.message : 'unknown error'}`);
                    throw error;
                }
            }

            this.logger.log('Setting processors for shops finished successfully');
        } catch (error) {
            this.logger.log(`Setting processors for shops failed, reason: ${error instanceof Error ? error.message : 'unknown error'}`);
            throw error;
        }
    }
}

# Purpose
This script assigns processorIds to shops.

https://linear.app/chargeflow/issue/CHA-4989/prepare-migration-script-to-add-processorids-to-shop

## Motivation
Every shop should have collection of assigned processorIds.

## Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

1. Set the following environment variables in CWD:
```
MONGO_URI=<db connection string>
BATCH_SIZE=<batch size>
PAGE=<on which migration stops if not provided starting from page 1>
```

2. Install deps from CWD: `npm i`

3. Run the `dry` mode first. See the number of projected items to migrate and adjust the `BATCH_SIZE` env var.

4. Run the migration in `wet-primary` mode once everything is prepared.


### Modes
dry - does not update anything, writes the results to the console
wet-secondary - not implemented
wet-primary - executes the actual migration

### How this script was tested
- [x] test migration against `dev` db
- [x] test migration against `test` db

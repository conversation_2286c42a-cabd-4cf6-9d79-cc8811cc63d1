# Purpose:
To stamp submitted disputes with success rate
# Motivation:
We want to start using success rate on date of stamping, for this we need every dispute to be stamped with success rate.
# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**, **ROCKSET_API_KEY**, **ROCKSET_DB_NAME** and **ROCKSET_ENDPOINT**

### How this script was tested
[x] Wet run against dev 
import dotenv from 'dotenv';
import { ObjectId } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import RocksetClient from '../shared/rockset';
import { ScriptBase, ScriptContext } from '../shared/script-base';

dotenv.config();

type IdAndRate = {
  chargeflowId: {
    id: string;
  };
  chargeflow_success_rate: number;
};

export class ScriptStampSuccessRate extends ScriptBase {
  public getName(): string {
    return 'stamp success Rate On Dispute.';
  }

  public getAuthor(): string {
    return 'Liza Bogorad';
  }

  public getTicket(): string {
    return 'CHA-4501';
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log('Running in dry run mode (not changing anything)');
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error('Method not implemented.');
  }

  getUpdateDisputesQuery(idAndRate: IdAndRate) {
    const chargeflowId = idAndRate.chargeflowId?.id;
    const successRate = idAndRate.chargeflow_success_rate;
    if (!chargeflowId || successRate === undefined || successRate === null) {
      console.log('No shop found for', idAndRate);
      return;
    }
    const filter = {
      chargeflowId: new ObjectId(chargeflowId),
      'chargeflow.submitted.submittedAt': { $ne: null },
      'lastDisputeStatus.status': { $nin: ['won', 'warning_won', 'lost'] },
    };

    const update = {
      $set: { 'chargeflow.submitted.successRate': successRate },
    };
    return { updateMany: { filter, update } };
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    const rocksetClient = new RocksetClient();
    const mongoClient = await loadMongoClient();
    const rocksetDBName = process.env.ROCKSET_DB_NAME;
    const disputesCollection = mongoClient
      .db('chargeflow_api')
      .collection('disputes');
    const query = rocksetClient
      .queryBuilder()
      .select('d.chargeflowId', 'shops.chargeflow_success_rate')
      .fromRaw(`${rocksetDBName}.disputes_view d`)
      .leftJoin(
        `${rocksetDBName}.shops`,
        'd.chargeflowId.id',
        'shops.chargeflow_id.id'
      )
      .whereRaw(
        'd.chargeflow.submitted.submittedAt IS NOT NULL AND d.chargeflow.submitted.successRate IS NULL'
      )
      .whereNotIn('d.lastDisputeStatus.status', ['won', 'warning_won', 'lost'])
      .groupBy('d.chargeflowId', 'shops.chargeflow_success_rate')
      .toQuery();

    const handleDryRun = async () => {
      console.log('Running in dry run mode (not changing anything),', query);
      const toUpdate: IdAndRate[] = await rocksetClient.executeQuery(query);
      console.log('Update queries...');
      const updateQueries = toUpdate
        .map((idAndRate: IdAndRate) => this.getUpdateDisputesQuery(idAndRate))
        .filter(Boolean);
      console.log('update queries:', JSON.stringify(updateQueries, null, 2));
    };

    const handleWetRun = async () => {
      const toUpdate = await rocksetClient.executeQuery(query);
      console.log(`Found ${toUpdate.length} chargeflowId's to update.`);
      const updateQueries = toUpdate
        .map((idAndRate: IdAndRate) => this.getUpdateDisputesQuery(idAndRate))
        .filter(Boolean);
      if (!updateQueries.length) {
        console.log('No disputes to update.');
        return;
      }
      const res = await disputesCollection.bulkWrite(updateQueries);
      console.log('Execution result...');
      console.log(JSON.stringify(res, null, 2));
    };

    try {
      if (isDryRun) {
        await handleDryRun();
      } else {
        await handleWetRun();
      }
    } catch (err) {
      console.error(err);
      if (err instanceof Error) {
        throw err;
      }
    } finally {
      mongoClient.close();
    }
  }
}

const script = new ScriptStampSuccessRate();
script.main();

import '@shopify/shopify-api/adapters/node';
// @ts-ignore
import {ScriptBase} from '../shared/script-base';
import dotenv from 'dotenv';
import {MongoClient, ObjectId} from 'mongodb';
import {ParamsConfig} from '@chargeflow-team/chargeflow-utils-sdk';

dotenv.config();

export class CreateShopifySubscriptionScript extends ScriptBase {
    public getName() {
        return 'Create Shopify Subscription Script';
    }

    public getAuthor() {
        return 'Georgy Bunin';
    }

    public getTicket(): string {
        return 'CHA-13225';
    }

    private async findOneShopByChargeflowId(chargeflowId: string, mongoClient: MongoClient) {
        if (!chargeflowId) {
            throw new Error('Missing chargeflowId');
        }

        return mongoClient.db('chargeflow_api')
            .collection('shops')
            .findOne({'chargeflow_id': new ObjectId(chargeflowId)});
    }

    private async saveOneBilling(billing: any, mongoClient: MongoClient) {
        return mongoClient.db('chargeflow_api').collection('billings').insertOne(billing);
    }

    private async rawRequest(access_token: string, cappedAmount: number, store: string, test: boolean = true) {
        return fetch(`https://${store}.myshopify.com/admin/api/2025-01/recurring_application_charges.json`, {
            method: 'POST',
            headers: {
                'X-Shopify-Access-Token': access_token,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                'recurring_application_charge': {
                    'name': 'Chargeflow Success',
                    'price': 0.0,
                    'return_url': `https://app.chargeflow.io/dashboard?isBillingApproved=true`,
                    'capped_amount': cappedAmount,
                    'terms': `You'll be charged a 25% fee only if we successfully recover a chargeback on your behalf.`,
                    'test': test,
                }
            })
        }).then(res => res.json());
    }

    private async createShopifySubscription(chargeflowId: string, mongoClient: MongoClient, test = true) {
        console.log('createShopifyRecurringCharge', chargeflowId);

        const cappedAmountByPlan = Object.freeze({
            'Shopify Employees': 500,
            'Shopify Basic': 500,
            'Shopify Affiliate': 500,
            'Shopify Trial': 500,
            'Shopify': 1000,
            'Shopify Advanced': 2000,
            'Shopify Plus': 2000,
        });

        const shop = await this.findOneShopByChargeflowId(chargeflowId, mongoClient);
        const shopName = shop?.name;
        if (!shopName) {
            throw new Error('shopName not found');
        }

        const accessToken = shop?.connect?.access_token;
        if (!accessToken) {
            throw new Error(`accessToken not found in shopName=${shopName}`);
        }

        // @ts-ignore
        const cappedAmount = cappedAmountByPlan[shop.shopify_plan];

        const response = await this.rawRequest(accessToken, cappedAmount, shopName, test);

        const recurringAppCharge = response.body?.['recurring_application_charge'] ?? {};


        const billingObject = {
            dateCreated: new Date(),
            dateUpdated: new Date(),
            billingDate: recurringAppCharge.billing_on ? new Date(recurringAppCharge.billing_on) : null,
            chargeflowId: new ObjectId(chargeflowId),
            accountId: shop.accountId && new ObjectId(shop.accountId),
            shopId: shop._id,
            shopName,
            activeRecurringAppChargeId: null,
            pendingRecurringAppChargeId: recurringAppCharge.id,
            activeStatus: false,
            pendingStatus: true,
            canceledStatus: false,
            activeCappedAmount: null,
            activeBalanceUsed: null,
            activeBalanceRemaining: null,
            pendingCappedAmount: Number(recurringAppCharge.capped_amount),
            rawData: [recurringAppCharge],
        };

        if (!test) {
            await this.saveOneBilling(billingObject, mongoClient);
        }

        return {
            billingObject,
            billingStatus: recurringAppCharge.status,
            confirmationUrl: recurringAppCharge.confirmation_url,
        };
    }

    public async dryRun(): Promise<void> {
        await ParamsConfig.loadParameters();

        const chargeflowId = '67c737d62f96715ade20f4eb';

        const options = {useNewUrlParser: true, useUnifiedTopology: true, auth: JSON.parse(process.env.MONGO_AUTH!)};
        const mongoClient = await MongoClient.connect(process.env.MONGO_URI!, options);

        const res = await this.createShopifySubscription(chargeflowId, mongoClient);

        console.log(res);
    }

    public async wetRunOnPrimary(): Promise<void> {
        await ParamsConfig.loadParameters();
        throw new Error('Not implemented');
    }

    public async wetRunOnSecondary(): Promise<void> {
        await ParamsConfig.loadParameters();
        throw new Error('Not implemented');
    }
}

const script = new CreateShopifySubscriptionScript();

script.main();









export interface  GithubVulnerabilityAlerts{
  data: Data;
}

interface Data {
  organization: Organization;
}

interface Organization {
  repositories: Repositories;
}

interface Repositories {
  nodes: RepositoryDetails[];
}

export interface RepositoryDetails {
  name: string;
  vulnerabilityAlerts: VulnerabilityAlerts;
}

interface VulnerabilityAlerts {
  nodes: Node[];
}

interface Node {
  vulnerableManifestPath: string;
  vulnerableRequirements: string;
  securityVulnerability: SecurityVulnerability;
}

interface SecurityVulnerability {
  package: Package;
  severity: string;
  advisory: Advisory;
  firstPatchedVersion?: FirstPatchedVersion;
}

interface FirstPatchedVersion {
  identifier: string;
}

interface Advisory {
  description: string;
  identifiers: Identifier[];
}

interface Identifier {
  type: string;
  value: string;
}

interface Package {
  name: string;
  ecosystem: string;
}

export interface GithubVulnerabilityAlertsMapped {
  repoName: string;
  findings: Finding[];
}

interface Finding {
  origin: string;
  package: string;
  currentVersion: string;
  description: string;
  fixedVersion?: string;
  severity: string;
  identifiers: Identifier[];
}

interface Identifier {
  type: string;
  value: string;
}

export interface CreateLinearIssueResponse {
  data: Data;
}

interface Data {
  issueCreate: IssueCreate;
}

interface IssueCreate {
  success: boolean;
  issue: Issue;
}

interface Issue {
  id: string;
}
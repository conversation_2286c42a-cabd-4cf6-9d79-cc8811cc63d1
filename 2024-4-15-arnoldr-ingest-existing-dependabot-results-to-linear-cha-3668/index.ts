import { <PERSON>riptBase, <PERSON>riptContext, ScriptMode } from "../shared/script-base"
import { fetchRepositoryVulnerabilities } from "./github-api-helper"
import { createLinearIssue } from "./linear-api-helper"

const scriptFn = async ({ mode } : { mode: ScriptMode})=>{
  const vulnerabilities = await fetchRepositoryVulnerabilities()
  if (mode === 'dry') {
    vulnerabilities.forEach(vulnerability => {
      console.log(`Reponame: ${vulnerability.repoName}, Vulnerabilities Found: ${vulnerability.findings.length}`)
    })
    return
  }

  for (const vulnerability of vulnerabilities) {
    await createLinearIssue(vulnerability)
  }
}


class Script extends ScriptBase {
  getName = () => 'Ingest existing dependabot results to Linear';

  getAuthor = () => 'Arnold Ramos';

  getTicket = () => 'CHA-3668';

  async dryRun() {
    await scriptFn({ mode: 'dry' });
  }

  async wetRunOnPrimary(_context: ScriptContext): Promise<void> {
    await scriptFn({ mode: 'wet-primary' });
  }

  async wetRunOnSecondary(_context: ScriptContext): Promise<void> {
    console.log('Not supported. Resending messages to a queue.')
  }
}

const script = new Script();
script.main();
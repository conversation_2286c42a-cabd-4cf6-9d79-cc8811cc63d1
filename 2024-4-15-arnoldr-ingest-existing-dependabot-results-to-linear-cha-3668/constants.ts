export const SECURITY_LABEL_ID = process.env.SECURITY_LABEL_ID
export const TEAM_ID = process.env.TEAM_ID
export const PACKAGE_VULNERABILITY_LABEL_ID = process.env.PACKAGE_VULNERABILITY_LABEL_ID

export const LINEAR_API_KEY = process.env.LINEAR_API_KEY

export const GITHUB_TOKEN = process.env.GITHUB_TOKEN 

export const GITHUB_VULNERABILTY_QUERY = `
query {
  organization(login: "chargeflow-team") {
    repositories(first: 100) {
      nodes {
        name
        vulnerabilityAlerts(first: 100) {
          nodes {
            vulnerableManifestPath
            vulnerableRequirements
            securityVulnerability {
              package {
                name
                ecosystem
              }
              severity
              advisory {
                description
                identifiers {
                  type
                  value
                }
              }
              firstPatchedVersion {
                identifier
              }
            }
          }
        }
      }
    }
  }
}
`;
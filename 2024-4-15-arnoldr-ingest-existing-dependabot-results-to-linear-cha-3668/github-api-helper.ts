import { GITHUB_TOKEN, GITHUB_VULNERABILTY_QUERY } from "./constants";
import {
  GithubVulnerabilityAlerts,
  GithubVulnerabilityAlertsMapped,
} from "./types";



export const fetchRepositoryVulnerabilities = async (): Promise<
  GithubVulnerabilityAlertsMapped[]
> => {
  const queryResult = await queryFromGithubGql();
  return queryResult.map((repo) => {
    const highAndCritical = repo.vulnerabilityAlerts.nodes.filter(
      (alert) =>
        alert.securityVulnerability.severity === "HIGH" ||
        alert.securityVulnerability.severity === "CRITICAL"
    );
    return {
      repoName: repo.name,
      findings: highAndCritical.map((alert) => ({
        origin: alert.vulnerableManifestPath,
        package: alert.securityVulnerability.package.name,
        currentVersion: alert.vulnerableRequirements,
        description: alert.securityVulnerability.advisory.description,
        severity: alert.securityVulnerability.severity,
        fixedVersion:
          alert.securityVulnerability.firstPatchedVersion?.identifier,
        identifiers: alert.securityVulnerability.advisory.identifiers,
      })),
    };
  });
};

const queryFromGithubGql = async () => {
  const response = await fetch("https://api.github.com/graphql", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${GITHUB_TOKEN}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ query:GITHUB_VULNERABILTY_QUERY }),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.statusText}`);
  }

  const data: GithubVulnerabilityAlerts = await response.json();
  return data.data?.organization?.repositories?.nodes;
};

import { LINEAR_API_KEY, PACKAGE_VULNERABILITY_LABEL_ID, SECURITY_LABEL_ID, TEAM_ID } from "./constants";
import { CreateLinearIssueResponse, GithubVulnerabilityAlertsMapped } from "./types";

export const createLinearIssue = async(alerts:GithubVulnerabilityAlertsMapped)=>{
  await linearRequest(createLinearTicketQuery(alerts));
  console.log(`Created Linear Issue for ${alerts.repoName}`);
}

const linearRequest = async(query:string) : Promise<CreateLinearIssueResponse>=>{
  const response = await fetch('https://api.linear.app/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': LINEAR_API_KEY!,
    },
    body: JSON.stringify({
      query,
    }),
  });
  
  return response.json();
}

const createLinearTicketQuery = (alerts:GithubVulnerabilityAlertsMapped) => {
  const description = alerts.findings.map(finding => {
    const identifiers = finding.identifiers.map(identifier => 
      `    ${identifier.type} : \`${identifier.value}\``
    ).join('\n');

    return `Package: \`${finding.package}\`\nSeverity:\`${finding.severity}\`\nCurrent Version: \`${finding.currentVersion}\`\nFixed Version: \`${finding.fixedVersion}\`\nIdentifiers:\n${identifiers}\n ___________`;
  });

  const descriptionString = description.join('\n\n');
  const escapedDescription = descriptionString.replace(/"/g, '\\"').replace(/\n/g, '\\n');

  return `
    mutation {
      issueCreate(input: {
        title: "Package Vulnerabilities in ${alerts.repoName}",
        description: "${escapedDescription}",
        teamId: "${TEAM_ID}",
        labelIds: ["${SECURITY_LABEL_ID}", "${PACKAGE_VULNERABILITY_LABEL_ID}"]
      }) {
        success
        issue {
          id
        }
      }
    }
  `;
}
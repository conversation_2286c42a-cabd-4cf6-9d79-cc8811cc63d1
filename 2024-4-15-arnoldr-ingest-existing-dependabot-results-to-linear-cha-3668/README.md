
This script ingest all the existing vulnerability reports of all repos from dependabot to linear 

How to run the script:
```
    1. run `npm install`
    2. In your .env file, within this folder, set value for SECURITY_LABEL_ID , TEAM_ID ,PACKAGE_VULNERABILITY_LABEL_ID,
    LINEAR_API_KEY and GITHUB_TOKEN
    3. run `ts-node 2024-4-15-arnoldr-ingest-existing-dependabot-results-to-linear-cha-3668/index.ts`
```
# Purpose

Find unlinked disputes (`{orderName: {$exists: false}}`) and re-send them as SQS messages for further processing by Shopify Order Linking Function.

## Motivation

Problem:

- The order linking function's calls to Shopify API was throttled likely because of the ongoing disputes ingestion process in parallel
- DLQ is empty due to the error being incorrectly suppressed in lambda function so it's not possible to redrive messages from the DLQ

This script finds these "unlinked disputes" in the database and resends them as SQS messages.

## Running this script

`npx ts-node ./script.ts <dry | wet-primary | wet-secondary>`

### Modes

dry - does not update anything, writes the results to the console
wet-secondary - does not update the source table, writes results to another collection (disputes_test), adding documents as needed
wet-primary - UPDATES THE DISPUTES TABLE. Use with extreme caution!

### How this script was tested

[x] Run locally with prod database in dry-run mode

## Required env variables

MONGO_URI=<mongo_uri_string>
QUEUE_URL=<sqs url of shopify-OrderLinkingQueue>

import dotenv from 'dotenv';
import { ScriptBase } from '../shared/script-base';
import { loadMongoClient } from '../shared/mongo-config';
import { ObjectId } from 'mongodb';
import { MessageSystemAttributeName, SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';

dotenv.config();

export class LinkShopifyOrders extends ScriptBase {
  public getName(): string {
    return 'Re-send unlinked disputes to SQS for Order Linking Function';
  }

  public getAuthor(): string {
    return 'Roksolana Dinter';
  }

  public getTicket(): string {
    return 'CHA-14538-link-shopify-orders-for-merchant-spanx-com';
  }

  async dryRun(): Promise<void> {
    await this.logic();
  }

  async wetRunOnPrimary(): Promise<void> {
    await this.logic();
  }

  async wetRunOnSecondary(): Promise<void> {
    throw new Error('Method not implemented.');
  }

  isEnvVarsOkOrThrow(keys: string[]) {
    const requiredEnvVars = keys;
    if (!requiredEnvVars.every((envVar) => process.env[envVar])) {
      throw new Error(`Missing required environment variables: ${requiredEnvVars.join(', ')}`);
    }
  }

  async logic() {
    const client = await loadMongoClient();

    this.isEnvVarsOkOrThrow(['MONGO_URI', 'QUEUE_URL']);

    const CHARGEFLOW_ID = '680643f2070229f0d7cfe9b7';
    console.log('SPANX-COM CHARGEFLOW_ID', CHARGEFLOW_ID);

    const DISPUTES = client.db('chargeflow_api').collection('disputes');

    const unlinkedDisputes = await DISPUTES.find({ chargeflowId: new ObjectId(CHARGEFLOW_ID), orderName: { $exists: false } }).toArray();

    if (!unlinkedDisputes || unlinkedDisputes.length === 0) {
      console.log('No unlinked disputes found');
      return;
    }

    console.log('Unlinked disputes count:', unlinkedDisputes.length);

    const validUnlinkedDisputes = unlinkedDisputes.filter((dispute) => dispute.transaction && dispute.transaction.orderId);

    console.log('Valid unlinked disputes: ', validUnlinkedDisputes.length);

    if (this.mode === 'dry') {
      console.log('Dry run completed.');
      return;
    }

    const SQS = new SQSClient();

    const promises = unlinkedDisputes.map((dispute) => {
      const body = [dispute._id.toString()];
      const attributes = {
        shopName: {
          DataType: 'String',
          StringValue: dispute.shopName
        },
      };

      const params = {
        MessageBody: JSON.stringify(body),
        MessageAttributes: attributes,
        QueueUrl: process.env.QUEUE_URL,
      };

      const command = new SendMessageCommand(params);
      return SQS.send(command);
    });

    const response = await Promise.allSettled(promises);

    const success = response.filter((r) => r.status === 'fulfilled').length;
    const failed = response.filter((r) => r.status === 'rejected').length;

    console.log(`Sent ${success} disputes to the queue`);
    console.log(`Failed to send ${failed} disputes to the queue`);
  }
}

const script = new LinkShopifyOrders();
script.main();

# Purpose
This script should delete old disputes before we import them.

https://linear.app/chargeflow/issue/CHA-3278/re-import-scentbird-disputes

## Motivation
We have imported some disputes using webhooks with wrong mapping, and missing some data.
We want to reimport them using scraper.

## Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console
wet-secondary - does not update the source table, writes results to another collection (disputes_test), adding documents as needed
wet-primary - UPDATES THE DISPUTES TABLE. Use with extreme caution!

### How this script was tested
[x] Run locally with development database

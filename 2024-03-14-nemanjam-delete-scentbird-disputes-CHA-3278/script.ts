import {ScriptBase, ScriptContext} from "../shared/script-base";
import {ObjectId} from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import fs from 'fs';

function storeDisputesInFile(disputes: any[], folder: string) {
    createFolder(folder)

    disputes.forEach(item => {
        const fileContent = JSON.stringify(item, null, 4);
        const fileName = `${item._id}.json`;
        fs.writeFileSync(folder + '/' + fileName, fileContent);
    });
}

function createFolder(folderName: string, subFolderName: string | null = null) {
    if (!fs.existsSync(folderName)) {
        fs.mkdirSync(folderName, { recursive: true });
        if (subFolderName) {
            fs.mkdirSync( folderName + '/' + subFolderName);
        }
    } else if (subFolderName) {
        fs.mkdirSync( folderName + '/' + subFolderName);
    }
}

const filter: any = {
    "dispute.processor": "adyen",
    "shopId": new ObjectId('65a8ff1b8e89d77a9e228a29') //ScentBird shopID (or any other)
}
export class DeleteDisputes extends ScriptBase {
    public getName(): string {
       return 'Remove scenbird disputes'
    }
    public getAuthor(): string {
       return 'Nemanja Malocic';
    }
    public getTicket(): string {
        return 'CHA-3278'
    }
    async dryRun(context: ScriptContext): Promise<void> {
        const client = await loadMongoClient();
        const session = client.startSession();
        try {
            session.startTransaction();
            const results = await client
                .db("chargeflow_api")
                .collection("disputes")
                .find(filter).toArray();

            storeDisputesInFile(results, "Adyen backup");

            results.forEach( dispute => {
                console.log(JSON.stringify(dispute, null, 2));
            });

            // This is here because it is easier to see
            console.log(`Found ${results.length} disputes to be deleted`);

        } catch (err) {
            console.warn('Aborting transaction');
            await session.abortTransaction();
            console.error(err);
            throw err;
        } finally {
            await session.endSession();
        }
    }
    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error("Method not implemented.");
    }
    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        const client = await loadMongoClient();
        const session = client.startSession();
        try {
            session.startTransaction();
            const disputes = await client
                .db("chargeflow_api")
                .collection("disputes")
                .find(filter).toArray();

            storeDisputesInFile(disputes, "Adyen backup");

            const results = await client
                .db("chargeflow_api")
                .collection("disputes")
                .deleteMany(filter);



            console.log(`deleted ${results.deletedCount} records, successful = ${results.acknowledged}`);
        } catch (err) {
            console.warn('Aborting transaction');
            await session.abortTransaction();
            console.error(err);
            throw err;
        } finally {
            await session.endSession();
        }
    }
}

const script = new DeleteDisputes();
script.main();

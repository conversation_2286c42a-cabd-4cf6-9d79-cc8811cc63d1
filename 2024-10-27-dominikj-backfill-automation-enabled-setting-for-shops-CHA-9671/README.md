# Purpose:

Update `settings` collection with automation toggles based on note analysis

Backfill automation toggle setting where missing

# Motivation:

Propagate shop notes to automation toggle. Also fill where empty due to historical reasons for consistency.

# Running this script

`npx tsx ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**

### How this script was tested
[x] Dry run against dev
import dotenv from "dotenv";
import { AnyBulkWriteOperation, Document, ObjectId, WithId } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { IMongoSettings } from "@chargeflow-team/data-access/dist/Settings/ISettings";
import neatCsv from "neat-csv";
import fs from "fs";

type AutomationToggleCsvItem = {
  chargeflowId: string;
  "Automation toggle": string;
};

type AutomationToggleParsedItem = {
  chargeflowId: string;
  automationToggle: boolean;
};

dotenv.config();

export class ScriptMigrateVariableKeyMappings extends ScriptBase {
  public getName(): string {
    return "Import Shop Automation Toggle";
  }

  public getAuthor(): string {
    return "Dominik Jancik";
  }

  public getTicket(): string {
    return "CHA-9671";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    let updatedCount = 0;
    const client = await loadMongoClient();
    const collection = client
      .db("chargeflow_api")
      .collection<IMongoSettings>("settings");

    const automationTogglesCsv = fs.readFileSync("automation-toggles.csv");
    const automationTogglesData = await neatCsv<AutomationToggleCsvItem>(
      automationTogglesCsv
    );
    const parsedAutomatonTogglesData = automationTogglesData.map(
      (row): AutomationToggleParsedItem => ({
        chargeflowId: row.chargeflowId,
        automationToggle: row["Automation toggle"] === "On",
      })
    );

    const handleDryRun = (toggleData: AutomationToggleParsedItem) => {
      console.log(
        `Would update setting ${toggleData.chargeflowId} with automation toggle: ${toggleData.automationToggle}`
      );
    };

    const createBulkOperation = (
      toggleData: AutomationToggleParsedItem
    ): AnyBulkWriteOperation<IMongoSettings> => {
      console.log(
        `Will update setting ${toggleData.chargeflowId} with automation toggle: ${toggleData.automationToggle}`
      );
      return {
        updateMany: {
          filter: {
            chargeflow_id: new ObjectId(toggleData.chargeflowId),
          },
          update: {
            $set: {
              "fields.auto_submit": toggleData.automationToggle,
            },
          },
        },
      };
    };

    let bulkOps: AnyBulkWriteOperation<IMongoSettings>[] = [];
    const backfillOp = {
      updateMany: {
        filter: {
          "fields.auto_submit": { $exists: false },
        },
        update: {
          $set: {
            "fields.auto_submit": true,
          },
        },
      },
    };

    if (!isDryRun) {
      bulkOps = parsedAutomatonTogglesData
        .map(createBulkOperation)
        .filter((op) => op !== null) as AnyBulkWriteOperation<IMongoSettings>[];
    } else {
      parsedAutomatonTogglesData.forEach(handleDryRun);

      const docsWithoutAutoSubmit = await collection
        .find(
          { "fields.auto_submit": { $exists: false } },
          { projection: { chargeflow_id: 1 } }
        )
        .toArray();

      docsWithoutAutoSubmit.forEach((doc: WithId<IMongoSettings>) => {
        if (
          parsedAutomatonTogglesData.find(
            ({ chargeflowId }) => chargeflowId === doc.chargeflow_id?.toString()
          )
        ) {
          return;
        }
        console.log(
          `Would update setting ${doc.chargeflow_id} with automation toggle: true`
        );
      });
    }

    if (!isDryRun && bulkOps.length > 0) {
      console.log("Updating explicit settings");
      const result = await collection.bulkWrite(bulkOps);
      console.log(
        `${new Date().toISOString()}: Processed ${
          parsedAutomatonTogglesData.length
        } documents. Total updated: ${result.modifiedCount}`
      );

      console.log("Backfilling missing settings");
      const backfillResult = await collection.bulkWrite([backfillOp]);

      console.log(
        `${new Date().toISOString()}: Processed ${
          parsedAutomatonTogglesData.length
        } documents. Total updated: ${backfillResult.modifiedCount}`
      );
    }
  }
}

const script = new ScriptMigrateVariableKeyMappings();
script.main();

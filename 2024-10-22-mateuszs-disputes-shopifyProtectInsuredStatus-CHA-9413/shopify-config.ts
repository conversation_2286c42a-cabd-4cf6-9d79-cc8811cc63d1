import '@shopify/shopify-api/adapters/node';
import {RestClientParams} from '@shopify/shopify-api/lib/clients/rest/types';
import { Logger, ErrorUtil, RetryUtil } from '@chargeflow-team/chargeflow-utils-sdk';
import { shopifyApi, ApiVersion } from '@shopify/shopify-api';
import {GraphqlClient} from '@shopify/shopify-api/lib/clients/graphql/graphql_client';

const retryableErrors = [
    'Internal Server Error',
    'Origin Error',
    'HttpRequestError',
];

export const getShopifyGraphQlClient = async (shopName: string, accessToken: string, scopes: any[]): Promise<GraphqlClient | undefined> => {
    if (!shopName) {
        ErrorUtil.throwError('NotFoundError', 'Missing shopName');
    }
    if (!accessToken) {
        ErrorUtil.throwError('NotFoundError', `Missing accessToken for shop: ${shopName}`);
    }

    const session = { shop: `${shopName}.myshopify.com`, accessToken };
    const shopifyVersion  = process.env.SHOPIFY_CLIENT_API_VERSION as keyof typeof ApiVersion;
    try {
        const shopify = shopifyApi({
            apiKey: process.env.SHOPIFY_CLIENT_API_ID,
            apiSecretKey: process.env.SHOPIFY_CLIENT_API_KEY as string,
            scopes: scopes || process.env.SHOPIFY_SCOPE,
            hostName: process.env.HOST_NAME as string,
            apiVersion: ApiVersion[shopifyVersion],
            isEmbeddedApp: false,
        });
        return new shopify.clients.Graphql({ session } as RestClientParams);
    } catch (error: any) {
        if (error.constructor.name === 'HttpThrottlingError') {
            return await RetryUtil.retry(getShopifyGraphQlClient,
                shopName, accessToken, scopes);
        } else if (
            retryableErrors.includes(error.constructor.name) ||
            retryableErrors.includes(error.statusText)) {
            return await RetryUtil.retry(getShopifyGraphQlClient,
                shopName, accessToken, scopes);
        }
        Logger.error('BadGatewayError',
            `Error creating Shopify client for shop: ${shopName}, error: ${JSON.stringify(error)}`);
    }

    return;
}

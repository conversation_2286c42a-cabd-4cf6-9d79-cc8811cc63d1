import {DisputeStatusUpdateObject} from './types';
import {ObjectId} from 'mongodb';


export const buildDBGetShopsAndDisputesQuery = () => {
    return [
        {
            $match: {
                'dispute.processor': 'shopify',
                'order.id': { '$exists': true },
                'lastDisputeStatus.status': { '$ne': 'insured' },
            }
        },
        {
            // Join with the shops collection based on the shopId field
            $lookup: {
                from: 'shops',
                localField: 'shopId',
                foreignField: '_id',
                as: 'shopData'
            }
        },
        {
            // Unwind the shopData array to flatten the result
            $unwind: {
                path: '$shopData',
                preserveNullAndEmptyArrays: true  // If no match is found, preserve the dispute
            }
        },
        {
            // Group by shopId and accumulate all disputes related to each shopId
            $group: {
                _id: '$shopId',
                shopName: { $first: '$shopData.name' },
                accessToken: { $first: '$shopData.connect.access_token' },
                scope: { $first: '$shopData.connect.scope' },
                disputes: {
                    $push: {
                        orderId: '$order.id',
                        disputeId: '$_id',
                        chargeflowId: '$chargeflowId',
                        lastDisputeStatus: '$lastDisputeStatus.status',
                    }
                }
            }
        }
    ];
};

export const buildDBUpdateQuery = (orderId: number, updatedDisputeStatus?: DisputeStatusUpdateObject, chargeflowId?: ObjectId) => {
    return {
        updateOne: {
            filter: {
                "order.id": orderId,
                "chargeflowId": new ObjectId(chargeflowId),
            },
            update: {
                $set: {
                    "lastDisputeStatus" : updatedDisputeStatus,
                    "chargeflow.cfActions.handleByChargeflow": false,
                },
                $push: {
                    "dispute.disputeStatuses" : updatedDisputeStatus,
                },
            },
            upsert: false
        }
    };
}

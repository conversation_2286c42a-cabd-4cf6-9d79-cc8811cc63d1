import { DisputeStatusUpdateObject, ShopifyOrder } from './types';

export const SHOPIFY_ORDERS_MAX_BATCH_SIZE = 250;

export const buildOrdersShopifyProtectQuery = (orderIds: number[]) => {
    return `query {
    nodes(ids: [${orderIds.map(id => `"gid://shopify/Order/${id}"`).join(', ')}]) {
      ... on Order {
        legacyResourceId
        shopifyProtect {
          status
        }
      }
    }
  }`;
}

export function getShopifyProtect(orderId: number, ordersWithShopifyProtect?: ShopifyOrder[]) {
    if (!ordersWithShopifyProtect?.length || !orderId) {
        return;
    }
    const orderWithShopifyProtect = ordersWithShopifyProtect.find(order => Number(order?.orderId) === Number(orderId));
    if (!orderWithShopifyProtect) {
        console.warn(`No order with shopify protect found for orderId: ${orderId}`);
        return;
    }
    return orderWithShopifyProtect.shopifyProtect?.status;
}


export function mapShopifyProtectStatusToCfDisputeStatus(shopifyProtectStatus: string, previousStatus: string): DisputeStatusUpdateObject | null {
    switch (shopifyProtectStatus) {
        case 'ACTIVE':
        case 'PROTECTED':
            return {
                dateCreated: new Date(),
                statusDate : new Date(),
                status: 'insured',
                processorStatus : previousStatus || null,
                source: 'fetch',
            };
        default:
            return null;
    }
}


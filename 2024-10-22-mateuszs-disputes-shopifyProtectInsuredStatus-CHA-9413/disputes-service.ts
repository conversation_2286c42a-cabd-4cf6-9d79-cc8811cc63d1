import {ShopifyOrder, ShopWithDisputes} from './types';
import {getShopifyProtect, mapShopifyProtectStatusToCfDisputeStatus} from './shopify.utils';
import {AnyBulkWriteOperation, ObjectId} from 'mongodb';
import {updateManyDisputes} from './disputes-repository';
import {buildDBUpdateQuery} from './db.utils';

export const updateDisputesStatus = (shops: ShopWithDisputes[], shopifyStatusesByShop: ShopifyOrder[][], isDryRun: boolean) => shops.map(async (shop) => {
    const {disputes, shopName} = shop;
    const orders = shopifyStatusesByShop.find(shopifyShop => shopifyShop?.[0]?.shopName === shop.shopName);
    const bulkWrites = disputes
        .map(dispute => {
            const { orderId, chargeflowId, lastDisputeStatus } = dispute;
            const shopifyProtectStatus = getShopifyProtect(orderId, orders);
            if (!shopifyProtectStatus) {
                return;
            }

            const cfStatus = mapShopifyProtectStatusToCfDisputeStatus(shopifyProtectStatus, lastDisputeStatus);
            if (!cfStatus) {
                return;
            }
            console.log(`${shopName}: order to update: `, orderId, chargeflowId?.toString(), shopifyProtectStatus, cfStatus?.status, cfStatus?.dateCreated, `, previous status: ${cfStatus?.processorStatus}`);

            return buildDBUpdateQuery(orderId, cfStatus, new ObjectId(chargeflowId));
        })
        .filter(Boolean) as AnyBulkWriteOperation<any>[];

    if (bulkWrites.length) {
        console.log(`Number of disputes in prepared bulk write for shop: ${shopName}: `, bulkWrites?.length);
    }

    try {
        if (!isDryRun) {
            return await updateManyDisputes(bulkWrites);
        }
    } catch (error) {
        console.error(`BulkWrites error for : ${shopName}`, error);
    }

});

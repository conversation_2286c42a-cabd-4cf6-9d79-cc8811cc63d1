# Purpose
This script updates statuses of shopify disputes to "insured" if their order is "shopify-protected".
https://linear.app/chargeflow/issue/CHA-9413/migration-script

## Motivation
Updates statuses of shopify disputes based on "shopify-protected" status.  

## Running this script
`npx ts-node ./update-dispute-shopify-protect-insured-status.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console
wet-secondary - does not update the source table, writes results to another collection (disputes_test), adding documents as needed
wet-primary - UPDATES THE DISPUTES TABLE. Use with extreme caution!

### How this script was tested
[x] on dev

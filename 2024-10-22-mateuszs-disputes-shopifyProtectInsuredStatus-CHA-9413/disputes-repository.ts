import {loadMongoClient} from '../shared/mongo-config';
import {AnyBulkWriteOperation, MongoClient} from 'mongodb';
import {ShopWithDisputes} from './types';
import {buildDBGetShopsAndDisputesQuery} from './db.utils';

export const updateManyDisputes = async (bulkOperations: AnyBulkWriteOperation<any>[] = []) => {
    if (bulkOperations.length > 0) {
        console.log(`bulkOperations?.length: `, bulkOperations?.length);

        const client = await loadMongoClient();
        const result = await client
            .db('chargeflow_api')
            .collection('disputes')
            .bulkWrite(bulkOperations);

        console.log(`Updated ${result.modifiedCount} disputes with new statuses.`);
        console.log(`Updated ${result.matchedCount} disputes with new statuses.`);
        console.log(JSON.stringify(result, null, 2));
    } else {
        console.log('No bulk writes to run.', bulkOperations);
    }
}

export const getShops = (client: MongoClient) => async (): Promise<ShopWithDisputes[]> => {
    try {
        const database = client.db('chargeflow_api');
        const disputesCollection = database.collection('disputes');

        const query = buildDBGetShopsAndDisputesQuery();

        console.log(`Checks for Shopify orderIds and group by shopId...`);
        const groupedResults = await disputesCollection.aggregate(query).toArray();

        console.log(`Found Shopify disputes in number of shops: ${groupedResults.length}`);

        return groupedResults as ShopWithDisputes[];
    } catch (error) {
        console.error(`Error while fetching Shopify disputes: `, error);
        return [];
    }
};

import {ObjectId} from 'mongodb';

export type ShopWithDisputes = {
    _id: ObjectId;
    shopName: string;
    accessToken: string;
    scope: string;
    disputes: {
        orderId: number;
        disputeId: string;
        chargeflowId: ObjectId;
        lastDisputeStatus: string;
    }[];
};

export type DisputeStatusUpdateObject = {
    dateCreated: Date;
    statusDate : Date;
    status: string;
    processorStatus: string | null;
    source: string;
}

export type ShopifyProtect = {
    status?: string;
}
export type ShopifyOrder = {
    shopName: string;
    orderId: number;
    shopifyProtect?: ShopifyProtect;
};

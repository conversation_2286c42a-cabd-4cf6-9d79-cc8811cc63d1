import { ShopifyOrder, ShopWithDisputes} from './types';
import {getShopifyGraphQlClient} from './shopify-config';
import {GraphqlClient} from '@shopify/shopify-api/lib/clients/graphql/graphql_client';
import {logMappedShopifyOrder, splitIntoBatches} from './utils';
import {buildOrdersShopifyProtectQuery, SHOPIFY_ORDERS_MAX_BATCH_SIZE} from './shopify.utils';


export const getShopifyStatuses = async (shops: ShopWithDisputes[]): Promise<ShopifyOrder[][]> => {
    const promises = shops.map(async ({ _id, shopName, disputes, accessToken, scope }) => {
        const orderIds = disputes.map(dispute => dispute.orderId);
        const scopes = scope?.split(',');

        console.log(`${shopName}:${_id}: fetches orders status from Shopify GraphQL API, for number of orders: ${orderIds?.length}`);

        if (accessToken) {
            const shopifyQl = await getShopifyGraphQlClient(shopName, accessToken, scopes) as GraphqlClient;

            const orderIdsBatches = splitIntoBatches(orderIds, SHOPIFY_ORDERS_MAX_BATCH_SIZE);

            console.log(`${shopName}: Get shopify status: Number of batches: ${orderIdsBatches?.length}`);

            const batchPromises = orderIdsBatches.map(async (idsBatch) => {
                const queryQL = buildOrdersShopifyProtectQuery(idsBatch);

                try {
                    const shopifyResponse: any = await shopifyQl.query({ data: queryQL });
                    const mappedOrders = shopifyResponse?.body?.data?.nodes?.map((node: any) => {
                        if (!node) {
                            console.warn('No node found');
                            return null;
                        }
                        const shopifyOrder: ShopifyOrder = {
                            shopName,
                            orderId: node.legacyResourceId,
                            shopifyProtect: node.shopifyProtect,
                        };
                        return shopifyOrder;
                    }).filter(Boolean);

                    if (mappedOrders?.length) {
                        console.log(`${shopName}: mapped orders to shopify status: `, mappedOrders.map(logMappedShopifyOrder));
                        return mappedOrders;
                    }

                    return;
                } catch (error) {
                    console.error(`${shopName}: shopifyQl error: `, error);
                    return;
                }
            });

            const shopResults = await Promise.all(batchPromises.filter(Boolean));

            return shopResults.flat();
        }
        return;
    });

    const allResults = await Promise.all(promises.filter(Boolean));

    return allResults.filter(Boolean) as ShopifyOrder[][];
}



import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase } from '../shared/script-base';
import {getShopifyStatuses} from './shopify-service';
import {getShops} from './disputes-repository';
import {updateDisputesStatus} from './disputes-service';


class Script extends ScriptBase {
  public getName(): string {
    return 'shopify-protect-insured-status';
  }
  public getAuthor(): string {
    return ''
  }
  public getTicket(): string {
    return 'CHA-9413';
  }

  async dryRun() {
    await this.logic(true);
  }
  private async doWetRun() {
    await this.logic(false);
  }

  private async logic(isDryRun: boolean) {
    console.log('Running in dry run (not changing anything)');

    const client = await loadMongoClient();
    const shops = await getShops(client)();
    const shopifyStatuses = await getShopifyStatuses(shops);
    const updatePromises = updateDisputesStatus(shops, shopifyStatuses, isDryRun);

    if (isDryRun) {
      console.log('-'.repeat(50));
      console.log('DRY RUN - aborting.');
      console.log('-'.repeat(50));
    } else {
      const performedUpdates = await Promise.allSettled(updatePromises);

      console.log('Updated disputes statuses in number of shops: ', performedUpdates?.length);
    }

    await client.close();
    console.log('Client closed.');
  }

  async wetRunOnSecondary() {
    return Promise.resolve(undefined);
  }
  async wetRunOnPrimary() {
    return await this.doWetRun()
  }
}

const script = new Script();
script.main();

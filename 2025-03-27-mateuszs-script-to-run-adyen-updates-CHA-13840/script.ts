import {loadMongoClient} from "../shared/mongo-config";
import {ScriptBase} from "../shared/script-base";
import {ClientSession, MongoClient, ObjectId, WithId} from 'mongodb';
import {
    IDisputeStatus,
    IMongoEvidence,
    IMongoUnifiedDisputeObject
} from "@chargeflow-team/data-access/dist/Dispute/Types";
import fs from "node:fs";
import path from "path";
import _ from "lodash";
import {DataAccessUnitOfWork} from "@chargeflow-team/data-access";
import {ChargeFlowDisputeStatus, DisputeStatusSource} from "@chargeflow-team/data-access/dist/Dispute/enums";
import {SQSClient, SendMessageCommand, SendMessageCommandOutput} from "@aws-sdk/client-sqs";
import {randomUUID} from "crypto";
import {IMongoAction} from "@chargeflow-team/data-access/dist/Action/Types";


export class UpdateAdyenDisputes extends ScriptBase {

    public getName(): string {
        return "Update adyen disputes which were affected by timeouts and errors";
    }

    public getAuthor(): string {
        return "Mateusz Smagiel";
    }

    public getTicket(): string {
        return "cha-13840-prepare-and-run-adyen-updates";
    }

    async dryRun(): Promise<void> {
        await this.logic(true);
    }

    async wetRunOnPrimary(): Promise<void> {
        try {
            await this.logic(false);
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    async wetRunOnSecondary(): Promise<void> {
        throw new Error("Method is not implemented.");
    }

    async logic(dryRun: boolean) {
        const client = await loadMongoClient();
        const sqsClient = new SQSClient();
        const dataAccess = new DataAccessUnitOfWork(client);
        const affectedAdyenDisputes = this.readData();
        const disputeIds = affectedAdyenDisputes.map(dispute => new ObjectId(dispute.disputeId));
        const disputesRepository = client!.db("chargeflow_api")
            .collection<IMongoUnifiedDisputeObject>("disputes");
        const disputes = await disputesRepository.find({
            $and: [
                {_id: {$in: disputeIds}},
            ]
        }).toArray();
        console.log(`Found ${disputes.length} disputes`);
        const [disputesUpdatedByWebhook, disputesNotUpdatedByWebhook] = _.partition(disputes, d => {
            const affectedAdyenDispute = affectedAdyenDisputes.find(ad => ad.disputeId === d._id.toString());
            if (!affectedAdyenDispute) {
                console.error(`Dispute ${d._id} not found in the affectedAdyenDisputes`);
                return false;
            }
            console.log(affectedAdyenDispute!.timestamp);
            const affectedAdyenDisputeDate = new Date(affectedAdyenDispute!.timestamp);
            console.log(`Dispute ${d._id} lastDisputeStatus dateCreated: ${d.lastDisputeStatus?.dateCreated}, affectedAdyenDisputeDate: ${affectedAdyenDisputeDate}`);
            return d.lastDisputeStatus?.dateCreated && d.lastDisputeStatus.dateCreated >= affectedAdyenDisputeDate;
        })
        console.log(`Found ${disputesNotUpdatedByWebhook.length} disputes not updated by webhook`);
        console.log(`Found ${disputesUpdatedByWebhook.length} disputes updated by webhook`);

        const updatedByWebhook = _.mapValues(_.groupBy(disputesUpdatedByWebhook, d => d.lastDisputeStatus?.status), group =>
            group.map(d => d._id)
        );
        const notUpdatedByWebhook = _.mapValues(_.groupBy(disputesNotUpdatedByWebhook, d => d.lastDisputeStatus?.status), group =>
            group.map(d => d._id)
        );
        console.log("Grouped updated by webhook:---------------------------------------------------------");
        console.log(updatedByWebhook);
        console.log("Grouped not updated by other:------------------------------------------------------------");
        console.log(notUpdatedByWebhook);

        for (const dispute of disputes) {
            const affectedAdyenDispute = affectedAdyenDisputes.find(ad => ad.disputeId === dispute._id.toString());
            if (!affectedAdyenDispute) {
                console.error(`Dispute ${dispute._id} not found in the affectedAdyenDisputes`);
                continue;
            }
            await this.saveDisputeDefendedData(client, dataAccess, sqsClient, dispute, affectedAdyenDispute, dryRun);
        }
    }

    private async saveDisputeDefendedData(
        client: MongoClient,
        dataAccess: DataAccessUnitOfWork,
        sqsClient: SQSClient,
        dispute: WithId<IMongoUnifiedDisputeObject>,
        affectedDispute: AffectedAdyenDisputes,
        dryRun: boolean
    ): Promise<any> {
        const affectedDisputeTimestamp = new Date(affectedDispute.timestamp);
        const newStatus = {
            dateCreated: affectedDisputeTimestamp,
            statusDate: affectedDisputeTimestamp,
            status: ChargeFlowDisputeStatus.UnderReview,
            processorStatus: 'information_supplied',
            source: DisputeStatusSource.ReFetch,
        };
        console.log('saving dispute defended data for dispute %s', dispute._id);
        const session = dataAccess.startTransactionV2();
        try {
            const evidenceId = affectedDispute.evidenceId;
            const evidence = dispute.chargeflow.evidences.find(ev => ev.evidenceId.toString() === evidenceId);
            console.log(`Evidence for ${evidenceId}: ${JSON.stringify(evidence)}`);
            await this.updateEvidence(dataAccess, dispute, evidence, affectedDisputeTimestamp, session, dryRun);
            await this.updatePerformedAction(dispute, dataAccess, session, dryRun);
            await this.updateHistoricalData(dispute, affectedDisputeTimestamp, dataAccess, newStatus, session, dryRun);
            const actions = await client.db("chargeflow_api")
                .collection<IMongoAction>('actions')
                .find({disputeId: dispute._id})
                .toArray();
            console.log(`Found ${actions.length} actions for dispute ${dispute._id}`);
            await this.addAction(actions, dispute, dataAccess, affectedDisputeTimestamp, session, dryRun);
            console.log('committing transaction');
            await session?.commitTransaction();
            console.log('successfully saved dispute defended data for dispute %s', dispute._id);
        } catch (error) {
            console.error(`failed to save dispute defended data for dispute ${dispute._id}. Error message ${JSON.stringify(error)}`);
            await session?.abortTransaction();
            throw error;
        } finally {
            await session?.endSession();
        }
        await this.sendtoChargescoreQueue(dispute, sqsClient, dryRun);
    }

    private async sendtoChargescoreQueue(dispute: WithId<IMongoUnifiedDisputeObject>,
                                              sqsClient: SQSClient,
                                              dryRun: boolean) {
        if (dryRun) {
            console.log('dry run, skipping sending dispute to chargescore calculation queue, dispute %s', dispute._id);
            return;
        }
        await this.sendToChargescoreQueue(sqsClient, dispute._id!.toString());
    }

    private async addAction(actions: WithId<IMongoAction>[],
                            dispute: WithId<IMongoUnifiedDisputeObject>,
                            dataAccess: DataAccessUnitOfWork,
                            affectedDisputeTimestamp: Date,
                            session: ClientSession,
                            dryRun: boolean) {
        if (actions.find(a => a.actionType === 'evidence_submitted')) {
            console.log('skipping adding action for dispute %s', dispute._id);
            return;
        }
        if (dryRun) {
            console.log('dry run, skipping adding action for dispute %s', dispute._id);
            return;
        }
        console.log('adding action for dispute %s', dispute._id);
        await dataAccess.action.add({
            dateCreated: affectedDisputeTimestamp,
            chargeflowId: dispute.chargeflowId.toString(),
            disputeId: dispute._id!.toString(),
            caseId: dispute.dispute.id,
            shopName: dispute.shopName ?? '',
            processor: dispute.dispute.processor,
            actor: 'system',
            actorRole: 'expert',
            actionType: 'evidence_submitted',
        }, session);
    }

    private async updateHistoricalData(dispute: WithId<IMongoUnifiedDisputeObject>,
                                       affectedDisputeTimestamp: Date,
                                       dataAccess: DataAccessUnitOfWork,
                                       newStatus: IDisputeStatus,
                                       session: ClientSession,
                                       dryRun: boolean) {
        if (!dispute.lastDisputeStatus.dateCreated || dispute.lastDisputeStatus.dateCreated >= affectedDisputeTimestamp) {
            console.log('skipping updating dispute %s with new status', dispute._id);
            return;
        }
        if (dryRun) {
            console.log('dry run, skipping update historical data for dispute %s', dispute._id);
            return;
        }
        console.log('updating dispute %s historical data', dispute._id);
        await dataAccess.disputeRepository.updateAndPushHistoricalData({
            _id: dispute._id!.toString(),
            lastDisputeStatus: newStatus,
        }, {
            'dispute.disputeStatuses': newStatus,
        }, session);
    }

    private async updatePerformedAction(dispute: WithId<IMongoUnifiedDisputeObject>,
                                        dataAccess: DataAccessUnitOfWork,
                                        session: ClientSession,
                                        dryRun: boolean) {
        if (dryRun) {
            console.log('dry run, skipping update performed action for dispute %s', dispute._id);
            return;
        }
        console.log('updating dispute %s performed action', dispute._id);
        await dataAccess.disputeRepository.updatePerformedAction(dispute._id!.toString(), true, session);
    }

    private async updateEvidence(dataAccess: DataAccessUnitOfWork,
                                 dispute: WithId<IMongoUnifiedDisputeObject>,
                                 evidence: IMongoEvidence | undefined,
                                 affectedDisputeTimestamp: Date,
                                 session: ClientSession,
                                 dryRun: boolean) {
        if (!evidence || evidence.sentDate) {
            console.log('skipping updating dispute evidence %s, dispute %s', evidence?.evidenceId, dispute._id);
            return;
        }
        if (dryRun) {
            console.log('dry run, skipping update evidence');
            return;
        }
        console.log('updating dispute evidence %s, dispute %s', evidence?.evidenceId, dispute._id);
        await dataAccess.disputeRepository.updateEvidence(dispute._id!.toString(), evidence.evidenceId.toString(), {
            attemptedSend: true,
            shouldSendEvidence: false,
            sent: true,
            sentDate: affectedDisputeTimestamp,
        }, session);
    }

    private async sendToChargescoreQueue(sqsClient: SQSClient, disputeId: string) {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }
        console.log(`sending dispute to ${process.env.AWS_SQS_CHARGESCORE_DISPUTE_ID_URL} queue`, disputeId);
        try {
            const queueUrl = process.env.AWS_SQS_CHARGESCORE_DISPUTE_ID_URL!;
            const params = {
                MessageAttributes: {
                    Key: {
                        DataType: 'String',
                        StringValue: randomUUID(),
                    },
                },
                MessageBody: JSON.stringify(disputeId),
                QueueUrl: queueUrl
            };
            const command = new SendMessageCommand(params);
            const response: SendMessageCommandOutput = await sqsClient.send(command);
            return response;
        } catch (err: any) {
            console.error(`Failed to send sqs message for dispute ${disputeId}: ${err}`);
            throw err;
        }
    }

    private readData(): AffectedAdyenDisputes[] {
        try {
            const filePath = path.join(__dirname, 'adyen-disputes.json');
            const data = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(data);
        } catch (err) {
            console.error('Error reading or parsing the file:', err);
            throw err;
        }
    }
}

interface AffectedAdyenDisputes {
    timestamp: number,
    disputeId: string,
    evidenceId: string
}

const script = new UpdateAdyenDisputes();
script.main();

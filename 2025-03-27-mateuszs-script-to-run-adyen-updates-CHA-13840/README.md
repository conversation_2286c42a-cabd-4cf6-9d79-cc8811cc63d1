# Purpose

This script updates adyen disputes for which we failed to update metadata after successful evidence submission.
https://linear.app/chargeflow/issue/CHA-13840/prepare-and-run-migration-script-for-adyen-disputes

## Motivation

We need to update this disputes metadata in order to be consistent.

## Running this script

`npx ts-node ./script.ts <dry | wet-primary>`

## Modes

- dry - only logs the total number of disputes that are eligible for resending
- wet-primary - sends disputes to the OrderLinkingQueue via SQS

## Required env variables

MONGO_URI=<mongo_uri_string>
AWS_SQS_CHARGESCORE_DISPUTE_ID_URL=<sqs queue url>

## Required files

- `adyen-disputes.json` - a json file containing the disputes that need to be updated
  
### Example 
  [{
  "disputeId": "67cc77f13b240eb150d25894",
  "evidenceId": "67dbeac7b0052ab378da9b3a",
  "timestamp": 1742465740809
  }]

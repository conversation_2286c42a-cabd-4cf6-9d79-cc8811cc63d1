# Purpose
This script should insert missing fields related to order-linking from dev database to prod database.

https://linear.app/chargeflow/issue/CHA-5090/create-script-to-link-orders-for-ingested-my-obvi-disputes

## Motivation
40 disputes that are in the production database are missing order-linking because improvement on order-linking was done after.
Dev database contains all correct data so we will just copy that to prod.

## Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD, env should have two values:
MONGO_URI - uri to dev database
PROD_MONGO_URI= - uri to prod database
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console
wet-secondary - does not update the source table, writes results to another collection (disputes_test), adding documents as needed
wet-primary - UPDATES THE DISPUTES TABLE. Use with extreme caution!

### How this script was tested
[x] Run locally with development database
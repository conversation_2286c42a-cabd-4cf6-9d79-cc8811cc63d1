import {ScriptBase, ScriptContext} from "../shared/script-base";
import { loadMongoClient, loadMongoClientProdAfterpay } from "../shared/mongo-config";
import fs from 'fs';
import { AnyBulkWriteOperation, Document } from "mongodb";


const filter: any = {
    "dispute.processor": "afterpay",
    "shopName":"my-obvi"
}

export class TransformDisputes extends ScriptBase {
    public getName(): string {
       return 'Link missing afterpay orders'
    }
    public getAuthor(): string {
       return '<PERSON><PERSON>';
    }
    public getTicket(): string {
        return 'CHA-5090'
    }

    private storeDisputesInFile(disputes: any[], folder: string) {
        this.createFolder(folder)

        disputes.forEach(item => {
            const fileContent = JSON.stringify(item, null, 4);
            const fileName = `${item._id}.json`;
            fs.writeFileSync(folder + '/' + fileName, fileContent);
        });
    }

    private createFolder(folderName: string, subFolderName: string | null = null) {
        if (!fs.existsSync(folderName)) {
            fs.mkdirSync(folderName, { recursive: true });
            if (subFolderName) {
                fs.mkdirSync( folderName + '/' + subFolderName);
            }
        } else if (subFolderName) {
            fs.mkdirSync( folderName + '/' + subFolderName);
        }
    }

    async dryRun(context: ScriptContext): Promise<void> {
        const client = await loadMongoClientProdAfterpay();
        const session = client.startSession();
        try {
            session.startTransaction();
            const results = await client
                .db("chargeflow_api")
                .collection("disputes")
                .find(filter).toArray();

            this.storeDisputesInFile(results, "Afterpay backup");

            results.forEach( dispute => {
                console.log(JSON.stringify(dispute._id, null, 2));
            });

            // This is here because it is easier to see
            console.log(`Found ${results.length} disputes.`);

        } catch (err) {
            console.warn('Aborting transaction');
            await session.abortTransaction();
            console.error(err);
            throw err;
        } finally {
            await session.endSession();
        }
    }
    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error("Method not implemented.");
    }
    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        const clientDev = await loadMongoClient();
        const clientProd = await loadMongoClientProdAfterpay();
        const session = clientDev.startSession();
        try {
            session.startTransaction();
            const devDisputes = await clientDev
                .db("chargeflow_api")
                .collection("disputes")
                .find(filter).toArray();

            this.storeDisputesInFile(devDisputes, "Afterpay backup");

            const prodDisputes = await clientProd
            .db("chargeflow_api")
            .collection("disputes")
            .find(filter).toArray();

            this.storeDisputesInFile(prodDisputes, "Afterpay prod backup");

            const disputesToTransform: any[] = [];
            const updateActions: AnyBulkWriteOperation<Document>[] = devDisputes.map( devDispute => {
                const prodDispute = prodDisputes.find(prodDispute => prodDispute.dispute.id === devDispute.dispute.id);
                if (prodDispute && !prodDispute.order && devDispute.order) {
                    disputesToTransform.push(prodDispute.dispute.id);
                    return {
                        updateOne: {
                            filter: { _id: prodDispute._id },
                            update: {
                                $set: {
                                    orderDate: devDispute.orderDate,
                                    order: devDispute.order,
                                    orderName: devDispute.orderName,
                                    "transaction.checks": devDispute.transaction.checks,
                                    "transaction.paymentDetails": devDispute.transaction.paymentDetails,
                                    "chargeflow.checks": devDispute.chargeflow.checks,                                 
                                }
                            }
                        }
                    }
                }
                return null;
            }).filter( action => action !== null) as AnyBulkWriteOperation<Document>[];

            console.log(`${disputesToTransform.length} disputes to transform: ${disputesToTransform}`);
            
            if (disputesToTransform.length === 0) {
                return;
            }

            const results = await clientProd
                .db("chargeflow_api")
                .collection("disputes")
                .bulkWrite(updateActions);

            console.log(`transformed ${results.modifiedCount} records, errors that occurred: ${results.getWriteErrors()}`);
            
        } catch (err) {
            console.warn('Aborting transaction');
            await session.abortTransaction();
            console.error(err);
            throw err;
        } finally {
            await session.endSession();
        }
    }
}

const script = new TransformDisputes();
script.main();
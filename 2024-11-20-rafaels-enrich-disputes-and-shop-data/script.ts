import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Collection, Db, ObjectId } from 'mongodb';

dotenv.config();

const DISABLED_STATUSES = [ 'disabled', 'uninstalled', 'inactive'];
const MANUALLY_ONBOARDED_SHOP_PLATFORM = 'sas';
const TRUE_VALUE = true;
const FALSE_VALUE = false;
const SHOPIFY_PROCESSOR = 'shopify';

const getBillingStatusByShopPlatform = (shopPlatform: string, activeRecurringAppChargeId: string, paymentMethodId: string, shopStatus: string, shopSource: string) => {
  if (shopPlatform === MANUALLY_ONBOARDED_SHOP_PLATFORM || shopSource) {
      return TRUE_VALUE;
  } else if (DISABLED_STATUSES.includes(shopStatus)) {
      return FALSE_VALUE;
  }  else if (shopStatus === null) {
      return !!activeRecurringAppChargeId || !!paymentMethodId;        
  } else if (shopPlatform === SHOPIFY_PROCESSOR) {
      return !!activeRecurringAppChargeId;        
  } else {
      return !!paymentMethodId;
  } 
};

export class EnrichDisputesWithBillingAndShopData extends ScriptBase {
  db: Db | undefined = undefined;

  public getName(): string {
    return "Create script to enrich disputes with billing and shop data";
  }

  public getAuthor(): string {
    return "Rafael Schuster";
  }

  public getTicket(): string {
    return "CHA-10340";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }
  private async logic(context: ScriptContext, isDryRun: boolean): Promise<void> {
    console.log('Enriching disputes with billing and shop data');
   
    const mongoClient = await loadMongoClient();
    const shopsMongoCollection = mongoClient.db("chargeflow_api").collection("shops");
    const disputesMongoCollection = mongoClient.db("chargeflow_api").collection("disputes");
    console.log('Successfully generated DB client');

    try {
        const shopsCompanyAndBilling: any[] = await this.findShopsCompanyAndBillingData(shopsMongoCollection);
        console.log('Processing data for %s shops', shopsCompanyAndBilling.length);
        if (!shopsCompanyAndBilling.length) {
            console.warn('No shops to reconcile data. Ennding process...');
            return;
        }
    
        const updateQueries = shopsCompanyAndBilling.map(this.createUpdateQuery);
        console.log('%s update queries generated', updateQueries.length);

        if (isDryRun) {
            console.log('Dry run- Would have processed %s queries. Ending process...', updateQueries.length);
            return;
        }

        console.log('Running bulk update queries');
        const res  = await this.updateManyDisputesInBulk(updateQueries, disputesMongoCollection);
        console.log('Bulk response: %j', res);
    } catch (error) {
      console.error('Error in main processing loop:', error);
      throw error;
    } finally {
      console.log('Cleaning up connections');
      await mongoClient.close();
    }
  }

   createUpdateQuery (shopCompanyAndBillingData: any) {
    const chargeflowId = shopCompanyAndBillingData.chargeflowId;
    const shopPlatform = shopCompanyAndBillingData.shopPlatform;
    const shopStatus = shopCompanyAndBillingData.shopStatus;
    const source = shopCompanyAndBillingData.shopSource;
    const activeRecurringAppChargeId = shopCompanyAndBillingData.activeRecurringAppChargeId[0];
    const paymentMethodId = shopCompanyAndBillingData.paymentMethodId[0];
    const isBillingActive = getBillingStatusByShopPlatform(
        shopPlatform, activeRecurringAppChargeId, paymentMethodId, shopStatus, source)

    const updateObj = {
        shopPlatform: shopPlatform,
        isBillingActive: isBillingActive,
    }

    return {
        updateMany: {
            filter: { chargeflowId: new ObjectId(chargeflowId) },
            update: { $set: updateObj } 
        },
      };
}

 async updateManyDisputesInBulk(operationsArray, disputesCollection: Collection) {
    if (!operationsArray.length) {
      return;
    }
    return disputesCollection
      .bulkWrite(operationsArray, { ordered: false })
      .catch(err => {
        console.log(err);
        throw err;
      });
  }

   async findShopsCompanyAndBillingData(shopsCollection: Collection) {

    const agg = [
        {
            $lookup: {
                from: 'billings',
                localField: 'chargeflow_id',
                foreignField: 'chargeflowId',
                as: 'billing',
                pipeline: [
                  {
                    $project: {
                      _id: 0,
                      activeRecurringAppChargeId: 1,
                    },
                  },
                ],
              },
        },
        {
            $lookup: {
                from: 'companies',
                localField: 'chargeflow_id',
                foreignField: 'users.chargeflowId',
                as: 'company',
                pipeline: [
                  {
                    $project: {
                      _id: 0,
                      'users.billing.stripeCustomer.paymentMethodId': 1,
                    },
                  },
                ],
              },
        },
        {
            $project: {
                _id: 0,
                chargeflowId: '$chargeflow_id',
                activeRecurringAppChargeId: '$billing.activeRecurringAppChargeId',
                shopPlatform: '$platform',
                paymentMethodId: '$company.users.billing.stripeCustomer.paymentMethodId',
                shopStatus: '$chargeflow_status',
                shopSource: '$source'
            }
        },

    ]

    try {
        const res = await shopsCollection.aggregate(agg).toArray();
        return res;
    } catch (err) {
        console.error(err);
        return [];
    }
    }
}

const script = new EnrichDisputesWithBillingAndShopData();
script.main();
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { chargeflowIds } from './utils';
import { createFolder } from '../asana-script/file-manager';
import { storeDisputesInFile  } from '../asana-script/logger';
import { ObjectId } from "mongodb";

class Script extends ScriptBase {
  public getName(): string {
    return "Delete imported disputes from Asana";
  }
  public getAuthor(): string {
    return "Goran Kuljanin"
  }
  public getTicket(): string {
    return "CHA-2860";
  }
  async dryRun(context: ScriptContext) {
    console.log("Running in dry run (not changing anything)");

    for (const chargeflowId of chargeflowIds) {
        await getDisputesFromProductionDB(chargeflowId);
    }
  }

  async wetRunOnSecondary(context: ScriptContext) {
    await this.doWetRun(false);
  }
  async wetRunOnPrimary(context: ScriptContext) {
    await this.doWetRun(true);
  }

  private async doWetRun(mainRun: boolean) {
    console.log("Running in wet run (changing data)");

    const client = await loadMongoClient();
    const session = client.startSession();
    try {
      session.startTransaction();

      for (const chargeflowId of chargeflowIds) {
        const disputes = await getDisputesFromProductionDB(chargeflowId);
        if (mainRun) {
          await deleteDisputesFromProductionDB(disputes, chargeflowId);
        }
      }
      
      await session.commitTransaction();
      client.close();
    } catch (ex) {
      console.warn('Aborting transaction');
      await session.abortTransaction();
      console.error(ex);
      throw ex;
    } finally {
      session.endSession();
    }
  }

}

async function getDisputesFromProductionDB(chargeflowId: ObjectId) {
  const client = await loadMongoClient();

  const disputes = await client.db('chargeflow_api')
          .collection('disputes')
          .find({
              'chargeflow.imported': true,
              'chargeflow.importTaskId': { $exists: true, $eq: null },
              'dispute.transactionId': { $exists: true, $eq: null },
              chargeflowId: chargeflowId
          }).toArray();
  
  console.log(`Number of disputes  ${disputes?.length} for chargeflowId ${chargeflowId.toString()}`);
  
  createFolder('disputes-to-delete/' + chargeflowId.toString(), '/ids');
  storeDisputesInFile(disputes, 'disputes-to-delete/' + chargeflowId.toString());

  console.log('Finished fetching disputes from DB');
  
  return disputes;
}

async function deleteDisputesFromProductionDB(disputes: any[], chargeflowId: ObjectId) {

  const client = await loadMongoClient();

  const disputeIds = disputes.map(item => item?._id);
  await client.db('chargeflow_api')
      .collection('disputes')
      .deleteMany({ _id: {$in: disputeIds}, chargeflowId: chargeflowId});
}

const script = new Script();
script.main();
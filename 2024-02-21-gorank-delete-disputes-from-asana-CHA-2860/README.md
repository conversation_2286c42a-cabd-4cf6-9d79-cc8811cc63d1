# Purpose
This script deletes previously imported disputes from Asana by specified ChargeflowIds array. This array can be edited to certain
ChargeflowIds in need to remove disputes from specific shops.

https://linear.app/chargeflow/issue/CHA-2860/write-a-script-to-delete-previously-imported-asana-tasks

## Motivation
There was an issue with mapping of previously imported disputes. With new decision, historic data will be imported from Asana exported file. Previously imported disputes should be deleted, so fresh historic data will be taken entirely from the file.

## Running this script
`npx ts-node ./delete-imported-from-asana.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD, that contains MongoDB connection string.
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the file
wet-secondary - does not update the source table, writes results to files
wet-primary - UPDATES THE DISPUTES TABLE. Use with extreme caution!

### How this script was tested
[x] New folder will be created 'disputes-to-delete' that contains subfolders where each represents a section/shop with a list of json files that represent disputes that will be deleted from DB
[x] Verify that those disputes should indeed be deleted

[ ] After deleting from DB with wet-secondary, verify that disputes from files are indeded deleted from DB, or by running this same script in dry-mode (in that case there should be no disputes to delete from DB)
       
The following scripts are used to fix data corruption, during importing from Asana and correcting date format.

These 3 steps will be followed.

1. Restore script for disputes imported from Asana where original was in Backup DB
2. For all the disputes that were created prior to Sept 1st 2023, we can restore all data from backup.
3. Updates submittedAt format for disputes.

Script description:

1. First script is used to retrieve all disputes from DB (for already imported shops), where 'chargeflow.importTaskId' is null.
This will return list of disputes that were overried by import from Asana, from Backup DB. 
The script will then filter those disputes that couldn't be matched with Backup DB, and will override them from Production DB.

How to run the script (from asana-script folder):
    1. run `npm install`
    2. In your .env file, within asana-script folder, set urls for MongoDB connection string:
        PRODUCTION_MONGODB_URL
        PRODUCTION_BACKUP_MONGODB_URL
    3. run 'npm run restore

2. Second script is used to retrieve all dispute (for already imported shop), where date created (by '_id' property is prior to 2023-09-01).
These disputes will be places in Production DB as they are.

How to run the script:
    1. run `npm install`
    2. In your .env file, set urls for MongoDB connection string:
        PRODUCTION_MONGODB_URL
        PRODUCTION_BACKUP_MONGODB_URL
    3. run 'npm run restore-old

3. This script is used to format submittedAt and dispute.dateUpdated property from string to Date.

How to run the script:
    1. run `npm install`
    2. In your .env file, set urls for MongoDB connection string:
        PRODUCTION_MONGODB_URL
    3. run 'npm run update-dates
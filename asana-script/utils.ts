import { ObjectId } from "mongodb";

export const chargeflowIds = [
    new ObjectId('64a5a9e08389c68e27ca1929'),   // dr-livingood
    new ObjectId('653828badeaa91f38cfe214b'),
    new ObjectId('653eb78c4c42ae369c5f3d0c'),
    new ObjectId('4d47f4de2bc4699dba55c288'),
    new ObjectId('61f02403a95f01d49733b476'),   // snowjoe
    new ObjectId('64a704b423933dc1a04ec752'),   // ultimateautographs
    new ObjectId('631a41ea44aa8d9cf5adc65a'),
    new ObjectId('645279a748590446c9db09fa'),
    new ObjectId('6571fc385583dea38164df37'),
    new ObjectId('65afab0488a52aaf56f1b6e7'),
    new ObjectId('642c752237253108a85d0f85'),
    new ObjectId('63bde904666a68685df0ed56'),
    new ObjectId('63dac95d0cfc99cceef00a86'),
    new ObjectId('64900cb8a7132f82ae5e5c6f'),
    new ObjectId('6452c5da7e41a5c8b53516f6'),
    new ObjectId('646fe32644ce6ad7a8d92aa0'),
    new ObjectId('6464eb37dbec9702ead5e316'),
    new ObjectId('6553559d756ccd06845a03c7'),
    new ObjectId('6464e0357f4308e59c2473dd'),
    new ObjectId('6464e622dbec9702ead5e2fc'),
    new ObjectId('62b4ccba56bb23d3278a0ee0'),
    new ObjectId('63a95b92542506b06440ce06'),
    new ObjectId('61f89aca91096967bac47343'),
    new ObjectId('6327cc6c44aa8d9cf5addff1')
];
// Restore disputes that were created before 2023-09-01

import { ObjectId } from 'mongodb';
import { getMongoClient, getMongoClientBackup } from './mongo-config';
import dotenv from 'dotenv';
import { storeDisputesInFile, storeErrorsInFile, storeTextInFile } from './logger';
import { closeFs, createFolder } from './file-manager';
import { chargeflowIds } from './utils';

dotenv.config();

storeTextInFile('Script started...\n');
restoreDisputesCreatedBeforeDateByChargeflowId();


// Import old ones created before 2023-09-01
async function restoreDisputesCreatedBeforeDateByChargeflowId() {
    try {

        for (const chargeflowId of chargeflowIds) {
            await restoreDisputesFromBackup(chargeflowId);
        }

    
    } catch (err) {
        storeErrorsInFile(err);
    }

    closeFs();
}

async function restoreDisputesFromBackup(chargeflowId: ObjectId) {
    const client = await getMongoClientBackup();
    const disputes = await client.db('chargeflow_api')
            .collection('disputes')
            .find({
                    _id: { $lt: new ObjectId('64f0ff500000000000000000') },
                    chargeflowId: chargeflowId
                })
            .toArray();
    
    storeTextInFile(`Finish ${disputes?.length}\n`);
    createFolder('restore-backup/' + chargeflowId.toString(), '/ids');
    storeDisputesInFile(disputes, 'restore-backup/' + chargeflowId.toString());
            
    // TODO: Uncomment this line to save disputes to the new DB
    // await updateManyCFDispute(disputes, chargeflowId);
}

export async function updateManyCFDispute(disputes: any[], chargeflowId: ObjectId) {

    if (disputes.length < 1) {
        storeTextInFile(`There are no disputes for chargeflowId: ${chargeflowId}.\n`);
        return;
    }

    try {
        const client = await getMongoClient();
        const operations = disputes.map(dispute => ({
            updateOne: {
                filter: { _id: dispute._id, chargeflowId: chargeflowId},
                update: { $set: dispute },
            }
        }));
        
        const res = await client.db('chargeflow_api').collection('disputes').bulkWrite(operations);
        storeTextInFile(`Modified: ${res.modifiedCount}, for chargeflowId: ${chargeflowId}.\n`);
    } catch (err) {
        storeTextInFile(`Exception occurred for chargeflowId: ${chargeflowId}, ${JSON.stringify(err)}.\n`);
        storeErrorsInFile(err);
        throw err;

    }
}

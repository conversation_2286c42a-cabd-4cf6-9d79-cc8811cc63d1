// Update script for updating disputes with Date format

import { ObjectId } from 'mongodb';
import { closeMongoDB, getMongoClient } from './mongo-config';
import dotenv from 'dotenv';
import { storeDisputesInFile, storeErrorsInFile, storeTextInFile } from './logger';
import { closeFs, createFolder } from './file-manager';

dotenv.config();

updateDateFormatFromUpdateDisputesService();

// ***********************************************************************************************

async function updateDateFormatFromUpdateDisputesService() {
    try {
        const logMessage = 'updateDateFormatFromUpdateDisputesService invoked\n';
        storeTextInFile(logMessage);

        await getAllProductionDisputesAndUpdateWithDateFormat();
        
    }
    catch (err) {
        storeErrorsInFile(err);
    }

    closeFs();
    await closeMongoDB();
}

async function getAllProductionDisputesAndUpdateWithDateFormat() {
    const client = await getMongoClient();

    const disputes = await client.db('chargeflow_api')
            .collection('disputes')
            .find({
                'chargeflow.submitted.submittedAt': {$type: 'string'}
            }).toArray();
    
    storeTextInFile(`Number of disputes  ${disputes?.length}\n`);
    
    createFolder('production/', '/ids');
    storeDisputesInFile(disputes, 'production/');

    const updatedDisputes: any[] = [];
    for (const dispute of disputes) {
        // TODO: Uncomment these 2 line to update the disputes after dry run
        // const updated = await updateDateOfOriginalDispute(dispute);
        // updatedDisputes.push(updated);
    }

    createFolder('production-date-updated/', '/ids');
    storeDisputesInFile(updatedDisputes, 'production-date-updated/');

    storeTextInFile('Finished.\n');
}

async function updateDateOfOriginalDispute(originalDispute: any) {
    const client = await getMongoClient();
    const submittedAtDate = new Date(originalDispute?.chargeflow?.submitted?.submittedAt);
    const disputeUpdated = new Date(originalDispute?.dispute?.dateUpdated);
    storeTextInFile(`Dispute ${originalDispute?._id} submittedAt: ${originalDispute?.chargeflow?.submitted?.submittedAt}, newDate: ${submittedAtDate}, dateUpdated: ${disputeUpdated}\n`);
    
    const updated = await client.db('chargeflow_api')
        .collection('disputes')
        .findOneAndUpdate(
            { _id: new ObjectId(originalDispute?._id) }, 
            { $set: { 
                'chargeflow.submitted.submittedAt': submittedAtDate,
                'dispute.dateUpdated': disputeUpdated
            } },
            { returnDocument: 'after'});

    return updated.value;
}

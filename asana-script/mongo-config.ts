
import {  MongoClient  } from 'mongodb';
import dotenv from 'dotenv';

dotenv.config();

let client: MongoClient;
let clientBackup: MongoClient;

interface MongoClientOptions {
    useNewUrlParser: boolean;
    useUnifiedTopology: boolean;
    auth?: { username: string; password: string };
}

async function initClient(...args: any[]): Promise<MongoClient> {
    const options: MongoClientOptions = {
        useNewUrlParser: true,
        useUnifiedTopology: true,
    };

    try {
        return await MongoClient.connect(process.env.PRODUCTION_MONGODB_URL ?? '');

    } catch (err) {
            console.warn('No network connection: %s', err);
        throw err;
    }
}

export async function getMongoClient() {

    if (!client) {
        client = await initClient();
    }

    return client;
}

async function initClientBackup(...args: any[]): Promise<MongoClient> {
    const options: MongoClientOptions = {
        useNewUrlParser: true,
        useUnifiedTopology: true,
    };

    try {
        return await MongoClient.connect(process.env.PRODUCTION_BACKUP_MONGODB_URL ?? '');

    } catch (err) {
            console.warn('No network connection: %s', err);
        throw err;
    }
}

export async function getMongoClientBackup() {

    if (!clientBackup) {
        clientBackup = await initClientBackup();
    }

    return clientBackup;
}

export async function closeMongoDB() {

    if (clientBackup) {
        clientBackup.close();
    }

    if (client) {
        client.close();
    }
}

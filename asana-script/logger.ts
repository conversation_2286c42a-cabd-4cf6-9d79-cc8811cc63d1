import fs from 'fs';

const errorPath = 'errors.txt';
const logPath = 'log.txt';
const filePath = 'disputes.txt';
const idsPath = 'ids.txt';

export function storeErrorsInFile(error: any) {
    const logMessage = `Error occured: ${JSON.stringify(error)}\n`;
    fs.appendFile(errorPath, logMessage, logError);
}

export function storeTextInFile(logMessage: string) {
    fs.appendFile(logPath, logMessage, logError);
}

export function storeDisputesInFile(disputes: any[], folder: string) {

    const ids = disputes.map(item => JSON.stringify({_id: item?._id, disputeId: item?.dispute?.id})).join('\n');
    fs.writeFileSync(folder + '/ids/ids.txt', ids);

    disputes.forEach(item => {
        const fileContent = JSON.stringify(item, null, 4);
        const fileName = `${item._id}.json`;
        fs.writeFileSync(folder + '/' + fileName, fileContent);
    });
}

export function storeDisputeIds(disputeIds: any[], folder: string) {
    const ids = disputeIds.map(item => JSON.stringify({caseId: item})).join('\n');
    fs.writeFile(folder + '/ids.txt', ids, logError);
}

function logError(error: any) {
    if (error)
        console.log('Error occured: ', error);
}

// Restore script for disputes imported from Asana where original was in Backup DB

import { ObjectId } from 'mongodb';
import { closeMongoDB, getMongoClient, getMongoClientBackup } from './mongo-config';
import dotenv from 'dotenv';
import { storeDisputesInFile, storeErrorsInFile, storeTextInFile } from './logger';
import { closeFs, createFolder } from './file-manager';
// import { chargeflowIds } from './utils';

dotenv.config();

export const chargeflowIds = [
    new ObjectId('61f02403a95f01d49733b476'),   // snowjoe
    new ObjectId('64a5a9e08389c68e27ca1929'),   // dr-livingood
    new ObjectId('64a704b423933dc1a04ec752'),   // ultimateautographs
];

restoreOverridenImportedDisputesFromAsanaFromProductionDB();

// ***********************************************************************************************

async function restoreOverridenImportedDisputesFromAsanaFromProductionDB() {
    try {
        const logMessage = 'removeImportedDisputesFromProductionDB invoked\n';
        storeTextInFile(logMessage);

        for (const chargeflowId of chargeflowIds) {
            await restoreDisputesFromBackupDBtoProductionDB(chargeflowId);
        }
    }
    catch (err) {
        storeErrorsInFile(err);
    }

    closeFs();
    await closeMongoDB();
}

async function restoreDisputesFromBackupDBtoProductionDB(chargeflowId: ObjectId) {
    const client = await getMongoClient();

    const disputes = await client.db('chargeflow_api')
            .collection('disputes')
            .find({
                // 'chargeflow.imported': true,
                'chargeflow.importTaskId': { $exists: true, $eq: null },
                "order.id": { $exists: true },
                chargeflowId: chargeflowId,
            }).toArray();
    
    storeTextInFile(`Number of disputes  ${disputes?.length} for chargeflowId ${chargeflowId.toString()}\n`);
    
    createFolder('production/' + chargeflowId.toString(), '/ids');
    storeDisputesInFile(disputes, 'production/' + chargeflowId.toString());

    const disputeIds = disputes.map(item => item?._id);
    const backupDisputes = await findDisputesInBackupDB(disputeIds, chargeflowId);
    if (backupDisputes) {
        storeTextInFile(`Disputes found in backup DB: ${backupDisputes.length}.\n`);
        createFolder('backup/' + chargeflowId.toString(), '/ids');
        storeDisputesInFile(backupDisputes, 'backup/' + chargeflowId.toString());
    }

    // Delete disputes not found in backup DB
    const disputesToDelete = disputes.filter(
        item => !backupDisputes.some(
            backupItem => backupItem?._id.toString() === item?._id.toString() && backupItem?.dispute?.id === item?.dispute?.id));
    storeTextInFile(`Disputes to delete: ${disputesToDelete.length}.\n`);
    createFolder('to-delete/' + chargeflowId.toString(), '/ids');
    storeDisputesInFile(disputesToDelete, 'to-delete/' + chargeflowId.toString());


    // Restore disputes from backup DB to Production DB
    for (const dispute of disputeIds) {
        const backupDocument = backupDisputes.find(item => item?._id.toString() === dispute.toString());
        if (backupDocument) {
            // TODO: Uncomment the line below to restore disputes from backup DB to production DB
            // await restoreDispute(backupDocument);
        }
    }

    storeTextInFile('Finished.\n');
}

async function restoreDispute(backupDispute: any) {
    storeTextInFile(`Update dispute from backup: ${backupDispute?._id}.\n`);
    const client = await getMongoClient();
    await client.db('chargeflow_api')
        .collection('disputes')
        .updateOne({ _id: new ObjectId(backupDispute?._id) }, { $set: backupDispute });
}

async function findDisputesInBackupDB(disputeIds: ObjectId[], chargeflowId: ObjectId) {
    const client = await getMongoClientBackup();
    return await client.db('chargeflow_api')
        .collection('disputes')
        .find({ _id: { $in: disputeIds }, chargeflowId: chargeflowId})
        .toArray();
}

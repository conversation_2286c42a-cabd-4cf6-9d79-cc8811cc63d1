import fs from 'fs';

export function createFolder(folderName: string, subFolderName: string | null = null) {
    if (!fs.existsSync(folderName)) {
        fs.mkdirSync(folderName, { recursive: true });
        if (subFolderName) {
            fs.mkdirSync( folderName + '/' + subFolderName);
        }
    } else if (subFolderName) {
        fs.mkdirSync( folderName + '/' + subFolderName);
    }
}

export function closeFs() {
    fs.closeSync(0);
}
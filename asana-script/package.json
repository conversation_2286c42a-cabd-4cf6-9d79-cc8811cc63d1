{"name": "data-unification", "description": "Data Unification service to upload manually collected disputes and synchronize them with one source of thruth", "version": "1.0.0", "private": true, "scripts": {"restore-old": "ts-node restore-old-disputes.ts", "restore": "ts-node restore-disputes-from-backup-db.ts", "update-dates": "ts-node update-dates-format-from-dispute-service.ts"}, "devDependencies": {"cross-env": "^7.0.3", "dotenv": "^16.4.1", "eslint": "^8.56.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-json": "^3.1.0", "eslint-plugin-mocha": "^10.1.0", "eslint-plugin-yml": "^1.8.0", "lint-staged": "^13.2.2", "npm-check-updates": "^16.10.12", "npm-run-all": "^4.1.5", "proxyquire": "^2.1.3", "source-map-support": "^0.5.21", "ts-migrate": "^0.1.35", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "optionalDependencies": {"aws4": "^1.12.0", "mongodb": "^5.6.0"}, "dependencies": {"aws-sdk": "^2.1399.0", "aws-xray-sdk": "^3.5.0", "esbuild": "^0.19.5", "json2csv": "^5.0.7", "node-fetch": "^3.3.2"}}
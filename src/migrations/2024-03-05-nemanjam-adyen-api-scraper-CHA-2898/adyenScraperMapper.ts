import {CardHolderInfo, RootObject} from "./scraped-types";
import {parseISO} from "date-fns";
import {
    IChargeflowObject,
    IDisputeStatus,
    IUnifiedDisputeObject,
    IChecks
} from "@chargeflow-team/data-access/dist/Dispute/Types";
import {PaymentMethod} from "./Types";
import {
    ChargeFlowDisputeStatus,
    DisputeReason,
    DisputeStage,
    DisputeStatusSource,
} from "@chargeflow-team/data-access/dist/Dispute/enums";
import {Source} from "@chargeflow-team/data-access/dist/shared/enums";

export default class AdyenScraperMapper {

    public mapToUDO(data: RootObject, chargeflowId: string, shopId: string, shopName: string, liveMode: boolean): IUnifiedDisputeObject {
        const disputeStatuses: IDisputeStatus[] = [];
        const disputeStatus: IDisputeStatus = {
            dateCreated: new Date(),
            statusDate: null,
            status: this.mapToDisputeStatus(data?.disputeDetails?.pspDetails?.disputeInformation?.status),
            processorStatus: data?.disputeDetails?.pspDetails?.disputeInformation?.status || null,
            source: DisputeStatusSource.Reconciliation
        }
        disputeStatuses.push(disputeStatus);

        const chargeFlow: IChargeflowObject = {
            templates: [],
            evidences: [],
            cfActions: null,
            resolved: null,
            chargeScore: null,
            submitted: {}
        }

        const cvcCheckCode = this.mapCVCCode(data?.disputeDetails?.paymentPspDetails?.processingInfo?.cvcResponseCode);
        const avsCheckCode = this.mapAVSCode(data?.disputeDetails?.paymentPspDetails?.processingInfo?.avsResponseCode);
        
        const checks: IChecks = {
            cvcCheck: {
                code: cvcCheckCode,
                description: this.cvcDescriptionMapper(cvcCheckCode),
            },
            avsCheck: {
                code: avsCheckCode,
                description: this.avsDescriptionMapper(avsCheckCode),
            },
            addressLine1Check: null,
            addressZipCheck: null,
        }

        return {
            shopId: shopId,
            chargeflowId: chargeflowId,
            shopName: shopName,
            liveMode: true,
            dateCreated: new Date(),
            dateUpdated: null,
            dispute: {
                id: data.dispute.pspReference,
                dateCreated: parseISO(data.dispute.creationDate),
                dateReceived: new Date(),
                dateUpdated: null,
                processor: Source.Adyen,
                source: Source.Adyen,
                amount: data.dispute.amount.value / 100,
                currency: data.dispute.amount.currency,
                reason: this.mapToDisputeReason(data.dispute.chbSchemeCode as PaymentMethod, data.dispute.chbReasonCode),
                processorReason: data.dispute.description,
                reasonCode: data.dispute.chbReasonCode,
                responseDue: this.calculateDueDate(data.dispute.daysLeftToDefend),
                merchantOrderId: data.disputeDetails.paymentPspDetails.merchantBean?.merchantOrderReference,
                stage: this.mapToDisputeStage(data.dispute.status),
                disputeStatuses: disputeStatuses,
                intentionalFraud: false,
                closedDate: null,
                transactionId: "",
                disputeFee: null,
                mid: null,
                statementDescriptor: null,
                submittedCount: null,
                isRefundable: null,
                submittedAt: null,
                actions: [],
                rawData: {dispute: {}, webhooks: []}
            },
            extensions: [],
            isBillingActive: false,
            lastDisputeStatus: disputeStatus,
            chargeflow: chargeFlow,
            order: null,
            intentionalFraud: null,
            shippingData: null,
            orderDate: null,
            orderName: null,
            transaction: {
                id: data.dispute.paymentPspReference,
                dateReceived: new Date(),
                dateUpdated: null,
                receiptUrl: null,
                dateCreated: new Date(data.dispute.paymentCreationDate),
                amount: data?.disputeDetails?.paymentPspDetails?.paymentOverview?.amount?.value / 100,
                currency: data?.disputeDetails?.paymentPspDetails?.paymentOverview?.amount?.currency,
                source: 'adyen',
                orderId: null,
                risk: null,
                statementDescriptor: null,
                rawData: {
                    transaction: {},
                    webhooks: [],
                },
                checks,
                billing: {
                    name: data?.disputeDetails?.paymentPspDetails?.shopperInfo?.shopperPersonalDetails?.firstName +
                        " " + data?.disputeDetails?.paymentPspDetails?.shopperInfo?.shopperPersonalDetails?.lastName,
                    city: data?.disputeDetails?.paymentPspDetails?.shopperInfo?.shopperBillingAddress || null,
                    state: data?.disputeDetails?.paymentPspDetails?.shopperInfo?.shopperBillingAddress || null,
                    country: data?.disputeDetails?.paymentPspDetails?.shopperInfo?.shopperBillingCountry || null,
                    line2: data?.disputeDetails?.paymentPspDetails?.shopperInfo?.shopperBillingAddress || null,
                    line1: data?.disputeDetails?.paymentPspDetails?.shopperInfo?.shopperBillingAddress || null,
                    zip: data?.disputeDetails?.paymentPspDetails?.shopperInfo?.shopperBillingAddress || null,
                    phone: null,
                },
                customerName:  data?.disputeDetails?.paymentPspDetails?.shopperInfo?.shopperPersonalDetails?.firstName +
                        " " + data?.disputeDetails?.paymentPspDetails?.shopperInfo?.shopperPersonalDetails?.lastName,
                customerEmail: data?.disputeDetails?.paymentPspDetails?.shopperInfo?.shopperEmail || null,
                paymentDetails: {
                    cardHolderName: data?.disputeDetails?.paymentPspDetails?.cardHolderInfo?.cardHolder || null,
                    cardBrand: data?.disputeDetails?.paymentPspDetails?.cardHolderInfo?.paymentMethod?.description,
                    country: data?.disputeDetails?.paymentPspDetails?.cardHolderInfo?.paymentMethod?.description,
                    expMonth: +data?.disputeDetails?.paymentPspDetails?.cardHolderInfo?.expiryDate.substring(0, 2),
                    expYear: +data?.disputeDetails?.paymentPspDetails?.cardHolderInfo?.expiryDate.slice(-4),
                    installments: data?.disputeDetails?.paymentPspDetails?.information?.numberOfInstallments,
                    network: data?.disputeDetails?.paymentPspDetails?.cardHolderInfo?.issuerCountryCode,
                    last4: this.getLast4(data?.disputeDetails?.paymentPspDetails?.cardHolderInfo),
                    issuer: data?.disputeDetails?.paymentPspDetails?.cardHolderInfo?.issuerName || null,
                    '3dSecure': null,
                },
            },
        };
    }

    private mapToDisputeReason(paymentMethod: PaymentMethod, reasonCode: string): DisputeReason {
        const visaMapping: Record<string, DisputeReason> = {
            '10.1': DisputeReason.Fraud,
            '10.2': DisputeReason.Fraud,
            '10.3': DisputeReason.Fraud,
            '10.4': DisputeReason.Fraud,
            '10.5': DisputeReason.Fraud,
            '11.1': DisputeReason.General,
            '11.2': DisputeReason.General,
            '11.3': DisputeReason.General,
            '12.1': DisputeReason.General,
            '12.2': DisputeReason.General,
            '12.3': DisputeReason.General,
            '12.4': DisputeReason.General,
            '12.5': DisputeReason.General,
            '12.6': DisputeReason.DuplicateCharge,
            '12.7': DisputeReason.General,
            '13.1': DisputeReason.NotReceived,
            '13.2': DisputeReason.CanceledRecurringBilling,
            '13.3': DisputeReason.NotAsDescribed,
            '13.4': DisputeReason.NotAsDescribed,
            '13.5': DisputeReason.NotAsDescribed,
            '13.6': DisputeReason.CreditNotProcessed,
            '13.7': DisputeReason.CreditNotProcessed,
            '13.8': DisputeReason.General,
        };

        const achMapping: Record<string, DisputeReason> = {
            'R01': DisputeReason.InsufficientFunds,
            'R02': DisputeReason.General,
            'R03': DisputeReason.IncorrectAccountDetails,
            'R04': DisputeReason.IncorrectAccountDetails,
            'R05': DisputeReason.Unauthorized,
            'R06': DisputeReason.General,
            'R07': DisputeReason.Unauthorized,
            'R08': DisputeReason.General,
            'R09': DisputeReason.General,
            'R10': DisputeReason.Unauthorized,
            'R11': DisputeReason.General,
            'R12': DisputeReason.General,
            'R13': DisputeReason.IncorrectAccountDetails,
            'R15': DisputeReason.General,
            'R16': DisputeReason.General,
            'R17': DisputeReason.General,
            'R20': DisputeReason.General,
            'R29': DisputeReason.General,
            'R31': DisputeReason.General,
            'R51': DisputeReason.General,
        };

        const amexMapping: Record<string, DisputeReason> = {
            '4521': DisputeReason.General,
            'A02': DisputeReason.Unauthorized,
            'A01': DisputeReason.CreditNotProcessed,
            'A08': DisputeReason.General,
            '4751': DisputeReason.General,
            '4540': DisputeReason.Fraud,
            'F29': DisputeReason.Fraud,
            '4763': DisputeReason.Fraud,
            'FR2': DisputeReason.Fraud,
            '4798': DisputeReason.Fraud,
            'F30': DisputeReason.Fraud,
            '4799': DisputeReason.Fraud,
            'F31': DisputeReason.Fraud,
            '4534': DisputeReason.Fraud,
            'F24': DisputeReason.Fraud,
            'FR4': DisputeReason.Fraud,
            'FR6': DisputeReason.Fraud,
            '4507': DisputeReason.IncorrectAmount,
            'P05': DisputeReason.IncorrectAmount,
            '4512': DisputeReason.DuplicateCharge,
            'P08': DisputeReason.DuplicateCharge,
            '4522': DisputeReason.Unauthorized,
            '4523': DisputeReason.IncorrectAccountDetails,
            'P01': DisputeReason.IncorrectAccountDetails,
            '4525': DisputeReason.IncorrectAmount,
            '4530': DisputeReason.IncorrectAmount,
            'P23': DisputeReason.IncorrectAmount,
            '4536': DisputeReason.General,
            'P07': DisputeReason.General,
            '4752': DisputeReason.General,
            'P03': DisputeReason.General,
            'P04': DisputeReason.General,
            '4755': DisputeReason.NotReceived,
            '4758': DisputeReason.General,
            'F22': DisputeReason.General,
            '4515': DisputeReason.DuplicateCharge,
            'C14': DisputeReason.DuplicateCharge,
            '4532': DisputeReason.NotAsDescribed,
            '4544': DisputeReason.CreditNotProcessed,
            'C28': DisputeReason.CanceledRecurringBilling,
            '4554': DisputeReason.NotReceived,
            'C08': DisputeReason.NotReceived,
            '4754': DisputeReason.General,
            'C02': DisputeReason.CreditNotProcessed,
            'C04': DisputeReason.CreditNotProcessed,
            'C05': DisputeReason.CreditNotProcessed,
            'C31': DisputeReason.NotAsDescribed,
            'C32': DisputeReason.NotAsDescribed,
        };

        const cashAppPayMapping: Record<string, DisputeReason> = {
            'FR10': DisputeReason.Fraud,
            'FR11': DisputeReason.Fraud,
            'PE10': DisputeReason.DuplicateCharge,
            'PE11': DisputeReason.IncorrectAmount,
            'PE12': DisputeReason.DuplicateCharge,
            'CD10': DisputeReason.CanceledRecurringBilling,
            'CD11': DisputeReason.NotAsDescribed,
            'CD12': DisputeReason.NotReceived,
            'CD13': DisputeReason.CreditNotProcessed,
        };

        const carteBancaireMapping: Record<string, DisputeReason> = {
            '45': DisputeReason.Fraud,
        };

        const discoverMapping: Record<string, DisputeReason> = {
            'C46': DisputeReason.Fraud,
            'C41': DisputeReason.Fraud,
            'C42': DisputeReason.Fraud,
            'C53': DisputeReason.Fraud,
            'C54': DisputeReason.Fraud,
            'A06': DisputeReason.IncorrectAccountDetails,
            'A02': DisputeReason.General,
            'B24': DisputeReason.General,
            'B25': DisputeReason.DuplicateCharge,
            'B26': DisputeReason.General,
            'B27': DisputeReason.General,
            'D61': DisputeReason.IncorrectAmount,
            'D67': DisputeReason.DuplicateCharge,
            'D62': DisputeReason.NotReceived,
            'D66': DisputeReason.CreditNotProcessed,
            'D69': DisputeReason.CanceledRecurringBilling,
            'D70': DisputeReason.Fraud,
            '4752': DisputeReason.General,
            '4866': DisputeReason.Fraud,
            '4867': DisputeReason.Fraud,
            '7010': DisputeReason.Fraud,
            '7030': DisputeReason.Fraud,
            '4753': DisputeReason.General,
            '4534': DisputeReason.DuplicateCharge,
            '4542': DisputeReason.General,
            '4550': DisputeReason.General,
            '4586': DisputeReason.IncorrectAmount,
            '4865': DisputeReason.DuplicateCharge,
            '4755': DisputeReason.NotReceived,
            '4553': DisputeReason.NotAsDescribed,
            '4541': DisputeReason.CanceledRecurringBilling,
            '8002': DisputeReason.CreditNotProcessed,
        };

        const eloMapping: Record<string, DisputeReason> = {
            '75': DisputeReason.Fraud,
            '83': DisputeReason.Fraud,
            '30': DisputeReason.NotReceived,
            '41': DisputeReason.CanceledRecurringBilling,
            '53': DisputeReason.NotAsDescribed,
            '74': DisputeReason.General,
            '77': DisputeReason.IncorrectAccountDetails,
            '80': DisputeReason.General,
            '82': DisputeReason.DuplicateCharge,
            '85': DisputeReason.CreditNotProcessed,
        };

        const jcbMapping: Record<string, DisputeReason> = {
            '534': DisputeReason.DuplicateCharge,
            '546': DisputeReason.Unauthorized,
            '526': DisputeReason.General,
            '527': DisputeReason.General,
            '517': DisputeReason.General,
            '522': DisputeReason.General,
            '523': DisputeReason.General,
            '503': DisputeReason.General,
            '547': DisputeReason.General,
            '507': DisputeReason.IncorrectAmount,
            '510': DisputeReason.General,
            '512': DisputeReason.DuplicateCharge,
            '524': DisputeReason.IncorrectAmount,
            '525': DisputeReason.IncorrectAmount,
            '536': DisputeReason.General,
            '541': DisputeReason.General,
            '581': DisputeReason.General,
            '583': DisputeReason.DuplicateCharge,
            '502': DisputeReason.NotReceived,
            '513': DisputeReason.CreditNotProcessed,
            '516': DisputeReason.NotReceived,
            '544': DisputeReason.CanceledRecurringBilling,
            '554': DisputeReason.NotReceived,
            '537': DisputeReason.General,
            '538': DisputeReason.General,
        };

        const mastercardMapping: Record<string, DisputeReason> = {
            '4837': DisputeReason.Fraud,
            '4840': DisputeReason.General,
            '4849': DisputeReason.Fraud,
            '4863': DisputeReason.Unrecognized,
            '4870': DisputeReason.Fraud,
            '4871': DisputeReason.Fraud,
            '4999': DisputeReason.General,
            '4835': DisputeReason.General,
            '4807': DisputeReason.General,
            '4808': DisputeReason.General,
            '4812': DisputeReason.General,
            '4834': DisputeReason.DuplicateCharge,
            '4831': DisputeReason.General,
            '4842': DisputeReason.General,
            '4846': DisputeReason.General,
            '4850': DisputeReason.NotAsDescribed,
            '4853': DisputeReason.NotAsDescribed,
            '4841': DisputeReason.CanceledRecurringBilling,
            '4854': DisputeReason.NotAsDescribed,
            '4855': DisputeReason.NotReceived,
            '4860': DisputeReason.CreditNotProcessed,
            '6305': DisputeReason.IncorrectAmount,
        };

        const mexicoMapping: Record<string, DisputeReason> = {
            '63A': DisputeReason.Fraud,
            '63B': DisputeReason.Fraud,
            '63C': DisputeReason.Fraud,
            '63D': DisputeReason.Fraud,
            '68A': DisputeReason.Fraud,
            '68B': DisputeReason.Fraud,
            '68C': DisputeReason.Fraud,
            '68D': DisputeReason.Fraud,
            '89A': DisputeReason.Unauthorized,
            '64A': DisputeReason.General,
            '64B': DisputeReason.General,
            '71A': DisputeReason.General,
            '71B': DisputeReason.General,
            '72A': DisputeReason.IncorrectAmount,
            '72C': DisputeReason.IncorrectAmount,
            '72B': DisputeReason.Unauthorized,
            '72D': DisputeReason.Unauthorized,
            '73A': DisputeReason.Unauthorized,
            '73B': DisputeReason.Unauthorized,
            '57A': DisputeReason.Unauthorized,
            '57C': DisputeReason.Unauthorized,
            '57B': DisputeReason.Unauthorized,
            '57D': DisputeReason.Unauthorized,
            '75A': DisputeReason.General,
            '75B': DisputeReason.General,
            '76A': DisputeReason.Unrecognized,
            '76C': DisputeReason.Unrecognized,
            '76B': DisputeReason.Unrecognized,
            '76D': DisputeReason.Unrecognized,
            '86A': DisputeReason.IncorrectAmount,
            '86B': DisputeReason.IncorrectAmount,
            '87A': DisputeReason.Unauthorized,
            '57E': DisputeReason.Unauthorized,
            '74A': DisputeReason.General,
            '74C': DisputeReason.General,
            '74B': DisputeReason.General,
            '74D': DisputeReason.General,
            '77A': DisputeReason.IncorrectAccountDetails,
            '80A': DisputeReason.General,
            '80B': DisputeReason.General,
            '80C': DisputeReason.General,
            '81A': DisputeReason.Unauthorized,
            '82A': DisputeReason.DuplicateCharge,
            '82C': DisputeReason.DuplicateCharge,
            '82B': DisputeReason.DuplicateCharge,
            '82D': DisputeReason.DuplicateCharge,
            '82E': DisputeReason.DuplicateCharge,
            '82F': DisputeReason.DuplicateCharge,
            '82G': DisputeReason.DuplicateCharge,
            '88A': DisputeReason.IncorrectAmount,
            '94A': DisputeReason.General,
            '94E': DisputeReason.General,
            '94B': DisputeReason.General,
            '94F': DisputeReason.General,
            '94C': DisputeReason.General,
            '94D': DisputeReason.General,
            '94G': DisputeReason.General,
            '94H': DisputeReason.General,
            '94I': DisputeReason.General,
            '95A': DisputeReason.Unauthorized,
            '95B': DisputeReason.Unauthorized,
            '95C': DisputeReason.Unauthorized,
            '95D': DisputeReason.Unauthorized,
            '41A': DisputeReason.CanceledRecurringBilling,
            '41G': DisputeReason.CanceledRecurringBilling,
            '41B': DisputeReason.CanceledRecurringBilling,
            '41H': DisputeReason.CanceledRecurringBilling,
            '41C': DisputeReason.CanceledRecurringBilling,
            '41I': DisputeReason.CanceledRecurringBilling,
            '41D': DisputeReason.CanceledRecurringBilling,
            '41E': DisputeReason.CanceledRecurringBilling,
            '41F': DisputeReason.CanceledRecurringBilling,
            '84A': DisputeReason.Unauthorized,
            '84B': DisputeReason.Unauthorized,
            '84C': DisputeReason.Unauthorized,
            '84D': DisputeReason.Unauthorized,
            '84E': DisputeReason.Unauthorized,
            '84F': DisputeReason.General,
            '85A': DisputeReason.CreditNotProcessed,
            '85B': DisputeReason.CreditNotProcessed,
            '60A': DisputeReason.General,
            '60B': DisputeReason.General,
            '60C': DisputeReason.General,
        };

        const sepaMappings: Record<string, DisputeReason> = {
            'AC01': DisputeReason.IncorrectAccountDetails,
            'AC04': DisputeReason.General,
            'AC06': DisputeReason.General,
            'AM04': DisputeReason.InsufficientFunds,
            'DNOR': DisputeReason.General,
            'MD01': DisputeReason.General,
            'MD06': DisputeReason.General,
            'MS02': DisputeReason.General,
            'MS03': DisputeReason.General,
            'SL01': DisputeReason.General,
        };

        const unionpayMappings: Record<string, DisputeReason> = {
            '4515': DisputeReason.Fraud,
            '4514': DisputeReason.DuplicateCharge,
            '4562': DisputeReason.Fraud,
            '4507': DisputeReason.IncorrectAmount,
            '4508': DisputeReason.IncorrectAmount,
            '4512': DisputeReason.DuplicateCharge,
            '4522': DisputeReason.General,
            '4536': DisputeReason.General,
            '4806': DisputeReason.DuplicateCharge,
            '4502': DisputeReason.NotReceived,
            '4532': DisputeReason.NotAsDescribed,
            '4544': DisputeReason.CanceledRecurringBilling,
        };

        switch (paymentMethod) {
            case PaymentMethod.ACH:
                return achMapping[reasonCode] || DisputeReason.General;
            case PaymentMethod.AmericanExpress:
                return amexMapping[reasonCode] || DisputeReason.General;
            case PaymentMethod.CashAppPay:
                return cashAppPayMapping[reasonCode] || DisputeReason.General;
            case PaymentMethod.CarteBancaire:
                return carteBancaireMapping[reasonCode] || DisputeReason.General;
            case PaymentMethod.Discover:
                return discoverMapping[reasonCode] || DisputeReason.General;
            case PaymentMethod.Elo:
                return eloMapping[reasonCode] || DisputeReason.General;
            case PaymentMethod.JCB:
                return jcbMapping[reasonCode] || DisputeReason.General;
            case PaymentMethod.Mastercard:
                return mastercardMapping[reasonCode] || DisputeReason.General;
            case PaymentMethod.MexicoDomestic:
                return mexicoMapping[reasonCode] || DisputeReason.General;
            case PaymentMethod.Sepa:
                return sepaMappings[reasonCode] || DisputeReason.General;
            case PaymentMethod.UnionPay:
                return unionpayMappings[reasonCode] || DisputeReason.General;
            case PaymentMethod.Visa:
                return visaMapping[reasonCode] || DisputeReason.General;
            default:
                return DisputeReason.General;
        }
    }

    private calculateDueDate(daysLeftToDefend: number) {
        const date = new Date();
        date.setDate(date.getDate() + daysLeftToDefend);
        return date;
    }

    private mapToDisputeStatus(status: string): ChargeFlowDisputeStatus {
        switch (status) {
            case "Undefended":
            case "Chargeback":
                return ChargeFlowDisputeStatus.NeedsResponse;
            case "Pending":
            case "InformationSupplied":
            case "ChargebackReversed":
                return ChargeFlowDisputeStatus.UnderReview;
            case "Won":
                return ChargeFlowDisputeStatus.Won;
            case "SecondChargeback":
            case "Expired":
            case "Lost":
            case "Accepted":
                return ChargeFlowDisputeStatus.Lost;
        }

        return ChargeFlowDisputeStatus.NeedsResponse;
    }

    private mapToDisputeStage(stage: string): DisputeStage {
        switch (stage) {
            case 'SecondChargeback':
                return DisputeStage.PreArbitration;
            default:
                return DisputeStage.Chargeback;
        }
    }

    private getLast4(cardHolderInfo: CardHolderInfo) {
        if (cardHolderInfo.originalCardSummary) {
            return cardHolderInfo.originalCardSummary?.slice(-4)
        } else if (cardHolderInfo.cardNumber) {
            return cardHolderInfo.cardNumber?.slice(-4)
        }
        return "";
    }

    private mapAVSCode(value: number) {
        switch (value) {
            case 1:
                return 'A'
            case 2:
                return 'N'
            case 6:
                return 'Z'
            case 7:
                return 'Y'
            case 12:
                return 'B'
            case 15:
                return 'P'
            default:
                return null
        }
    }

    

    private mapCVCCode(value: number) {
        switch (value) {
            case 0:
            case 3:
                return null;
            case 1:
                return 'M'
            case 2:
                return 'N'
            case 4:
            case 6:
                return 'I'
            case 5:
                return 'A'
            default: return null;
        }
    }

    private avsDescriptionMapper(code: string | null) {
        if (!code) return null;
        switch (code) {
            case 'X':
                return 'Street address and 9-digit ZIP code both match.';
            case 'Y':
                return 'Street address and 5-digit ZIP code both match.';
            case 'A':
                return 'Street address matches, but both 5-digit and 9-digit ZIP Code do not match.';
            case 'W':
                return 'Street address does not match, but 9-digit ZIP code matches.';
            case 'Z':
                return 'Street address does not match, but 5-digit ZIP code matches.';
            case 'N':
                return 'Street address, 5-digit ZIP code, and 9-digit ZIP code all do not match.';
            case 'U':
                return 'Address information unavailable. ' +
                'Returned if non-US. AVS is not available or if the AVS in a U.S. ' +
                'bank is not functioning properly.';
            case 'R':
                return "Retry - Issuer's System Unavailable or Timed Out.";
            case 'E':
                return 'AVS data is invalid.';
            case 'S':
                return 'U.S. issuing bank does not support AVS.';
            case 'D':
                return 'Street Address and Postal Code match for International Transaction.';
            case 'M':
                return 'Street Address and Postal Code match for International Transaction.';
            case 'B':
                return 'Street Address Match for International Transaction. ' +
                'Postal Code not verified due to incompatible formats.';
            case 'P':
                return 'Postal Codes match for International Transaction ' +
                'but street address not verified due to incompatible formats.';
            case 'C':
                return 'Street Address and Postal Code not verified ' +
                'for International Transaction due to incompatible formats.';
            case 'I':
                return 'Address Information not verified by International issuer.';
            case 'G':
                return 'Non-US. Issuer does not participate.';
            default:
                return null;
        }
    }
    
    private cvcDescriptionMapper(code: string | null) {
        if (!code) return null;
        switch (code) {
            case 'M':
                return 'Match';
            case 'pass':
                return 'Match';
            case 'N':
                return 'No Match';
            case 'fail':
                return 'No Match';
            case 'P':
                return 'Not Processed';
            case 'S':
                return 'The CVV was provided but the card-issuing bank '+
                    'does not participate in card verification';
            case 'U':
                return 'Issuer Not Certified';
            case 'X':
                return 'No response from the association';
            case 'A':
                return 'The CVV was provided but this type of transaction '+
                    'does not support card verification';
            case 'I':
                return 'No CVV was provided';
            case 'B':
                return 'CVV checks were skipped for this transaction';
            case 'D':
                return 'Transaction determined suspicious by issuing bank';
            case '1':
                return 'Card verification is not supported for this '+
                'processor or card type';
            case '2':
                return 'Unrecognized result code returned by the processor '+
                'for card verification response';
            case '3':
                return 'No result code was returned by the processor';
            default:
                return 'No CVV2/CVC data is available for the transaction';
        }
    }
}

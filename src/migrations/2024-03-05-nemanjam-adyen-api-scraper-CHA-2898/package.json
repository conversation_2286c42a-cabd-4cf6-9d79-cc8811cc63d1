{"name": "adyen-api-scraper", "version": "1.0.0", "description": "", "main": "main.ts", "scripts": {"start": "tsx main.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "devDependencies": {"@apify/tsconfig": "^0.1.0", "@types/node": "^20.0.0", "tsx": "^4.4.0", "typescript": "~5.3.0"}, "dependencies": {"@chargeflow-team/chargeflow-utils-sdk": "^1.0.106", "@chargeflow-team/data-access": "^1.0.73", "axios": "^1.9.0", "bottleneck": "^2.19.5", "date-fns": "^3.3.1"}}
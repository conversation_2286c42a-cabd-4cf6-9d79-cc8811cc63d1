import Configuration from "./configuration";
import AdyenScraperMapper from "./adyenScraperMapper";
import AdyenApiScraper from "./adyenApiScraper";
import {Script} from "./script";


const configuration: Configuration = {
    cookie: "activeAccount=Company.Scentbird; JSESSIONID=e8e80f0524a8b12a~34F95473CFC8519719B8082464D099CA.live1e;",
    shopUrlId: "S3B-SVw8TjNYf29WTHI4d0NCeSw9", //scentbird
    shopId: "62b357a17e9bd845fa1f8679",
    shopName: "nevership",
    chargeflowId: "beb2353d4bac9fe626853fd9",
    minDate: new Date("2024-01-01T00:00:00.000Z"), // last month
}

const mapper = new AdyenScraperMapper();
const scraper = new AdyenApiScraper(configuration, mapper);

new Script(scraper).main();

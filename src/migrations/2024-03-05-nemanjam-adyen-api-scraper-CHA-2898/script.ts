import {ScriptBase, ScriptContext} from "../../../shared/script-base";
import AdyenApiScraper from "./adyenApiScraper";
import {DataAccessUnitOfWork} from "@chargeflow-team/data-access";
import {IUnifiedDisputeObject} from "@chargeflow-team/data-access/dist/Dispute/Types";
import {MongoClient, ObjectId} from "mongodb";
import {MongoConfig} from "@chargeflow-team/chargeflow-utils-sdk";
import {RootObject} from "./scraped-types";
import { Source } from "@chargeflow-team/data-access/dist/shared/enums";

export class Script extends ScriptBase {

    constructor(private scraper: AdyenApiScraper) {
        super();
    }

    getAuthor(): string {
        return "Nemanja Malocic";
    }

    getName(): string {
        return "Scrape Adyen data from the user console";
    }

    getTicket(): string {
        return "CHA-2770";
    }

    async dryRun(context: ScriptContext) {
        await this.scraper.scrapeData(1, new Date(), (batchData, _raw) => this.dryRunPersist(batchData))
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.scraper.scrapeData(1, new Date(), (batchData, raw) => this.wetRunPersist(batchData, raw))
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        return Promise.resolve(undefined);
    }
    private async wetRunPersist(batchData: IUnifiedDisputeObject[], raw: RootObject[]): Promise<void> {
        if (batchData.length === 0) {
            return;
        }
        const mongoClient = await MongoClient.connect(process.env.MONGO_URI!);
        const dataAccess = new DataAccessUnitOfWork(mongoClient);
        const chargeflowId = this.scraper.configuration.chargeflowId;
        try {
            console.log(`Saving ${batchData.length} raw disputes to DB`);
            const bulkOps = raw.map((item) => ({
                updateOne: {
                    filter: {
                        "dispute.pspReference": item.dispute.pspReference,
                        chargeflowId: new ObjectId(chargeflowId)
                    },
                    update: {
                        $set: item
                    },
                    upsert: true
                }
            }));

            await mongoClient.db('chargeflow_api')
                .collection('adyenDisputesRawData')
                .bulkWrite(bulkOps);

            console.log(`Searching for ${batchData.length} existing disputes in DB`);

            const existingDisputes = await dataAccess.disputeRepository.getManyByCaseIdChargeflowIdProcessor(
                batchData.map((item) => item.dispute.id), 
                chargeflowId, Source.Adyen, undefined, 1, batchData.length);
            console.log(`Found ${existingDisputes.items.length} existing disputes in DB`);
            
            const newDisputes = batchData.filter((item) => !existingDisputes.items.some((existingItem) => existingItem.dispute.id === item.dispute.id));
            console.log(`Saving ${newDisputes.length} disputes to DB`);
            if (newDisputes.length > 0) {
                const addedDisputes = await dataAccess.disputeRepository.bulkAdd(newDisputes);
                console.log(`saved ${addedDisputes.length} disputes to DB successfully`);
            }
        } catch (dataAccessError) {
            console.log("Error committing transaction");
            console.log(dataAccessError);
        }


    }

    private async dryRunPersist(batchData: IUnifiedDisputeObject[]): Promise<void> {
        console.log(`Found ${batchData.length} items`);
        batchData.forEach((item: IUnifiedDisputeObject) => {
            console.log(`Pretty print dispute with ID ${item.dispute.id}`);
            console.log(JSON.stringify(item, null, 2));
        })
    }
}

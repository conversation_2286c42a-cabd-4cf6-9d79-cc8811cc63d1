# Purpose
This scripts load historical data from <PERSON>ye<PERSON>, using their web api
https://linear.app/chargeflow/issue/CHA-2416/create-scraper-for-adyen-to-load-past-disputes

## Moti<PERSON>
<PERSON><PERSON> asked if we can load all his disputes into our system, and we can't do it via normal reconciliation process.
We implemented api scraper to be able to do this

## Running this script
### Before running

Before running this script we need to setup ```Coniguration``` object in ```main.ts```
```json
const configuration: Configuration = {
    cookie: "activeAccount=Company.Scentbird; JSESSIONID=CEDE7B07D98A66A618FF1F5D6418790D.live1202e;",
    shopUrlId: "S3B-SVw8TjNYf29WTHI4d0NCeSw9", //scentbird
    shopId: "62b357a17e9bd845fa1f8679",
    shopName: "nevership",
    chargeflowId: "beb2353d4bac9fe626853fd9",
    minDate: new Date("2024-03-01T00:00:00.000Z"), // last month
}
```
* cookie - We need to login to clients portal and retrieve cookie, and paste it in our code.
* shopUrlId - Each shop has unique ID that is part of the url when making request. 
We hardcoded ID for scentbird but if other clients need this we should also read this from the website.
* shopId - This is shop id from our database that will connect dispute to a shop
* chargeflowId - ChargeflowId from our database that will connect dispute to chargeflowId
* shopName - This is Shop Name from our database that will be part of dispute object.


`npx ts-node ./main.ts <dry or wet-secondary or wet-primary> | tee log.txt`
<br/>NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console
wet-secondary - NOT IMPLEMENTED - does not update the source table, writes results to another collection (disputes_test), adding documents as needed
wet-primary - UPDATES THE DISPUTES TABLE. Use with extreme caution!

### How this script was tested
[x] Load data from the api and save to database

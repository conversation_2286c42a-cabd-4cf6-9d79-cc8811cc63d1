export interface RootObject {
    dispute: Dispute;
    disputeDetails: DisputeDetails;
}
interface DisputeDetails {
    pspDetails: PspDetails;
    paymentPspDetails: PaymentPspDetails;
}
interface PaymentPspDetails {
    addNote: AddNote;
    applicationInfo: ApplicationInfo;
    balancePlatformInfo: BalancePlatformInfo;
    cardHolderInfo: CardHolderInfo;
    enhancedScheme: EnhancedScheme;
    fraudControl: FraudControl;
    hasSignature: boolean;
    information: Information;
    merchantBean: MerchantBean;
    operationsInfo: OperationsInfo;
    paymentEvents: PaymentEvent[];
    paymentLifeCycle: DisputeHistory;
    paymentOverview: PaymentOverview;
    processingInfo: ProcessingInfo2;
    shopperInfo: ShopperInfo;
    threeDS: ThreeDS;
    transactionCost: TransactionCost;
}
interface TransactionCost {
    estimate: boolean;
    interchangeAmount: Amount;
    merchantBankAmount: Amount;
    merchantBankCost: string;
    schemeAmount: Amount;
    totalCostAmount: Amount;
}
interface ThreeDS {
    liabilityShift: boolean;
    noThreeDSReason: string;
    threeDSDataListDownload: ThreeDSDataListDownload;
    threeDSDynamicRuleTriggered: string;
}
interface ProcessingInfo2 {
    acquirerAvsResponseCode: string;
    acquirerCvcResponseCode: string;
    acquirerResponse: string;
    authorisationCode: string;
    avsResponseCode: number;
    cardVerificationCodeSupplied: boolean;
    cvcResponseCode: number;
    errorCvc: boolean;
    fraudScoring: FraudScoring;
    networkTokenPayment: boolean;
    recurringContract: string;
    recurringProcessingModel: string;
    retried: boolean;
    shopperInteraction: string;
}
interface PaymentEvent {
    accountKey: string;
    amount: Amount;
    arn?: string;
    creationDate: string;
    item: string;
    itemHasLink: boolean;
    merchantReference: string;
    noteHasLink: boolean;
    pspReference: string;
    status: string;
    txType: string;
    settlementBatch?: string;
    acquirerReference?: string;
    createdBy?: string;
}
interface FraudControl {
    availableReferrals: string[];
    description: string;
    displayUnlinkDetailsAction: boolean;
    pspReference: string;
}
interface EnhancedScheme {
    customerReference: string;
    totalTax: Amount;
}
interface BalancePlatformInfo {
    canViewBalanceAccounts: boolean;
    canViewTransfers: boolean;
    isBalancePlatformTx: boolean;
    isBalancePlatformUser: boolean;
}
interface ApplicationInfo {
    externalPlatformIntegrator: string;
    externalPlatformName: string;
    externalPlatformVersion: string;
}
interface PspDetails {
    addNote: AddNote;
    cardHolderInfo: CardHolderInfo;
    defenseReasons: DefenseReason[];
    disputeHistory: DisputeHistory;
    disputeInformation: DisputeInformation;
    information: Information;
    issuerComments: IssuerComments;
    merchantBean: MerchantBean;
    operationsInfo: OperationsInfo;
    paymentOverview: PaymentOverview;
    processingInfo: ProcessingInfo;
    shopperInfo: ShopperInfo;
}
interface ShopperInfo {
    creationDate: string;
    rechargePaymentDetails: RechargePaymentDetails;
    recurringContract: string;
    redacted: boolean;
    shopperBillingAddress: string;
    shopperBillingCountry: string;
    shopperBillingLookup: string;
    shopperCompleteLookup: string;
    shopperCountry: string;
    shopperCountryCode: string;
    shopperDNA: string;
    shopperEmail: string;
    shopperEmailLookup: string;
    shopperIp: string;
    shopperIpLookup: string;
    shopperPersonalDetails: ShopperPersonalDetails;
    shopperReference: string;
}
interface ShopperPersonalDetails {
    creationDate: string;
    firstName: string;
    genderCode: string;
    lastName: string;
    nameGoogleLookup: string;
    nameLinkedInLookup: string;
    sensitiveSSN: boolean;
}
interface RechargePaymentDetails {
    accountKey: MerchantAccountKey;
    pspReference: MerchantAccountKey;
    txTypeCode: MerchantAccountKey;
}
interface ProcessingInfo {
    acquirerAvsResponseCode: string;
    acquirerCvcResponseCode: string;
    acquirerResponse: string;
    authorisationCode: string;
    avsResponseCode: number;
    cardVerificationCodeSupplied: boolean;
    cvcResponseCode: number;
    errorCvc: boolean;
    fraudScoring: FraudScoring;
    liabilityShift: boolean;
    networkTokenPayment: boolean;
    noThreeDSReason: string;
    recurringContract: string;
    recurringProcessingModel: string;
    retried: boolean;
    shopperInteraction: string;
    threeDSDataListDownload: ThreeDSDataListDownload;
    threeDSDynamicRuleTriggered: string;
}
interface ThreeDSDataListDownload {
    accountKey: string;
    pspReference: string;
}
interface FraudScoring {
    accountKey: string;
    accountTotalScore: number;
    pspReference: string;
}
interface PaymentOverview {
    amount: Amount;
    pspReference: string;
    status: string;
}
interface OperationsInfo {
    activeLock: boolean;
    pspReference: MerchantAccountKey;
    txType: string;
}
interface MerchantBean {
    adyenInitiated: boolean;
    creationDate: string;
    merchantAccount: string;
    merchantAccountKey: MerchantAccountKey;
    merchantOrderReference: string;
    merchantReference: string;
    txDescription: string;
}
interface MerchantAccountKey {
    encoded: string;
    unencoded: string;
}
interface IssuerComments {
}
interface Information {
    acquirerAmount: Amount;
    creationDate: string;
    managePosTerminal: boolean;
    merchantIntegration: MerchantIntegration;
    numberOfInstallments: number;
    shopperPaid: Amount;
}
interface MerchantIntegration {
    integrationType: string;
    integrationVersion: string;
}
interface DisputeInformation {
    acquirerCode: string;
    amount: Amount;
    arn: string;
    autoDefended: boolean;
    creationDate: string;
    daysLeftToDefend: number;
    disputePspReference: string;
    disputeType: string;
    extraData: ExtraData;
    reason: string;
    reasonCode: string;
    status: string;
}
interface ExtraData {
    rapidDisputeResolution: string;
    ed: string;
}
interface DisputeHistory {
    lastStatus: string;
    statuses: Status[];
}
interface Status {
    auditUser?: string;
    date: string;
    journalType: string;
}
interface DefenseReason {
    defenseDocumentTypes: DefenseDocumentType[];
    defenseReasonCode: string;
    internalOnly: boolean;
    satisfied: boolean;
}
interface DefenseDocumentType {
    allowForDownload: boolean;
    available: boolean;
    clearingText: boolean;
    defenseDocumentTypeCode: string;
    generateDuringSubmission: boolean;
    merchantDeliverable: boolean;
    requirementLevel: string;
}
export interface CardHolderInfo {
    cardHolder: string;
    cardHolderFilterValue: string;
    cardType: CardType;
    changeConsumerName: boolean;
    coBrandedPaymentMethods: string[];
    consumerNameFilterValue: string;
    expiryDate: string;
    fundingSource: string;
    ibanFilterValue: string;
    issuerCountry: string;
    issuerCountryCode: string;
    issuerName: string;
    networkTokenPayment: boolean;
    originalCardSummary: string;
    cardNumber: string;
    paymentMethod: CardType;
    paymentToken: string;
    paymentTokenFilterValue: string;
    tokenTxVariants: string[];
    untokenisedCardSummary: string;
}
interface CardType {
    description: string;
    txVariantCode: string;
}
interface AddNote {
    enabled: boolean;
}
interface Dispute {
    accountKey: string;
    active: boolean;
    amount: Amount;
    chbReasonCode: string;
    chbSchemeCode: string;
    creationDate: string;
    daysLeftToDefend: number;
    defendable: boolean;
    description: string;
    disputeEndDate: string;
    disputeReason: string;
    merchantReference: string;
    paymentCreationDate: string;
    paymentMethod: string;
    paymentPspReference: string;
    pspReference: string;
    status: string;
}
interface Amount {
    currency: string;
    value: number;
}

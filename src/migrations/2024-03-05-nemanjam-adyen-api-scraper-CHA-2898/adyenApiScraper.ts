import Configuration from "./configuration";
import {compareAsc, parseISO} from "date-fns";
import axios from "axios";
import AdyenScraperMapper from "./adyenScraperMapper";
import {IUnifiedDisputeObject} from "@chargeflow-team/data-access/dist/Dispute/Types";
import {RootObject} from "./scraped-types";
import Bottleneck from "bottleneck";

type IPersistAction = (batchedData: IUnifiedDisputeObject[], raw: any) => Promise<void>

export default class AdyenApiScraper {
    private mapper: AdyenScraperMapper;
    private limiter: any;
    constructor(public readonly configuration: Configuration, mapper: AdyenScraperMapper) {
        this.mapper = mapper
        this.limiter = new Bottleneck({
            minTime: 50
        })
    }

    public async scrapeData(pageNumber = 1, currentDate: Date, method: IPersistAction) {
        const startDate = new Date(currentDate);
        startDate.setHours(0,0,0,0);
        let endDate = new Date(currentDate);
        endDate.setHours(23, 59,59,999);

        let end = compareAsc(currentDate, this.configuration.minDate)
        if (end === -1) {
            return;
        }

        try {
            let promiseList = [];
            let response = await this.getDisputes(pageNumber, startDate, endDate);
            const disputeItems = response.data.items;
            if (!disputeItems) {
                throw new Error("Invalid disputes page");
            }
            const limited = this.limiter.wrap(this.getDetailsAndMap).bind(this);
            for (const dispute of disputeItems) {
                try {
                    promiseList.push(limited(dispute));
                } catch (error) {
                    console.error("Error processing dispute item:", error);
                }
            }

            const batchedData = await Promise.all(promiseList);
            const mapped = batchedData.map( item => item.mapped);
            const raw = batchedData.map( item => item.raw);
            if(batchedData.length > 0) {
                await method(mapped, raw);
            }

            // don't ask why Adyen can't do more than 41 pages of pagination (not on their UI not via API)
            if (response.data.hasNext && pageNumber < 41) {
                await this.scrapeData(pageNumber + 1,currentDate, method);
            } else {
                currentDate.setDate(currentDate.getDate() - 1);
                await this.scrapeData(1, currentDate, method);
            }
        } catch (err) {
            console.log(err);
        }
    }

    private async getDetailsAndMap(dispute:any) {
        const [pspDetails, paymentPspDetails] =
            await Promise.all([this.getPsp(dispute.pspReference), this.getPaymentPsp(dispute.paymentPspReference)])

        const disputeDetails = {
            pspDetails: pspDetails.data,
            paymentPspDetails: paymentPspDetails.data
        }

        const disputeData: RootObject = {
            dispute,
            disputeDetails,
        };

        return {
            mapped: this.mapper.mapToUDO(disputeData, this.configuration.chargeflowId, this.configuration.shopId, this.configuration.shopName, false),
            raw: disputeData
        }
    }

    private async getDetails(dispute: any) {
        const [pspDetails, paymentPspDetails] =
            await Promise.all([this.getPsp(dispute.pspReference), this.getPaymentPsp(dispute.paymentPspReference)])

        return {
            pspDetails: pspDetails.data,
            paymentPspDetails: paymentPspDetails.data,
        };
    }

    private async getPsp(ref: string) {
        return axios.get(
            `https://ca-live.adyen.com/ca/ca/ui-api/disputes/v1/${this.configuration.shopUrlId}/details/${ref}`,
            { headers: { Cookie: this.configuration.cookie } }
        );
    }

    private  async getPaymentPsp(ref: string) {
        return axios.get(
            `https://ca-live.adyen.com/ca/ca/ui-api/payments/v1/account/${this.configuration.shopUrlId}/pspref/${ref}/details`,
            { headers: { Cookie: this.configuration.cookie } }
        );
    }

    private async getDisputes(pageNumber = 1, startDate: Date, endDate: Date) {
        console.log(`Fetching data for: ${startDate} current page: ${pageNumber}`);
        const data = {
            disputeReasons: null,
            accountId: 0,
            limit: 50,
            pageNumber,
            orderDirection: "desc",
            orderBy: "paymentCreationDate",
            filter1Value: null,
            filter1Field: null,
            disputeExpirationStartDate: startDate,
            disputeExpirationEndDate: endDate,
            pspRefFilter: "",
            paymentPspRefFilter: "",
            showDisputes: true,
            showNotificationOfFraud: false,
            showRequestForInformation: false,
            showUploadChargebacks: false,
            showUploadNotificationsOfFraud: false,
        }
        return axios.post(
            `https://ca-live.adyen.com/ca/ca/ui-api/disputes/v1/${this.configuration.shopUrlId}/disputes`,
            data,
            {
                headers: {
                    Cookie: this.configuration.cookie,
                },
            }
        );
    }
}

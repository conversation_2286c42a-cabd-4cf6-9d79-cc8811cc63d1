import dotenv from "dotenv";
import { <PERSON>riptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { Db, MongoClient, ObjectId } from "mongodb";
import { DataAccessUnitOfWork } from "@chargeflow-team/data-access";
dotenv.config();

const AGG_QUERY = [
  {
    $group: {
      _id: "$accountId",
      count: {
        $sum: 1
      },
      docs: {
        $push: "$$ROOT"
      }
    }
  },
  {
    $match: {
      count: {
        $gt: 1
      }
    }
  },
  {
    $unwind: "$docs"
  },
  {
    $replaceRoot: {
      newRoot: "$docs"
    }
  },
  {
    $project:
      /**
       * specifications: The fields to
       *   include or exclude.
       */
      {
        _id: 1,
        name: 1,
        chargeflow_id: 1,
        accountId: 1,
        email: 1,
        customer_id: 1,

      }
  },
  {
    $lookup:
      /**
       * from: The target collection.
       * localField: The local join field.
       * foreignField: The target join field.
       * as: The name for the results.
       * pipeline: Optional pipeline to run on the foreign collection.
       * let: Optional variables to use in the pipeline field stages.
       */
      {
        from: "account",
        localField: "chargeflow_id",
        foreignField: "legacy.chargeflowId",
        as: "account"
      }
  },
  {
    $unwind:
      /**
       * path: Path to the array field.
       * includeArrayIndex: Optional name for index.
       * preserveNullAndEmptyArrays: Optional
       *   toggle to unwind null and empty values.
       */
      {
        path: "$account",
        preserveNullAndEmptyArrays: true
      }
  },
  {
    $match:
      /**
       * query: The query in MQL.
       */
      {
        account: {
          $exists: false
        },
        chargeflow_id: new ObjectId('6797673f4aa80c5bc00ead1f')
      }
  }
];

const defaultCfId = 'chargeflowId';

const CHARGEFLOW_ID_MAPPING: Record<string, string> = {
  'pastCharges': defaultCfId,
  'billings': defaultCfId,
  'pendingCharges': defaultCfId,
  'seon': defaultCfId,
  'inquiryAutomation': defaultCfId,
  'businessUnits': defaultCfId,
  'afterpayDisputesRawData': defaultCfId,
  'disputes': defaultCfId,
  'shopTemplates': 'chargeflow_id',
  'trashedDisputes': defaultCfId,
  'merchantProfiles': defaultCfId,
  'shopifyShopsGateways': defaultCfId,
  'charges': defaultCfId,
  'actions': defaultCfId,
  'shops': 'chargeflow_id',
  // 'notificationPreferences',
  'fraudAnalysis': defaultCfId,
  'alerts': defaultCfId,
  'sessions': defaultCfId,
  'reconciliation-scheduler': defaultCfId,
  'asanaMappedApprovedData': defaultCfId,
  'afterpayTransactionsRawData': defaultCfId,
  'processors': 'chargeflow_id',
  'chargeflow_ids': '_id',
  'alertsRegistry': defaultCfId,
  // 'alert-linking-reports',
};

export class Script extends ScriptBase {

  public db!: Db;
  public getName(): string {
    return "Create missing processor for stripe users";
  }

  public getAuthor(): string {
    return "Riza Jumola";
  }

  public getTicket(): string {
    return "CHA-15017";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow([
        "MONGO_URI",
        "MONGO_DB_NAME",
      ]);

      const client = await loadMongoClient();
      const mongo = client.db(process.env.MONGO_DB_NAME);
      console.log("Mongo client loaded");

      const SHOPS = mongo.collection('shops');

      const shopsWithWrongAccountId = await SHOPS.aggregate(AGG_QUERY).toArray();
      console.log("Found %d documents", shopsWithWrongAccountId.length);

      if (!shopsWithWrongAccountId.length) return console.log("No documents found");

      // const collectionsWithAccountId = await findCollectionsWithAccountId(mongo);
      // console.log("Collections with accountId: ", collectionsWithAccountId);

      for (const shop of shopsWithWrongAccountId) {
        const { _id, name, accountId, chargeflow_id, customer_id } = shop;
        console.log("Shop: ", shop);

        await logDocsCountWithSameCfIdWrongAccountId(mongo, accountId, chargeflow_id);

        await accountNotExistsOrThrow(client, chargeflow_id);

        console.log('Account with chargeflow id does not exist, ok to create new account');

        if (this.mode === 'wet-primary') {
          const params = {
            email: shop.email,
            chargeflowId: chargeflow_id,
            shopId: _id,
            customerId: customer_id,
          };
          console.log('Creating new account with params: ', params);
          const newAccount = await createNewAccount(client, params);

          await changeCollectionAccountId(mongo, 'shops', accountId, newAccount._id, chargeflow_id);
        }

        console.log('Finished checking shop %s %s', _id, name);
      }
    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }
}

const script = new Script();
script.main();

async function changeCollectionAccountId(
  mongo: Db,
  collectionName: string,
  oldAccountId: ObjectId,
  newAccountId: ObjectId,
  chargeflowId: ObjectId
) {

  const collection = mongo.collection(collectionName);

  const result = await collection.updateMany(
    {
      accountId: oldAccountId,
      [CHARGEFLOW_ID_MAPPING[collectionName]]: chargeflowId,
    },
    { $set: { accountId: newAccountId } }
  );

  console.log(`Updated ${result.modifiedCount} documents in ${collectionName}`);
}

async function accountNotExistsOrThrow(client: MongoClient, chargeflowId: ObjectId) {
  const dataAccess = new DataAccessUnitOfWork(client);
  const account = await dataAccess.accountRepository.findOneByLegacyChargeflowId(chargeflowId);

  if (account) throw new Error(`Account with chargeflowId ${chargeflowId} not found`);
}

async function createNewAccount(client: MongoClient, params: {
  email: string;
  chargeflowId: ObjectId;
  shopId: ObjectId;
  customerId: ObjectId;
}) {
  const dataAccess = new DataAccessUnitOfWork(client);
  return await dataAccess.accountRepository.add({
      email: params.email,
      legacy: {
          chargeflowId: params.chargeflowId,
          shopId: params.shopId,
          customerId: params.customerId,
      },
  });
}

async function logDocsCountWithSameCfIdWrongAccountId(mongo: Db, accountId: ObjectId, chargeflow_id: ObjectId) {
  const collectionNames = Object.keys(CHARGEFLOW_ID_MAPPING);
  for (const collectionName of collectionNames) {
    const collection = mongo.collection(collectionName);

    const count = await collection.countDocuments({
        accountId: accountId,
        [CHARGEFLOW_ID_MAPPING[collectionName]]: chargeflow_id,
    });

    if (!count) continue;

    console.log("Collection %s has %d documents with chargeflow id %s accountId %s", collectionName, count, chargeflow_id, accountId);
  }
}

async function findCollectionsWithAccountId(db: any) {
  try {
    const collections = await db.listCollections().toArray();
    const collectionsWithAccountId: string[] = [];

    const IGNORES = [
      'tokens',
      'order-link-shadow',
      'stripeDisputesRawData', 'alertLinkingReports20112024Copy', 'system.views', 'reportOrders', 'processors-with-shopify-test-v1', 'accountMappings', 'disputes_copy'];

    const filteredCollections = collections.filter(
      ({ name, type }: any) => !IGNORES.includes(name) && type !== 'view'
    );

    for (const { name } of filteredCollections) {
      const latestDoc = await db.collection(name)
        .find({})
        .sort({ _id: -1 })
        .limit(1)
        .toArray();

      if (latestDoc?.[0]?.accountId) {
        console.log(`Collection ${name} has accountId`);
        collectionsWithAccountId.push(name);
      }
    }

    return collectionsWithAccountId;
  } catch (err) {
    console.error('Error:', err);
    throw err;
  }
}

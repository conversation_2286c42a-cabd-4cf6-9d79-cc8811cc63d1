## Deleting duplicate dispute.disputeStatus and dispute.rawData.webhooks docs

A recent bug found introduced an loop in handling dispute statuses and "webhook" events in stripe-webhooks from stripe-reconciliation.

The bug is already being fixed.

This script aims to clean up all duplicate documents that the bug caused.

- It first finds disputes with unusual number of disputeStatuses
- Then prepares a bulkWrite operation which uses `$reduce` to remove the duplciates by `status` value in both the `disputeStatuses` and `webhooks` object arrays.

### How to use

Create a `.env` file and add the following keys

```
MONGO_URI_SECONDARY=
MONGO_URI_PRIMARY=
MONGO_URI_TEST=
```

Commands:

```
ts-node utility-scripts/2024-02-26-ritzj-disputes-dupe-status-CHA-2820/index.ts dry
ts-node utility-scripts/2024-02-26-ritzj-disputes-dupe-status-CHA-2820/index.ts wet-secondary
ts-node utility-scripts/2024-02-26-ritzj-disputes-dupe-status-CHA-2820/index.ts wet-primary
```
import { AnyBulkWriteOperation, BulkWriteResult, ObjectId } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext, ScriptMode } from '../shared/script-base';
import dotenv from 'dotenv';
dotenv.config();

const findStripeDisputesAggQuery: any[] = [
  {
    $match: {
      'dispute.source': 'stripe',
    },
  },
  {
    $project: {
      _id: 1,
      statusesCount: {
        $size: '$dispute.disputeStatuses',
      },
    },
  },
  {
    $match: {
      statusesCount: {
        $gt: 100,
      },
    },
  },
];

const bulkWriteOpQuery = (disputeId: ObjectId) => ({
  updateOne: {
    filter: { _id: new ObjectId(disputeId) },
    update: [
      {
        $set: {
          'dispute.disputeStatuses': {
            $reduce: {
              input: '$dispute.disputeStatuses',
              initialValue: [],
              in: {
                $let: {
                  vars: {
                    last: {
                      $arrayElemAt: [
                        {
                          $filter: {
                            input: '$$value',
                            cond: { $eq: ['$$this.status', '$$this.status'] },
                          },
                        },
                        -1,
                      ],
                    },
                  },
                  in: {
                    $cond: [
                      { $eq: ['$$last.status', '$$this.status'] },
                      '$$value',
                      { $concatArrays: ['$$value', ['$$this']] },
                    ],
                  },
                },
              },
            },
          },
          'dispute.rawData.webhooks': {
            $reduce: {
              input: '$dispute.rawData.webhooks',
              initialValue: [],
              in: {
                $let: {
                  vars: {
                    lastWebhook: {
                      $arrayElemAt: [
                        {
                          $filter: {
                            input: '$$value',
                            cond: {
                              $eq: [
                                '$$this.data.object.status',
                                '$$this.data.object.status',
                              ],
                            },
                          },
                        },
                        -1,
                      ],
                    },
                  },
                  in: {
                    $cond: [
                      {
                        $eq: [
                          '$$lastWebhook.data.object.status',
                          '$$this.data.object.status',
                        ],
                      },
                      '$$value',
                      { $concatArrays: ['$$value', ['$$this']] },
                    ],
                  },
                },
              },
            },
          },
        },
      },
    ],
  },
});

interface StripeDisputeWithDupeStatus {
  _id: ObjectId;
  statusesCount: number;
}

class Script extends ScriptBase {
  getName = () =>
    'Deletes duplicate dispute.disputeStatuses from disputes collection';

  getAuthor = () => 'Ritz Jumola';

  getTicket = () => 'CHA-2820';

  async dryRun(ctx: ScriptContext) {
    console.log('Running in dry run (not changing anything)', ctx);
    await this.logic(ctx);
  }

  async wetRunOnSecondary(ctx: ScriptContext): Promise<void> {
    await this.logic(ctx);
  }

  async wetRunOnPrimary(ctx: ScriptContext): Promise<void> {
    await this.logic(ctx);
  }

  async logic(ctx: ScriptContext) {
    const mode = (ctx.args[0] ?? 'dry') as ScriptMode;

    const client = await loadMongoClient();
    const session = client.startSession();

    try {
      const disputes = (await client
        .db('chargeflow_api')
        .collection('disputes')
        .aggregate(findStripeDisputesAggQuery)
        .toArray()) as StripeDisputeWithDupeStatus[];

      console.log('Total disputes:', disputes.length);
      const ops: AnyBulkWriteOperation<Document>[] = disputes.map(dispute =>
        bulkWriteOpQuery(dispute._id),
      );

      // chunk ops by 10k
      const chunkedOps: AnyBulkWriteOperation<Document>[][] = [];
      const chunkSize = 10000;

      for (let i = 0; i < ops.length; i += chunkSize) {
        chunkedOps.push(ops.slice(i, i + chunkSize));
      }

      console.log('Total chunks:', chunkedOps.length);

      if (mode === 'dry') {
        console.log('Dry run, not changing anything');
        console.log('Total ops:', ops.length);
        console.log('sample ops: %j', ops[0]);
      } else {
        console.log('Wet run, changing data');
        const results: BulkWriteResult[] = [];
        for (const chunk of chunkedOps) {
          console.log('Processing chunk...');
          console.log('Total ops:', chunk.length);
          const res = await client
            .db('chargeflow_api')
            .collection('disputes')
            .bulkWrite(chunk as any, { session });
          results.push(res);
        }

        await session.commitTransaction();
        console.log('Results:', results);
      }
    } catch (er) {
      console.error(er);
      await session.abortTransaction();
    } finally {
      await session.endSession();
      client.close();
    }
  }
}

const script = new Script();
script.main();

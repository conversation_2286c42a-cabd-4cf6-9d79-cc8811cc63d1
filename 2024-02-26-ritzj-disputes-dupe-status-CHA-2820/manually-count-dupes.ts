// This file is only used to validate the mongo aggregation query and the bulk write operation query. 
// Not part of the actual script.
// This script determines how many unique statuses are present in the disputeStatuses array and the webhooks array.
// Which can then be used to determine if the bulk write operation query is working as expected.
import json from './sample-data/chargeflow_api.disputes-test-64be3f4fda792bd082729eaf.json';
import lodash from 'lodash';

(() => {
    const data = json[0] as any;
    const uniqueDisputeStatuses = lodash.uniq((data.dispute.disputeStatuses).map((d: any) => d.status));
    const uniqueWebhookStatuses = lodash.uniq((data.dispute.rawData.webhooks).map((d: any) => d.data.object.status));
    console.log('statuses', uniqueDisputeStatuses);
    console.log('uniqueWebhookStatuses', uniqueWebhookStatuses);
})();

import { ScriptBase, ScriptContext } from "../shared/script-base";
import { ResolvedLogic } from "./logic";

class Script extends ScriptBase {
    public getName(): string {
        return "Add populate mising resolvedDate to disputes";
    }
    public getAuthor(): string {
        return "Liza Bogorad";
    }
    public getTicket(): string {
        return "CHA-3496";
    }
    async dryRun(context: ScriptContext) {
        const Logic = new ResolvedLogic();
        const isDryRun = true;
        await Logic.executeRun(context, isDryRun);
    }

    async wetRunOnSecondary(context: ScriptContext) {
      throw new Error('Method not implemented.');
    }
    async wetRunOnPrimary(context: ScriptContext) {
        const Logic = new ResolvedLogic();
        const isDryRun = false;
        await Logic.executeRun(context, isDryRun);
    }


}


const script = new Script();
script.main();
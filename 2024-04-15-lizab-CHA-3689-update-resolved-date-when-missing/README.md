# Purpose
Currently a large amount of disputes in our system are missing resolved dates, those disputes were resolved before entering our system (Through our polling flows), The purpose of this script is to populate those disputes with missing dates.


## Motivation:
We use resolved dates for different analytics and currently more then 500k are missing the dates

## Running this script
`npx ts-node ./script.ts <dry or wet-primary> <processorName> <batchSize:optional> 2>&1 | tee log.txt` - batch size default
Example use: `npx ts-node ./script.ts wet-primary stripe 20000 2>&1 | tee log.txt`
(currently supported: stripe, shopify, klarna, paypal)
NOTE: a `.env` file is expected to be present in CWD   (only needs mongouri)
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes mapped update opreations to the console  (without actually applying them)
wet-primary - Updates MerchantActions on dispute collection

### How this script was tested
[x] dry run on dev env

[x] wet run on dev env
    [x] ensure all resolved dates were populated
    [x] ensure values are actual dates 
    [x] ensure existing resolvedDates weren't effected
    [x] ensure an indication if date is estimated is added 

[x] dry run on prod env

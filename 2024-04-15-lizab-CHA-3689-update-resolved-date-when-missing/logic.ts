import { ObjectId, MongoClient } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";

const STRIPE_AVG_LOST_RESOLVED_MS = 31.1 * 24 * 60 * 60 * 1000; // Convert days to milliseconds
const STRIPE_AVG_WON_RESOLVED_MS = 55.8 * 24 * 60 * 60 * 1000; // Convert days to milliseconds
const SHOPIFY_AVG__LOST_RESOLVED_MS = 47.6 * 24 * 60 * 60 * 1000; // Convert days to milliseconds
const SHOPIFY_AVG_WON_RESOLVED_MS  =67.7 * 24 * 60 * 60 * 1000; // Convert days to milliseconds
const supportedProcessors = ['stripe', 'paypal', 'shopify', 'klarna'];
const projection = {
    '_id': 1,
    'dispute': 1,
    'chargeflow.resolved': 1,
};

interface DisputeResult {
    _id: string;
    chargeflow: {
        resolved: {
            isResolved: boolean;
            isWon: boolean;
            resolvedDate?: Date;
        };
    };
    dispute: {
        dateCreated: Date;
        closedDate?: Date;
        rawData: { dispute: { balance_transaction: { available_on: number } } };
    };
    lastDisputeStatus: { status: string };
}

interface ProcessorLogic {
    mapUpdateOp(result: DisputeResult): any;
}

function getOperationMapper(processorName: string): ProcessorLogic {
    switch (processorName) {
        case 'stripe': return new StripeLogic();
        case 'paypal': return new PaypalLogic();
        case 'shopify': return new ShopifyLogic();
        case 'klarna': return new KlarnaLogic();
        default: throw new Error(`Processor ${processorName} is not supported`);
    }
}

export class ResolvedLogic {
    private filter?: object;

    private getFilter(processorName: string): object {
        return {
            'dispute.processor': processorName,
            'chargeflow.resolved.isResolved': true,
            'chargeflow.resolved.resolvedDate': null
        };
    }

    async executeRun(context: any, isDryRun: boolean): Promise<void> {
        const client = await loadMongoClient();
        const processorName = this.validateProcessorName(context.args[1]);
        const BATCH_SIZE = Number(context.args[2]) || 10000; // Default batch size
        const operationMapper = getOperationMapper(processorName).mapUpdateOp;
        this.filter = this.getFilter(processorName);

        let totalOperations = 0;
        let batchNumber = 0;

        while (true) {
            const operations = await this.getUpdateOperations(client, operationMapper, batchNumber, BATCH_SIZE);
            console.log(`Batch ${batchNumber + 1} - Operations:`, operations.length);
            if (operations.length === 0) {
                break; // Exit the loop     
            }

            if (!isDryRun) {
                const results = await this.processBatch(client, operations);
                console.log(results)
                totalOperations += results.modifiedCount;
            }

            console.log(`Batch ${batchNumber + 1} - ${isDryRun ? 'Prepared' : 'Modified'} operations:`, operations.length);
            batchNumber++;
        }

        console.log(`Total ${isDryRun ? 'prepared' : 'modified'} operations:`, totalOperations);
    }

    private async processBatch(client: MongoClient, updates: any[]): Promise<any> {
        if (!updates.length) return { modifiedCount: 0 };

        const dbName = 'chargeflow_api';
        return client.db(dbName).collection('disputes')
            .bulkWrite(updates)
            .catch(err => {
                console.log('Error during bulk write:', err);
                throw err;
            });
    }

    
private async getUpdateOperations(client: MongoClient, operationMapper: (result: DisputeResult) => any, batchNumber: number, BATCH_SIZE: number): Promise<any[]> {
    return client
        .db("chargeflow_api")
        .collection("disputes")
        .find(this.filter as object)
        .limit(BATCH_SIZE)
        .skip(BATCH_SIZE * batchNumber)
        .project(projection)
        .toArray()
        .then(results => results.filter(isDisputeResult).map(operationMapper).filter(Boolean));
}

    private validateProcessorName(processorName: string): string {
        if (!processorName) {
            throw new Error('Processor name is required');
        }
        if (!supportedProcessors.includes(processorName)) {
            throw new Error(`Processor ${processorName} is not supported`);
        }
        return processorName;
    }
}

// Specific processor classes
class BaseProcessorLogic implements ProcessorLogic {
    mapUpdateOp = (result: DisputeResult) => {
        const resolvedDate = result.dispute?.closedDate;
        if (!resolvedDate) {
            console.warn(`Could not determine resolved date for dispute ${result._id}`);
            return;
        }
        return {
            updateOne: {
                filter: { _id: new ObjectId(result._id) },
                update: {
                    $set: {
                        'chargeflow.resolved.resolvedDate': new Date(resolvedDate),
                        'chargeflow.resolved.resolvedDateSource': 'closedDate',
                    }
                }
            }
        };
    }
}

class StripeLogic extends BaseProcessorLogic {
    mapUpdateOp = (result: DisputeResult) => {
        const isWon = result.chargeflow?.resolved?.isWon;
        const resolvedDate = isWon ? this.getWonDate(result) : this.estimateResolvedDate(result, isWon);
        const estimatedWonDate = (isWon && !resolvedDate) && this.estimateResolvedDate(result, isWon);
        const isEstimated = estimatedWonDate || !isWon;
        const finalDate = resolvedDate || estimatedWonDate;
        if (!finalDate) {
            console.log(`Could not determine resolved date for dispute ${result._id}`);
            return;
        }

        return {
            updateOne: {
                filter: { _id: new ObjectId(result._id) },
                update: {
                    $set: {
                        'chargeflow.resolved.resolvedDate': new Date(finalDate),
                        'chargeflow.resolved.resolvedDateSource': isEstimated ?  'estimated' : 'balance_transaction' 
                    }
                }
            } 
        };
    }

    private estimateResolvedDate = (result: DisputeResult, isWon: boolean): Date => {
        const dateCreated = new Date(result.dispute.dateCreated!);
        return new Date(dateCreated.getTime() + (isWon ? STRIPE_AVG_LOST_RESOLVED_MS : STRIPE_AVG_WON_RESOLVED_MS));
    }

    private getWonDate = (result: DisputeResult): Date | undefined => {
        if(!result?.dispute?.rawData?.dispute?.balance_transaction) {
            console.log('Could not determine resolved date for dispute %j,', result.dispute.rawData.dispute);
            return;
        }
        return new Date(result.dispute.rawData.dispute.balance_transaction.available_on * 1000);
    }
}

class PaypalLogic extends BaseProcessorLogic {}
class ShopifyLogic extends BaseProcessorLogic {
    mapUpdateOp = (result: DisputeResult) => {
        const isWon = result.chargeflow.resolved.isWon;
        const resolvedDate = result.dispute?.closedDate || this.estimateResolvedDate(result, isWon);
        if (!resolvedDate) {
            console.warn(`Could not determine resolved date for dispute ${result._id}`);
            return;
        }
        const isEstimated =!result.dispute?.closedDate 
        const resolvedDateValue =  new Date(resolvedDate);
        return {
            updateOne: {
                filter: { _id: new ObjectId(result._id) },
                update: {
                    $set: {
                        'chargeflow.resolved.resolvedDate': resolvedDateValue,
                        'chargeflow.resolved.resolvedDateSource': isEstimated ? 'estimated' : 'closedDate',
                    }
                }
            }
        };
    }
    estimateResolvedDate = (result: DisputeResult, isWon:boolean): Date => {
        const dateCreated = new Date(result.dispute.dateCreated);
        return new Date(dateCreated.getTime() + (isWon ? SHOPIFY_AVG_WON_RESOLVED_MS : SHOPIFY_AVG__LOST_RESOLVED_MS)); 
    }
}
class KlarnaLogic extends BaseProcessorLogic {}

function isDisputeResult(doc: any): doc is DisputeResult {
    return 'chargeflow' in doc && 'dispute' in doc && '_id' in doc;
}
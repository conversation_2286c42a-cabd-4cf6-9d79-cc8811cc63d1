# utility-scripts
This is a collection of utility scripts, for example import, data migration, etc.

## Guidelines
1. Create a folder based on the following naming convention: [year]-[month]-[day]-[username]-[short-description]-CHA-[ticket]
2. Implement a class that inherits from ScriptBase (see 2024-02-21-alonw-disputes-evidenceUploadCategories-CHA-2836/add-evidence-upload-categories.ts for example)
3. Create a README.md file (see 2024-02-21-alonw-disputes-evidenceUploadCategories-CHA-2836/README.md) that includes the following sections:
    * Purpose (what this script is for)
    * Motivation (why this script is needed)
    * Running this script (how to run the script, what's needed, what does each run mode do)
4. Have your code reviewed.
5. Write readable, maintainable code
6. When ready to run on prod, take extra measures to ensure that load will not be high. Work in batches if needed. Add indexes to speed things up.
7. Store the log file for reference. We'll update this guidelines file when we decide on the specifics.s
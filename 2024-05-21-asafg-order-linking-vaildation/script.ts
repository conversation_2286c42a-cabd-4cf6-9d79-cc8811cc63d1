import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { Double, Filter, MongoClient } from 'mongodb';
import { Document, ObjectId } from 'bson';
import { groupBy } from 'lodash';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import {extractDate, getLinkedDisputesQuery} from '../shared/order-linking-validation/shared-order-linking-functions';

dotenv.config();

type timeUnit = "h" | "d" | "w" | "m" | "y" | undefined;
type validationResult = { found: boolean, processor: string };
type resultGroupEntry = [string, validationResult[]];
type tableEntry = { processor: string, totalLength: number, validLength: number, matchScore: number };
const hourLength = 3600 * 1000;
const timeUnitMap = {
    "h": hourLength,
    "d": hourLength * 24,
    "w": hourLength * 24 * 7,
    "m": hourLength * 24 * 30,
    "y": hourLength * 24 * 365
};
const dbName = 'chargeflow_api';
const disputesCollection = 'disputes';
const orderLinksCollection = 'order-link-shadow';

const createProjectionObject = (keys: string[]) => {
    const projectionObject: any = {};
    keys.forEach((key) => {
        projectionObject[key] = 1;
    });
    return { projection: projectionObject };
}

const getDisputesQuery = async (dateCreatedFrom: Date, dateCreatedTo: Date, processorName: string, chargeflowId: string) => {
    const relevantResultKeys = ["order.id", "chargeflowId", "dateCreated", "dispute.processor"];
    const projectionObject = createProjectionObject(relevantResultKeys);
    const query: Filter<Document> = await getLinkedDisputesQuery(dateCreatedFrom, dateCreatedTo, processorName);
    if (chargeflowId) {
        query['chargeflowId'] = new ObjectId(chargeflowId);
    }
    return {query, projectionObject};
}


const getDisputesByDateRange = async (mongoClient: MongoClient, dateCreatedFrom: Date, dateCreatedTo: Date, processorName: string, chargeflowId: string) => {
    const disputes = mongoClient.db(dbName).collection(disputesCollection);
    const { query, projectionObject } = await getDisputesQuery(dateCreatedFrom, dateCreatedTo, processorName, chargeflowId);
    const result = await disputes.find(query, projectionObject).toArray();
    return result;
};

const getOrderByDisputeId = async (mongoClient: MongoClient, disputeId: string, orderId: Double) => {
    const orderLinks = mongoClient.db(dbName).collection(orderLinksCollection);
    const relevantResultKeys = ["disputeId", "processorName", "shopifyOrderId"];
    const projectionObject = createProjectionObject(relevantResultKeys);
    const result = await orderLinks.find({
        'disputeId': new ObjectId(disputeId),
        'shopifyOrderId': orderId
    }, projectionObject)
        .toArray();
    return result;
};

const validateOrderLink = async (mongoClient: MongoClient, dispute: Document): Promise<validationResult> => {
    const orderLinks = await getOrderByDisputeId(mongoClient, dispute._id.toString(), dispute.order.id);
    const result = orderLinks.length > 0;
    if (!result) {
        console.log("No order link found for dispute:", dispute._id.toString());
    }
    return {
        found: result,
        processor: dispute.dispute.processor
    };
};

const calulateStartDate = (interval: number, unit: timeUnit = "d") => {
    const now = new Date();
    const start = now.getTime() - (interval * timeUnitMap[unit]);
    return new Date(start);
}

const getResultGroupData = (resultGroupEntry: resultGroupEntry): tableEntry => {
    const resultGroupkey = resultGroupEntry[0];
    const resultGroupData: validationResult[] = resultGroupEntry[1];
    const totalLength = resultGroupData.length;
    const validLength = resultGroupData.filter((item: validationResult) => item.found).length;
    const matchScore = (validLength / totalLength) * 100;
    return {
        processor: resultGroupkey,
        totalLength: totalLength,
        validLength: validLength,
        matchScore: matchScore
    }
}

function buildResultObject(disputesValidationResult: validationResult[], disputes: Document[]) {
    const resultGroups: Record<string, validationResult[]> = groupBy(disputesValidationResult, (item: validationResult) => item.processor);
    const result: tableEntry[] = Object.entries(resultGroups).map(group => getResultGroupData(group));
    const totalValidDisputes = disputesValidationResult.filter(item => item.found).length;
    result.push({
        processor: "Total",
        totalLength: disputes.length,
        validLength: totalValidDisputes,
        matchScore: (totalValidDisputes / disputes.length) * 100
    });
    return result;
}

const compareOrderLinks = async (dateCreatedFrom: Date, dateCreatedTo: Date, processorName: string = "", chargeflowId: string = "") => {
    try {
        const mongoClient: MongoClient = await loadMongoClient();
        const disputes = await getDisputesByDateRange(mongoClient, dateCreatedFrom, dateCreatedTo, processorName, chargeflowId);
        const validationPromises = disputes.map((item) => validateOrderLink(mongoClient, item));
        const disputesValidationResult = await Promise.all(validationPromises);
        const result: tableEntry[] = buildResultObject(disputesValidationResult, disputes);
        console.table(result);
    } catch (error) {
        console.error(error);
    }
};

export class ScriptOrderLinkingValidation extends ScriptBase {
    public getName(): string {
        return 'Order linking validation';
    }

    public getAuthor(): string {
        return 'Asaf Gamliel';
    }

    public getTicket(): string {
        return 'CHA-1382';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {
        const dateCreatedFrom = await extractDate(ctx.args[1]);
        const dateCreatedTo = await extractDate(ctx.args[2]);
        const processorName = ctx.args[3];
        const chargeflowId = ctx.args[4];

        const handleDryRun = async () => {
            await compareOrderLinks(dateCreatedFrom, dateCreatedTo, processorName, chargeflowId);
        };
        const handleWetRun = async () => {
            await compareOrderLinks(dateCreatedFrom, dateCreatedTo, processorName, chargeflowId);
        };
        try {
            if (isDryRun) {
                await handleDryRun();
            } else {
                await handleWetRun();
            }
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        }
    }
}

const script = new ScriptOrderLinkingValidation();
script.main();

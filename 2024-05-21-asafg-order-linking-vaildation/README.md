# Purpose:
Check how accurate is our linking for dispute orders

# Motivation:
Recently we created a new shopify linking service to link orders to disptues, we want to verify we are not causing regressions before saving orders to production disputes. This tool will help us validate we are linking the right orders.

# Running this script
`npx ts-node ./script.ts <dry or wet-primary> <dateCreatedFrom> <dateCreatedFrom> [<chargeflowId>]`

`dateCreatedFrom` and `dateCreatedTo` define the date range from which the disputes will be fetched. The date format
should be `YYYY-MM-DDTHH:mm:ssZ` (for instance: `2024-09-30T10:30:00Z`)

`chargeflowId` is an optional parameter to filter disputes by chargeflowId.

NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**.

Examples:
`~ npx ts-node script.ts dry 2024-09-30T00:00:00Z 2024-09-30T10:30:00Z`
`~ npx ts-node script.ts dry 2024-09-30T00:00:00Z 2024-09-30T10:30:00Z 66b08aec3384bd7fc94f8c11`

![Run without filter](run-without-filter.png)

`~ npx ts-node script.ts dry 5 m 640b2189b2f7bd478dd6450e`

![Run with filter](run-with-filter.png)


### How this script was tested
[x] Dry run against dev (There is no need for wet run) 

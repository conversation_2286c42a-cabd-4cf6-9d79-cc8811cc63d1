import {MongoClient} from "mongodb";
import {loadMongoClient} from "../shared/mongo-config";
import {ScriptBase} from "../shared/script-base";
import {ObjectId} from 'mongodb';
import {SQSClient, SendMessageCommand} from '@aws-sdk/client-sqs';
import {IMongoUnifiedDisputeObject} from "@chargeflow-team/data-access/dist/Dispute/Types";

export class ResendOrderLinking extends ScriptBase {
    public getName(): string {
        return "Resend disputes to PayPalOrderLinkingQueue";
    }

    public getAuthor(): string {
        return "Mateusz Smagiel";
    }

    public getTicket(): string {
        return "cha-13658-prepare-and-run-order-linking-for-paypal-disputes";
    }

    async dryRun(): Promise<void> {
        await this.logic(true);
    }

    async wetRunOnPrimary(): Promise<void> {
        await this.logic(false);
    }

    async wetRunOnSecondary(): Promise<void> {
        throw new Error("Method is not implemented.");
    }

    async logic(dryRun: boolean) {
        if (!process.env.QUEUE_URL) {
            throw new Error("QUEUE_URL is not set");
        }
        console.log(`QUEUE_URL: ${process.env.QUEUE_URL}`);
        const client: MongoClient = await loadMongoClient();
        const processorsRepository = client.db("chargeflow_api").collection<IMongoUnifiedDisputeObject>("processors");
        const processors = await processorsRepository.aggregate([
            {
                $match: {"processor_name": "paypal"}
            },
            {
                $lookup: {
                    from: "processors",
                    localField: "chargeflow_id",
                    foreignField: "chargeflow_id",
                    as: "relatedProcessors"
                }
            },
            {
                $match: {"relatedProcessors.processor_name": "shopify"}
            },
            {
                $lookup: {
                    from: "shops",
                    localField: "chargeflow_id",
                    foreignField: "chargeflow_id",
                    as: "shop"
                }
            },
            {
                $unwind: "$shop"
            },
            {
                $project: {
                    chargeflowId: "$chargeflow_id",
                    shopName: "$shop.name"
                }
            }
        ]).toArray();

        console.log(`Found ${processors.length} processors`);
        const disputesToOrderLinking: { shopName: string, disputeIds: string[] }[] = [];
        for (const processor of processors) {
            const disputes = client.db("chargeflow_api").collection<IMongoUnifiedDisputeObject>("disputes");
            console.log(`Processing processor with chargeflow_id: ${processor.chargeflowId} and shopName: ${processor.shopName}`);
            const result: IMongoUnifiedDisputeObject[] = await disputes.find({
                chargeflowId: new ObjectId(processor.chargeflowId),
                'dispute.processor': 'paypal',
                'lastDisputeStatus.status': {$in: ['warning_needs_response', 'needs_response']},
                $or: [
                    {orderName: null},
                    {orderName: {$exists: false}}
                ]
            }).toArray()
            const disputeIds = result.map(dispute => dispute._id)
            console.log(`Found ${disputeIds.length} disputes for shop: ${processor.shopName}, disputeIds: ${disputeIds}`);
            if (disputeIds.length > 0) {
                disputesToOrderLinking.push({shopName: processor.shopName, disputeIds});
            }
        }
        console.log(`Disputes to order linking: ${disputesToOrderLinking.length}`);
        if (dryRun) {
            disputesToOrderLinking.forEach(dispute => {
                console.log(`ShopName: ${dispute.shopName}, disputeIds size: ${dispute.disputeIds.length}`);
            });
            console.log("Dry run completed, no changes were made");
            return;
        }
        const SQS = new SQSClient();
        const queueUrl = process.env.QUEUE_URL!;
        const promises = disputesToOrderLinking.map(dispute => {
            const params = {
                MessageAttributes: {
                    shopName: {
                        DataType: 'String',
                        StringValue: dispute.shopName,
                    },
                },
                MessageBody: JSON.stringify(dispute.disputeIds),
                QueueUrl: queueUrl
            };
            const command = new SendMessageCommand(params);
            return SQS.send(command);
        });
        const response = await Promise.allSettled(promises);
        const success = response.filter(r => r.status === 'fulfilled').length;
        const failed = response.filter(r => r.status === 'rejected').length;
        console.log(`Sent ${success} messages to the queue`);
        console.log(`Failed to send ${failed} messages to the queue`);
    }
}

const script = new ResendOrderLinking();
script.main();

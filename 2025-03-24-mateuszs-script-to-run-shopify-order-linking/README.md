# Purpose
This script resends disputes to the PaypalOrderLinkingQueue via SQS in relation to back-fill shopify orders for disputes.
https://linear.app/chargeflow/issue/CHA-13658/prepare-and-run-order-linking-for-paypal-disputes

## Motivation
We need to resend disputes to the PaypalOrderLinkingQueue to ensure that the orders were linked to the disputes.

## Running this script
`npx ts-node ./script.ts <dry | wet-primary>`

## Modes
- dry - only logs the total number of disputes that are eligible for resending
- wet-primary - sends disputes to the OrderLinkingQueue via SQS

## Required env variables
MONGO_URI=<mongo_uri_string>
QUEUE_URL=<sqs queue url>

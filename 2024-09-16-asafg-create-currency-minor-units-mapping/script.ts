import dotenv from 'dotenv';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import sourceMapping from './source_mapping.json';

dotenv.config();

export class ScriptCreateCurrencyMinorUnitsMapping extends ScriptBase {
    public getName(): string {
        return 'Create currency minor units mapping';
    }

    public getAuthor(): string {
        return 'Asaf Gamliel';
    }

    public getTicket(): string {
        return 'CHA-8419';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {

        const handleDryRun = async () => {
            console.log('Dry run');
            console.log(sourceMapping);
            const mapByCode: any = {};
            Object.entries(sourceMapping).forEach(([processor, value]) => {
                Object.entries(value).forEach(([minorUnits, codesList]) => {
                    const innerObject = Object.fromEntries([[processor, minorUnits]]);
                    codesList.forEach((code) => {
                        mapByCode[code] = {...mapByCode[code], ...innerObject};
                    });
                });
            });
            console.log(JSON.stringify(mapByCode));
        };
        const handleWetRun = async () => {
            console.log('Wet run');
        };
        try {
            if (isDryRun) {
                await handleDryRun();
            } else {
                await handleWetRun();
            }
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        }
    }
}

const script = new ScriptCreateCurrencyMinorUnitsMapping();
script.main();

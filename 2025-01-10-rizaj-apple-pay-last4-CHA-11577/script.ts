import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Db } from 'mongodb';

dotenv.config();

const applePayWalletFilter = {
    'dispute.processor': 'stripe',
    'transaction.rawData.transaction.latest_charge.payment_method_details.card.wallet.apple_pay': {
        $exists: true,
    },
};

export class ScriptDeleteUser extends ScriptBase {
    db: Db | undefined = undefined;

    public getName(): string {
        return 'Fix apple pay last4 digits';
    }

    public getAuthor(): string {
        return '<PERSON><PERSON> Jumola';
    }

    public getTicket(): string {
        return 'CHA-11577';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(context: ScriptContext, isDryRun: boolean) {
        const client = await loadMongoClient();
        const collection = client.db('chargeflow_api').collection('disputes');
        if (isDryRun) {
            const items = await collection.find(applePayWalletFilter).toArray();
            console.log('Will update', items.length, 'items.');
        } else {
            const updateResult = await collection.updateMany(
                applePayWalletFilter,
                [
                    {
                        $set: {
                            'transaction.paymentDetails.last4':
                                '$transaction.rawData.transaction.latest_charge.payment_method_details.card.wallet.dynamic_last4',
                        },
                    }
                ]
            );
            console.log('Update result:', updateResult);
        }
    }
}

const script = new ScriptDeleteUser();
script.main();

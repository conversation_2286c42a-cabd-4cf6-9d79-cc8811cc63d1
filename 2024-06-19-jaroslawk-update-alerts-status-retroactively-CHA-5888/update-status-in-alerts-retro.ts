import { AnyBulkWriteOperation, Document, MongoClient } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext, ScriptMode } from '../shared/script-base';

class Script extends ScriptBase {

  public getName(): string {
    return 'Update alert statuses with type fraud-notification and no processorTransactionId field in alerts retroactively';
  }

  public getAuthor(): string {
    return 'Jaroslaw Kyc';
  }

  public getTicket(): string {
    return 'CHA-5888';
  }

  async dryRun(context: ScriptContext) {
    await this.logic(context);
  }

  wetRunOnSecondary(_context: ScriptContext): Promise<void> {
    throw new Error('Method not implemented.');
  }

  async wetRunOnPrimary(context: ScriptContext) {
    await this.logic(context);
  }

  private async logic(ctx: ScriptContext) {
    const mode = (ctx.args[0] ?? 'dry') as ScriptMode;
    const client: MongoClient = await loadMongoClient();


    if (mode === 'dry') {
      console.log('Dry run mode');
      console.log('Updating alerts with type fraud-notification and no processorTransactionId field in alerts to status Alerted');
    }
    else {
      const updatedCount = await this.updateAlert(client);
      console.log('Updated %s alerts', updatedCount);
    }
  }

  private async updateAlert(client: MongoClient): Promise<number> {
    try {
      return client
        .db('chargeflow_api')
        .collection('alerts')
        .updateMany(
          {
            type: 'fraud-notification',
            processorTransactionId: { $in: [null, ""] }
          },
          {
            $set: {
              status: 'Alerted'
            }
          }

        )
        .then((result) => result.modifiedCount);
    }
    catch (error) {
      console.error('Failed to update alerts %s', error);
      return 0;
    }
  }
}

const script = new Script();
script.main();

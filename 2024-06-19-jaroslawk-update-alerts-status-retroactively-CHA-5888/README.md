# Purpose
This script finds and updates alerts with type `fraud-notification` and no `processorTransactionId` to status `Alerted`

https://linear.app/chargeflow/issue/CHA-5888/write-a-script-to-update-alerts-statues-retroactively

## Motivation
Due to the introduction of "Acknowledged" status on Chargeback Hit's side, Alerts from type "Fraud Notification" are displayed on our dashboard as "Prevented" even if they are only "Alerted" (meaning, we couldn't make the refund). 

This task should introduce a quick fix until we agree on a permanent solution with Chareback hit.

## Running this script
`npx ts-node ./update-status-in-alerts-retro.ts <dry or wet-secondary or wet-primary> 2>&1 | tee log.txt`

NOTE: 
1. a `.env` file is expected to be present in CWD - using `MONGO_URI`

When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console 
wet-secondary - not utilized in this script
wet-primary - updates the ALERTS collection.

### How this script was tested
[x] Alerts have:
  [x] no processorTransactionId set
  [x] type `fraud-notification`
{"compilerOptions": {"incremental": false, "target": "es2020", "module": "commonjs", "outDir": "out", "rootDir": "src", "strict": true, "sourceMap": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "resolveJsonModule": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "**/*.spect.ts"]}
# Purpose
This script add 'merchantAction' object to our udo (under chargeflow object), it will contain 'isActionRequired' and a set of string of actions performed by the merchant for ref.

```
UDO:{
    chargeflow:{
        MerchantActions:{
            isActionRequired:Boolean,
            performedActions:[]
        }
    }
}
```

## Motivation:
 In order to replace logic spread in afew places (including front end) determinig if an action is required, we want to have a single indicator on the dispute representing if an action is required.

## Running this script
`npx ts-node ./script.ts <dry or wet-primary> <date> 2>&1 | tee log.txt`
Example usage: `npx ts-node ./script.ts wet-primary 2024-03-01 2>&1 | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD  
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes mapped update opreations to the console  (without actually applying them)
wet-primary - Updates MerchantActions on dispute collection

### How this script was tested
[x] dry run on dev env

[x] wet run on dev env
    [x] ensure isActioRequired is false if not handled by CF
    [x] ensure isActionRequired is true if no evidences performed by merchant
    [x] ensure isActionRequired is false if evidence has been submitted for merchant 
    [x] ensure performedActions is a set (no duplicate values)

[x] dry run on prod env

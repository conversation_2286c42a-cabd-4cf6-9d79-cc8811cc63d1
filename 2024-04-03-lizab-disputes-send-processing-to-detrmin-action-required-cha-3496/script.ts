import { ObjectId, MongoClient } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";

type DisputeResult = {
    _id: string;
    chargeflow: { 
        evidences: { evidenceUploadCategory: string, role: string, isDeleted: boolean }[];
        cfActions: { handleByChargeflow: boolean };
    };
    lastDisputeStatus: { status: string };
};

class Script extends ScriptBase {
    public getName(): string {
        return "Add chargeflow.merchantActions to disputes";
    }
    public getAuthor(): string {
        return "Liza Bogorad";
    }
    public getTicket(): string {
        return "CHA-3496";
    }

    async dryRun(context: ScriptContext) {
        const startDate = context.args[1] || new Date().toISOString().split('T')[0]; // Defaults to today if no start date provided
        console.log("Running in dry run (not changing anything)");
        await this.runUpdates(startDate, true, context);
    }

    async wetRunOnPrimary(context: ScriptContext) {
        const startDate = context.args[1] || new Date().toISOString().split('T')[0]; // Defaults to today if no start date provided
        console.log("Executing wet run (changes will be made)");
        await this.runUpdates(startDate, false, context);
    }
    async wetRunOnSecondary(context: ScriptContext) {
        throw new Error('Method not implemented.');
    }

    private async runUpdates(startDate: string, dryRun: boolean, context: ScriptContext) {
        const dbName: string = 'chargeflow_api';
        const client = await loadMongoClient();
        const currentDate = new Date();
        let day = new Date(startDate);

        while (day <= currentDate) {
            const formattedDate = day.toISOString().split('T')[0];
            console.log(`Handling updates for date: ${formattedDate}`);
            const filter = this.getFilterForDate(day);
            const updateOperations = await this.getUpdateOperations(client, filter);

            if (dryRun) {
                console.log(`Dry run - Total updates for ${formattedDate}: ${updateOperations.length}`);
            } else {
                console.log(`Wet run - Executing updates for ${formattedDate}`);
                const results = await this.doWetRun(updateOperations, dbName, client);
                console.dir(`Results for ${formattedDate}: `, results);
            }

            day.setDate(day.getDate() + 1);
        }
    }

    private getFilterForDate(date: Date) {
        const nextDay = new Date(date);
        nextDay.setDate(date.getDate() + 1);
        return {
            'dateCreated': {
                '$gte': date,
                '$lt': nextDay
            },
            'chargeflow.merchantActions': { '$exists': true }
        };
    }

    private async doWetRun(bulkUpdate: any[], dbName: string, client: MongoClient) {
        if (!bulkUpdate.length) {
            console.log('No updates to perform');
            return;
        }
        try {
            return await client.db(dbName).collection('disputes')
                .bulkWrite(bulkUpdate)
                .catch(err => {
                    console.error(err);
                    throw err;
                });

        } catch (err) {
            console.error(err);
            throw err;
        }
    }

    private async getUpdateOperations(client: MongoClient, filter: object) {
        const projection = {
            '_id': 1,
            'chargeflow': 1,
            'lastDisputeStatus': 1
        };

        return await client
            .db("chargeflow_api")
            .collection("disputes")
            .find(filter, { projection })
            .map((result) => this.mapUpdateOp(result as unknown as DisputeResult)) 
            .toArray();
    }
    private mapUpdateOp(result: DisputeResult) {    
        const checkIfMerchantAction = (evidence: {role?: string, sentBy?:string, evidenceUploadCategory: string, isDeleted: boolean}) => evidence.role === 'merchant' || evidence.sentBy === 'merchant' &&  !evidence.isDeleted && evidence.evidenceUploadCategory && evidence
        const merchantActions = result.chargeflow?.evidences?.filter((evd: { evidenceUploadCategory: string, role: string, isDeleted: boolean }) => checkIfMerchantAction(evd))?.filter(Boolean)
        const merchantActionsNames = merchantActions?.map((evd: { evidenceUploadCategory: string }) => evd.evidenceUploadCategory)
        const merchantActionsSet = new Array ( ...new Set(merchantActionsNames))
        const isActionRequired = !merchantActions?.length && result.chargeflow?.cfActions?.handleByChargeflow && ['needs_response','warning_needs_response'].includes(result.lastDisputeStatus?.status)
        const mappedUpdate = { 
            $set: { 'chargeflow.merchantActions.isActionRequired': isActionRequired,
            ...(merchantActionsSet.length ?  { 'chargeflow.merchantActions.performedActions':  [...merchantActionsSet] } : { 'chargeflow.merchantActions.performedActions': [] } )
        }}

        
        return {
            updateOne: {
                filter: { _id: new ObjectId(result._id) },
                update: mappedUpdate
            }
        }
    }
}

const script = new Script();
script.main(); 
import { MongoClient, Db, ObjectId } from 'mongodb';
import { DataAccessUnitOfWork } from "@chargeflow-team/data-access";

export interface Logger {
    log: (message: string) => void;
}

export class AssignProcessorsToShops {
    private readonly DB_NAME = 'chargeflow_api';
    private readonly SHOPS_COLLECTION_NAME = 'shops';
    private readonly BUSINESS_UNITS_COLLECTION_NAME = 'businessUnits';
    private readonly dataAccess;
    private db: Db

    constructor(
        mongoClient: MongoClient,
        private readonly logger: Logger,
    ) {
        this.db = mongoClient.db(this.DB_NAME);
        this.dataAccess = new DataAccessUnitOfWork(mongoClient);
    }


    async execute(batchSize: number, page: number, dryRun: boolean): Promise<void> {
        try {
            this.logger.log('Creating business units for shops started, please wait ...');
            const query = missingBusinessUnitQuery();
            const result = await this.db.collection(this.SHOPS_COLLECTION_NAME)
                .aggregate(query)
                .toArray();
            const totalItems = result.length;
            const totalBatches = Math.ceil(totalItems / batchSize);
            this.logger.log(`Total documents to process: ${totalItems}`);
            this.logger.log(`Total batches to process: ${totalBatches}`);

            if (dryRun) {
                this.logger.log('Dry run mode enabled, no changes made to the database');
                return;
            }
            for (let currentBatch = page; currentBatch <= totalBatches; currentBatch++) {
                try {
                    this.logger.log(`Processing batch ${currentBatch} of ${totalBatches}`);
                    const currentPage = (currentBatch - 1) * batchSize;
                    const shops = await this.db.collection(this.SHOPS_COLLECTION_NAME)
                        .aggregate(query)
                        .skip(currentPage)
                        .limit(batchSize)
                        .toArray();
                    await Promise.allSettled(
                        shops.map(async shop => {
                           if (!shop.chargeflow_id) {
                               this.logger.log(`Shop has not identifiers, skipping`);
                               throw Error('Shop has not identifiers');
                           }
                           const existingBusinessUnit = await this.db.collection(this.BUSINESS_UNITS_COLLECTION_NAME)
                               .findOne({ chargeflowId: new ObjectId(shop.chargeflow_id) });
                           if (existingBusinessUnit) {
                               this.logger.log(`Business unit for shop with chargeflowId ${shop.chargeflow_id} already exists, skipping`);
                               throw Error('Business unit already exists');
                           }
                           const company = await this.dataAccess.company.findByChargeflowId(shop.chargeflow_id.toString());
                           if (!company) {
                               this.logger.log(`Company with chargeflowId ${shop.chargeflow_id} not found`);
                           }
                           const businessUnit = {
                               chargeflowId: shop.chargeflow_id.toString(),
                               accountId: shop.accountId?.toString(),
                               name: company?.users?.businessInformation?.businessName || shop.name,
                               country: company?.users?.businessInformation?.businessCountry,
                               url: company?.users?.businessInformation?.businessUrl
                           };
                           this.logger.log(`Creating business unit for shop with chargeflowId ${shop.chargeflow_id} and data: ${JSON.stringify(businessUnit)}`);
                           return await this.dataAccess.businessUnits.add(businessUnit);
                       }),
                    );
                    this.logger.log(`Batch ${currentBatch} of ${totalBatches} processed successfully`);
                } catch (error: unknown) {
                    if (error instanceof Error) {
                        this.logger.log(`Batch ${currentBatch} of ${totalBatches} failed, reason: ${error.message}`);
                    }
                    throw error;
                }
            }
            this.logger.log('Creating business units for shops finished successfully');
        } catch (error) {
            if (error instanceof Error) {
                this.logger.log(`Creating business units for shops failed, reason: ${error.message}`);
            }
            throw error;
        }
    }
}

export const missingBusinessUnitQuery = () => {
    return [
        {
            $lookup: {
                from: "businessUnits",
                localField: "chargeflow_id",
                foreignField: "chargeflowId",
                as: "matched"
            }
        },
        {
            $match: { matched: { $size: 0 } }
        },
        {
            $project: { chargeflow_id: 1, _id: 1, date_created:1 }
        },
    ];
};

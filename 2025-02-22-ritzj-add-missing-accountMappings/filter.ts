export const FILTER = [
  // {
  //   "$match": {
  //     "chargeflow_id": ObjectId("640091e0cba6432b02059e5b")
  //   }
  // },
  {
    "$lookup": {
      "from": "accountMappings",
      "let": {
        "email": "$email",
        "chargeflow_id": "$chargeflow_id"
      },
      "pipeline": [
        {
          "$match": {
            "$expr": {
              "$and": [
                { "$eq": ["$email", "$$email"] },
                { "$eq": ["$chargeflowId", "$$chargeflow_id"] }
              ]
            }
          }
        }
      ],
      "as": "matchingMappings"
    }
  },
  {
    "$match": {
      "matchingMappings": { "$size": 0 }
    }
  },
  {
    "$lookup": {
      "from": "accounts",
      "localField": "name",
      "foreignField": "name",
      "as": "accountData"
    }
  },
  {
    "$unwind": {
      "path": "$accountData",
      "preserveNullAndEmptyArrays": true
    }
  },
  {
    "$project": {
			"name": 1,
      "email": 1,
      "chargeflow_id": 1,
      "accountsId": "$accountData._id"
    }
  },
  {
		"$match": {
		  "accountsId": { $exists: true }
		}
	}
]

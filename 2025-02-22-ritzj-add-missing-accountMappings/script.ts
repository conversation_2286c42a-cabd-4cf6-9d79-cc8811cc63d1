import { ObjectId } from "mongodb";
import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { Db } from "mongodb";
import { DataAccessUnitOfWork } from '@chargeflow-team/data-access';
import { IAccountMapping } from "@chargeflow-team/data-access/dist/AccountMappings/IAccountMapping";
import { FILTER } from './filter'

dotenv.config();



export class Script extends ScriptBase {

  public db!: Db;
  public getName(): string {
    return "Update existing account mappings docs";
  }

  public getAuthor(): string {
    return "Ritz Jumola";
  }

  public getTicket(): string {
    return "CHA-12074";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow([
        "MONGO_URI",
        "MONGO_DB_NAME",
      ]);

      const mongo = await loadMongoClient();
      console.log("Mongo client loaded");

      this.db = mongo.db(process.env.MONGO_DB_NAME);

      const SHOPS = this.db.collection("shops");

      const shopsWithNoAccountMappings = await SHOPS.aggregate(FILTER).toArray();

      console.log(`Found ${shopsWithNoAccountMappings.length} shops with no account mappings`);

      if (!shopsWithNoAccountMappings.length) {
        console.log("No shops found with no account mappings");
        return;
      }

      const da = new DataAccessUnitOfWork(mongo);

      const mappeds = shopsWithNoAccountMappings.map((shop) => {
        const mappedShop: IAccountMapping = {
          chargeflowId: shop.chargeflow_id.toString(),
          accountId: shop.accountsId.toString(),
          email: shop.email,
          dateCreated: new Date(),
          dateUpdated: new Date(),
          adminName: 'Chargeflow Application',
          name: 'Chargeflow User',
          onboardingStatus: 'active',
          role: 'admin',
        };
        return mappedShop;
      });

      console.log(`Mapped ${mappeds.length} shops`);


      if (isDryRun) {
        console.log("Dry run mode, not updating anything");
        return;
      }

      await da.accountMappings.insertAccountMapping(mappeds);

      console.log("Inserted account mappings");

    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }
}

const script = new Script();
script.main();

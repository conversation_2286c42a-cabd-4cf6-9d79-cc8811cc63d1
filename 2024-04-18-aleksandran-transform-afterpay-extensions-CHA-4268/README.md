# Purpose
This script should transofrm extensions field on dispute from array to object.

https://linear.app/chargeflow/issue/CHA-4268/transform-afterpay-extensions

## Motivation
We have mapped disputes from afterpay with wrong format for extensions field.
We want to transform extensions field to correct format (from array to object).

## Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console
wet-secondary - does not update the source table, writes results to another collection (disputes_test), adding documents as needed
wet-primary - UPDATES THE DISPUTES TABLE. Use with extreme caution!

### How this script was tested
[x] Run locally with development database

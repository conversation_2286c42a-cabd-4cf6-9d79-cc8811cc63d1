import {loadMongoClient} from "../shared/mongo-config";
import {ScriptBase} from "../shared/script-base";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { ObjectId } from "mongodb";

export class FixWixMistake extends ScriptBase {

    public getName(): string {
        return "Fixes mistake during wix import";
    }

    public getAuthor(): string {
        return "Ritz Jumola";
    }

    public getTicket(): string {
        return "cha-14715-wix-fix-transaction-id-for-data-batch-of-04-24";
    }

    async dryRun(): Promise<void> {
        await this.logic();
    }

    async wetRunOnPrimary(): Promise<void> {
        await this.logic();
    }

    async wetRunOnSecondary(): Promise<void> {
        await this.logic();
    }

    async logic() {
        isEnvVarsOkOrThrow(['MONGO_URI', 'MONGO_DB_NAME'])

        const mongo = await loadMongoClient();

        const WIX_CHARGEFLOW_ID = '67b72c33719d262c00af6992';
        const MONGO_VIEW_NAME = 'cha-14715-investigation';
        const VIEW = mongo.db(process.env.MONGO_DB_NAME!).collection(MONGO_VIEW_NAME);

        let itemsQuery = VIEW.find<{
            _id: ObjectId;
            psp: string;
            caseId: string;
            transactionId: string;
            fileDisputeId: string;
        }>({});

        if (this.mode === 'wet-secondary') {
            itemsQuery = itemsQuery.limit(1);
        }

        const items = await itemsQuery.toArray();

        // create bulk ops to update disputes
        const bulkOps = items.map(item => {
            return {
                updateOne: {
                    filter: { chargeflowId: new ObjectId(WIX_CHARGEFLOW_ID), 'dispute.id': item.caseId },
                    update: { $set: { 'transaction.id': item.transactionId } }
                }
            };
        });

        if (!bulkOps.length) {
            console.log('No bulk operations to perform.');
            return;
        }

        console.log('Bulk ops:', bulkOps.length, 'sample', JSON.stringify(bulkOps[0]));

        if (this.mode === 'dry') {
            console.log('Dry run enabled, skipping update.');
        }

        if (this.mode === 'wet-primary' || this.mode === 'wet-secondary') {
            const DISPUTES = mongo.db(process.env.MONGO_DB_NAME!).collection('disputes');
            const result = await DISPUTES.bulkWrite(bulkOps);
            const failedCount = result.getWriteErrors().length;
            if (failedCount) {
                console.log('Failed to update', failedCount, 'disputes');
                console.log('Failed write errors:', result.getWriteErrors());
            }
            console.log('Updated', result.modifiedCount, 'disputes');
            console.log('Inserted', result.upsertedCount, 'disputes');
            console.log('Matched', result.matchedCount, 'disputes');
            console.log('Bulk write result:', result);

            console.log('Bulk write operation completed successfully.');
        }

    }
}

const script = new FixWixMistake();
script.main();

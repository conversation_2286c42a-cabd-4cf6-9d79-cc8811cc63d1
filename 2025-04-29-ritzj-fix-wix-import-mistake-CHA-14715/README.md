# Purpose

<PERSON><PERSON> mistakenly forgot to remove from the Wix CSV the "transaction id" column, resulting in over-writing PSP transaction IDs. This is incorrect + causes issues in syncing the chargebacks queue to operational analysts.

## Requested Solution
Requesting engineering to fix the Transaction IDs of the Wix cases which we need to handle as appearing in the file attached. The transaction IDs should be the ones we, Chargeflow, get from the PSP.

## Running this script

`npx ts-node ./script.ts <dry | wet-primary>`

## Modes

- dry - only logs the processors for which the script will run
- wet-primary - updates adyen webhook for processor

## Required env variables

MONGO_URI=<mongo_uri_string>

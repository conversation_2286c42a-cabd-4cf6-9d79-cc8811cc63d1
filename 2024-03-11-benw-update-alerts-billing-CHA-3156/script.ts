import { AnyBulkWriteOperation } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';

export class updateAlertsBilling extends ScriptBase {
  public getName(): string {
    return 'Update alerts billing with `alertsSubscription`';
  }

  public getAuthor(): string {
    return '<PERSON> Weber';
  }

  public getTicket(): string {
    return 'CHA-3156';
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log('Running in dry run mode (not changing anything)');
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error('Method not implemented.');
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    const client = await loadMongoClient();
    const db = client.db('chargeflow_api');
    const alertsRegistryCollection = db.collection('alertsRegistry');
    const companiesCollection = db.collection('companies');

    const alertRegistries = await alertsRegistryCollection
      .find({ authorizedAt: { $ne: null } })
      .toArray();

    const bulkOperations: AnyBulkWriteOperation<Document>[] = [];

    console.log(`Processing ${alertRegistries.length} Alert Registries...`);
    for (const registry of alertRegistries) {
      const company = await companiesCollection.findOne({
        'users.chargeflowId': registry.chargeflowId,
        'users.billing.alertsSubscription': { $exists: false },
      });

      if (company) {
        console.log(
          `Preparing update for company with chargeflowId: ${company.users.chargeflowId.toString()}`
        );
        bulkOperations.push({
          updateOne: {
            filter: { 'users.chargeflowId': registry.chargeflowId },
            update: {
              $set: {
                'users.billing.alertsSubscription': {
                  ...company.users.billing.stripeCustomer,
                  dateCreated: new Date(),
                },
              },
            },
          },
        });
      } else {
        console.log('-'.repeat(50));
        console.log(
          `!!! No matching company found for chargeflowId: ${registry.chargeflowId.toString()}.`
        );
        console.log('-'.repeat(50));
      }
    }

    if (isDryRun) {
      bulkOperations.forEach(op =>
        console.log(`(Dry Run) Prepared operation: ${JSON.stringify(op)}`)
      );
    } else if (!isDryRun && bulkOperations.length > 0) {
      const result = await companiesCollection.bulkWrite(bulkOperations as any);
      result.modifiedCount;
      console.log(`Total companies updated: ${result.modifiedCount}`);
    }

    client.close();
  }
}

const script = new updateAlertsBilling();
script.main();

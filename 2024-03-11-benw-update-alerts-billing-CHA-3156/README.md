# Purpose
This script updates billing for Alerts customers: adds `alertsSubscription` to `users.billing` if they alertRegistry is authorized and were not yet added to __companies__ collection

https://linear.app/chargeflow/issue/CHA-3156/script-migrate-alertsubscription-billing-object

## Motivation
Alerts is a subscription-based service which we are rolling out to our clients. We had a prelimenary data structure which was initiated as POC. Now that we are migrated to the new data structure, we are deleting fields which are no longer relevant

## Running this script
`npx ts-node ./script.ts <dry or wet-primary> 2>&1 | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD  
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console  
wet-secondary - not utilized in this script
wet-primary - UPDATES FIELDS THE COMPANIES COLLECTION. Use with extreme caution!

### How this script was tested
[x] `alertsSubscription` object was added to `users.billing` in __companies__ collection for all alertProfiles that were previously authorized
[x] `alertsSubscription` object in __companies__ created with respective fields:
  [x] `customerPortalUrl`
  [x] `dateCreated`
  [x] `id`
  [x] `isSourceExpired`
  [x] `paymentMethodId`
  [x] `source`
[x] new `users.billing.alertsSubscription` object in __companies__ matches `users.billing.stripeCustomer` object with respective fields:
  [x] `customerPortalUrl`
  [x] `dateCreated` -- with new Date() value
  [x] `id`
  [x] `isSourceExpired`
  [x] `paymentMethodId`
  [x] `source`
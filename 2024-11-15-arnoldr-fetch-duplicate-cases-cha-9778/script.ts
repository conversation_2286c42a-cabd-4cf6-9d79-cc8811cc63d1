import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Db } from 'mongodb';
import path from 'path';
import fs from 'fs/promises';

dotenv.config();

const mongo = loadMongoClient();

type FetchResult = { caseId: string }
type CaseCount = { [key: string]: number };
let existingCounts: CaseCount = {};

export class CreateDuplicateCaseIdsReport extends ScriptBase {
  db: Db | undefined = undefined;

  public getName(): string {
    return "Create report of duplicate case ids";
  }

  public getAuthor(): string {
    return "Arnold Ramos";
  }

  public getTicket(): string {
    return "CHA-9778";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }


  private async fetchDisputeCounts() {
    const mongoInstance = await mongo
    return mongoInstance
      .db("chargeflow_api")
      .collection("disputes")
      .countDocuments();
  }

  private async fetchDisputesWithCaseId(page: number, limit: number) {
      const mongoInstance = await mongo
    return mongoInstance
      .db("chargeflow_api")
      .collection("disputes")
      .find({ 'dispute.id': { $exists: true, $ne: null } })
      .project({ caseId: '$dispute.id', _id: 0 })
      .skip(page * limit)
      .limit(limit)
      .toArray() as unknown as Promise<FetchResult[]>
  }

  private async fetchDisputesWithCaseIdArray(caseIds: string[]) {
    const mongoInstance = await mongo
    return mongoInstance
      .db("chargeflow_api")
      .collection("disputes")
      .find({ 'dispute.id': { $in: caseIds } })
      .project({ caseId: '$dispute.id', _id: 0 })
      .toArray() as unknown as Promise<FetchResult[]>
  }

  private async createLog(logs: any, fileName: string) {
    const logPath = path.resolve(__dirname, fileName);
    await fs.writeFile(logPath, JSON.stringify(logs, null, 2));
  }

  private processDuplicatedArray(set1: string[], set2: string[]) {
    return set1.reduce((acc: CaseCount, cur: string) => {
      const count = set2.filter((item) => item === cur).length;
      if (count > 1) {
        acc[cur] = count;
      }
      return acc;
    }, {} as CaseCount);
  }
  


  private async processDuplicatedDisputes() {
    let hasMore = true;
    let page = 1
    const limit = 2000
    let reportPage = 1
    let count = 0
    while (hasMore) {
      console.log('Existing counts', Object.keys(existingCounts).length)
      const disputesResult = await this.fetchDisputesWithCaseId(page, limit);
      const caseIdsFromFetch = disputesResult.map(({ caseId }) => caseId);
      const dupeDisputesArray = await this.fetchDisputesWithCaseIdArray(caseIdsFromFetch);
      const processed =  this.processDuplicatedArray(caseIdsFromFetch, dupeDisputesArray.map(({ caseId }) => caseId));
      existingCounts = { ...existingCounts, ...processed }

      console.log('Processed records', page * disputesResult.length)

      page += 1;
      count += disputesResult.length;
      hasMore = disputesResult.length === limit;
    }

    console.log('Total records processed', count)
    if (Object.keys(existingCounts).length > 0) {
      await this.writeResultsInFile(existingCounts);
    }
  }

  private async writeResultsInFile(data:CaseCount) {
    const filePath = path.resolve(__dirname, `duplicateCasesIds.json`);
    await fs.writeFile(filePath, JSON.stringify(data, null, 2));
  }


  private async logic(context: ScriptContext, isDryRun: boolean) {
    if (!isDryRun) {
      console.time('ProcessingDuplicateDisputes')
      console.log('RESETTING LOG REPORTS')
      await this.createLog({}, 'duplicateCaseIds.json');
      await this.processDuplicatedDisputes();
      console.timeEnd('ProcessingDuplicateDisputes')
    }
  }
}

const script = new CreateDuplicateCaseIdsReport();
script.main();

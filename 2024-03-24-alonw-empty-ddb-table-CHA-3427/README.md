# Purpose
This generic utility script purges all contents of a dynamodb table

## Motivation
As part of the Global Reconciliation process, the prod-dispute-reconciliation-log-events and prod-dispute-reconciliation-tasks have a lot of junk entries that are better deleted

## Running this script
`npx ts-node ./index.ts <dry or wet-primary> 2>&1 | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD  
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)
The following environment variables are required:
DDB_TABLE_NAME - name of dynamodb table
AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, (AWS_SESSION_TOKEN if required) - AWS access

### Modes
dry - does not delete anything, writes the deletion requests to the console but does not execute them
wet-secondary - PERFORMS ACTUAL DELETION
wet-primary - PERFORMS ACTUAL DELETION

### How this script was tested
[x] dry run on a dynamo table with items
[x] wet run on a dynamo table with items
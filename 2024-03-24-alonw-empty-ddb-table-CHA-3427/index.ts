import { DynamoDB, WriteRequest } from "@aws-sdk/client-dynamodb";
import { ScriptBase, ScriptContext } from "../shared/script-base";

interface ScriptResult {
    foundItems: number;
    deletedItems: number;
    tableName: string;
    keyProperties: string[];

}
class Script extends ScriptBase {
    public getName(): string {
        return "Purge a DyanmoDB table";
      }
      public getAuthor(): string {
        return "Alon Weiss";
      }
      public getTicket(): string {
        return "CHA-3427";
      }
      dryRun(context: ScriptContext): Promise<void> {
        return this.logic(context, false);
      }
      wetRunOnSecondary(context: ScriptContext): Promise<void> {
        return this.logic(context, true);
      }
      wetRunOnPrimary(context: ScriptContext): Promise<void> {
        return this.logic(context, true);
      }

    private async logic(context: ScriptContext, reallyDeleteItems: boolean = true) {
        if (!process.env.DDB_TABLE_NAME) {
            throw new Error("DDB_TABLE_NAME environment variable is not set.");
        }
        const ddbClient = new DynamoDB();
        const descr = await ddbClient.describeTable({ TableName: process.env.DDB_TABLE_NAME });
        if (!descr.Table) {
            throw new Error(`Table ${process.env.DDB_TABLE_NAME} does not exist.`);
        }
        console.log(`Table ${process.env.DDB_TABLE_NAME} has ${descr.Table.ItemCount} items.`);
        const keyProperties: string[] = descr.Table!.KeySchema!.map((key) => key.AttributeName!) || [];
        if (keyProperties.length === 0) {
            throw new Error(`Table ${process.env.DDB_TABLE_NAME} does not have a primary key.`);
        }
        let itemsLeft = descr.Table.ItemCount || 0;
        let lastEvaluatedKey: { [key: string]: any } | undefined;
        let status: ScriptResult = {
            foundItems: 0,
            deletedItems: 0,
            tableName: process.env.DDB_TABLE_NAME,
            keyProperties: keyProperties
        }
        do {
            console.log(`Scanning for items to delete, ${itemsLeft} items left...`);
            const items = await ddbClient.scan({ 
                TableName: process.env.DDB_TABLE_NAME, ExclusiveStartKey: lastEvaluatedKey,
                AttributesToGet: keyProperties
            });
            if (items.Items) {
                status.foundItems += items.Items.length;
                //in batches of 25, call batchDeleteItems
                while (items.Items.length > 0) {
                    const batch = items.Items.splice(0, 25);
                    await this.batchDeleteItems(ddbClient, { Items: batch }, reallyDeleteItems, status);
                    itemsLeft -= batch.length;
                }
            }
            lastEvaluatedKey = items.LastEvaluatedKey;
        } while (lastEvaluatedKey);
        console.log(`Found ${status.foundItems} items, Deleted ${status.deletedItems} from table ${process.env.DDB_TABLE_NAME}`);
    }

    private async batchDeleteItems(ddbClient: DynamoDB, items: any, reallyDeleteItems: boolean, status: ScriptResult) {
        const batch: WriteRequest[] = [];
        items.Items.forEach((item: any) => {
            const keys: any = {};
            status.keyProperties.forEach((key) => {
                keys[key] = item[key]!;
            });
            batch.push({ DeleteRequest: {Key: keys}});

        });
        if (reallyDeleteItems) {
            console.log(`Executing batch delete operation of ${batch.length} items.`);
            const operationResult = await ddbClient.batchWriteItem({ RequestItems: { [status.tableName]: batch } });
            let unprocessedItemCount = 0;
            if (operationResult.UnprocessedItems && operationResult.UnprocessedItems[status.tableName]) {
                //await this.batchDeleteItems(ddbClient, { Items: operationResult.UnprocessedItems[status.tableName] }, reallyDeleteItems, status);
                unprocessedItemCount = operationResult.UnprocessedItems[status.tableName].length;
            }

            console.log(`Deleted ${batch.length-unprocessedItemCount} items.`);
            status.deletedItems += (batch.length-unprocessedItemCount);
        } else {
            console.log(`Would execute batch delete operation: ${JSON.stringify(batch, null, 2)}`);
        }
    }

}
new Script().main();
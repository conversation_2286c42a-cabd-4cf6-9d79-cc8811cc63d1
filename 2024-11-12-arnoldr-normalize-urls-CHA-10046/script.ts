import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { BulkWriteResult, Db, ObjectId } from 'mongodb';
import path from 'path';
import fs from 'fs/promises';

dotenv.config();

const shopsFilter  = {
  $and: [
    {
      domain: {
        $not: { $regex: "https|myshopify.com", $options: "i" },
      },
    },
    { domain: { $ne: "" } },
    { domain: { $ne: null } }
  ],
}

const settingsFilter = {
  $and: [
    {
      "fields.site_url": {
        $not: { $regex: "https|myshopify.com", $options: "i" },
      },
    },
    { 'fields.site_url': { $ne: "" } },
    { 'fields.site_url': { $ne: null } }
  ],
}
const mongo = loadMongoClient();

type FetchResult = { chargeflow_id: string; domain: string }

export class DeleteChargesForPaidInvoices extends ScriptBase {
  db: Db | undefined = undefined;

  public getName(): string {
    return "Normalize URLS in certain collections";
  }

  public getAuthor(): string {
    return "<PERSON>";
  }

  public getTicket(): string {
    return "CHA-10046";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }
  private normalizeUrl(url: string) {
    try {
      if (!url) {
        return "";
      }
  
      if (url.includes(".myshopify.com")) {
        return url;
      }
  
      if (!/^https?:\/\//i.test(url)) {
        url = "https://" + url;
      }
  
      const urlObj = new URL(url);
  
      urlObj.hostname = urlObj.hostname.replace(/^www./, "");
  
      return urlObj.toString();
    } catch (error) {
      return url
    }
  }

  private mapUpdateFields(data: FetchResult, collectionName: 'shops' | 'settings' = 'shops') {
    const chargeflowIdFilter = {
      chargeflow_id: new ObjectId(data.chargeflow_id),
    };

    const fieldToUpdate = collectionName === 'shops' ? 'domain' : 'fields.site_url';

    const updateDomain = {
      $set: {
        [fieldToUpdate]: this.normalizeUrl(data.domain),
      },
    };

    return {
      updateOne: { filter: chargeflowIdFilter, update: updateDomain },
    };
  }

  private mapUrlChangesBeforeAndAfter(
    before: string,
    after: string,
    chargeflowId: string
  ) {
    return {
      chargeflowId:String(chargeflowId),
      before,
      after,
    };
  }

  private async fetchShopsToUpdateCount() {
    
        const mongoInstance = await mongo
    return mongoInstance
      .db("chargeflow_api")
      .collection("shops")
      .countDocuments(shopsFilter);
  }

  private async fetchSettingsToUpdateCount() {
        const mongoInstance = await mongo
    return mongoInstance
      .db("chargeflow_api")
      .collection("settings")
      .countDocuments(settingsFilter);
  }

  private async fetchShopsToUpdate(page: number, limit: number) {
        const mongoInstance = await mongo
    return mongoInstance
      .db("chargeflow_api")
      .collection("shops")
      .find(shopsFilter)
      .skip(page * limit)
      .limit(limit)
      .toArray() as unknown as Promise<FetchResult[]>
  }

  private async fetchSettingsToUpdate(page: number, limit: number) {
    const mongoInstance = await mongo
    return mongoInstance
      .db("chargeflow_api")
      .collection("settings")
      .aggregate([
        { $match: settingsFilter },
        { $project: { chargeflow_id: 1, domain: "$fields.site_url" } },
        { $skip: page * limit },
        { $limit: limit }
      ])
      .toArray() as unknown as Promise<FetchResult[]>
  }

  private async createLog(logs: any, fileName: string) {
    const logPath = path.resolve(__dirname, fileName);
    await fs.writeFile(logPath, JSON.stringify(logs, null, 2));
  }

  private async bulkUpdate(updateQueries: any, collection: string) {
    const mongoInstance = await mongo
    return mongoInstance
      .db("chargeflow_api")
      .collection(collection)
      .bulkWrite(updateQueries)
  }

  private async updateCollection(collection: string, limit: number, fetchFn: (page:number,limit:number)=>Promise<FetchResult[]>) {
    let hasMore = true;
    let updateResult = 0
    let page = 0
    const mapBeforeAndAfter: { chargeflowId: string; before: string; after: string }[] = [];
    while (hasMore) {
      const fetchResults = await fetchFn(page,limit);
      console.log('PAGE:', page, 'RESULT COUNT:', fetchResults.length, 'HASMORE', hasMore)
      const mappedUpdateQueries = fetchResults.map((data:any) =>
        this.mapUpdateFields(data, collection as 'shops' | 'settings')
      );
      console.log('EXECUTING BULK UPDATE....')
      const res = await this.bulkUpdate(mappedUpdateQueries, collection);
      const logs = fetchResults.map((data:any) => this.mapUrlChangesBeforeAndAfter(data.domain, this.normalizeUrl(data.domain), data.chargeflow_id));
      updateResult += res.modifiedCount;
      const existingLogs = await this.retriveJsonLogs(`${collection}-changelogs.json`);

      logs.forEach((log: any) => {
        if (!existingLogs.some((existingLog: any) => existingLog.chargeflowId === log.chargeflowId)) {
          existingLogs.push(log);
        }
      });

      await this.createLog(existingLogs, `${collection}-changelogs.json`);
      page++
      hasMore = fetchResults.length === limit;
    }

    console.log('DONE UPDATING COLLECTION', collection , 'WITH', updateResult, 'MODIFIED RECORDS')
    return {updateResult, mapBeforeAndAfter};
  }

  private async retriveJsonLogs(fileName: string) {
    const logPath = path.resolve(__dirname, fileName);
    const logs = await fs.readFile(logPath, 'utf-8');
    return JSON.parse(logs);
  }


  private async logic(context: ScriptContext, isDryRun: boolean) {
    const shopsToUpdateCount = await this.fetchShopsToUpdateCount();
    const settingsToUpdateCount = await this.fetchSettingsToUpdateCount();
    console.log({ shopsToUpdateCount, settingsToUpdateCount });
    if (!isDryRun) {
      const PAGE_LIMIT = 500
      console.log('RUNNING UPDATES ON SHOPS COLLECTION')
      const shopsUpdateResult = await this.updateCollection('shops', PAGE_LIMIT, this.fetchShopsToUpdate);
      console.log('SHOPS UPDATE RESULT', shopsUpdateResult.updateResult)
      console.log('REPORTS FOR CHANGES IN SHOPS COLLECTION CREATED')

      console.log('RUNNING UPDATES ON SETTINGS COLLECTION')
      const settingsUpdateResult = await this.updateCollection('settings', PAGE_LIMIT, this.fetchSettingsToUpdate);
      console.log('SETTINGS UPDATE RESULT', settingsUpdateResult.updateResult)
      console.log('REPORTS FOR CHANGES IN SETTINGS COLLECTION CREATED')
    }
  }
}

const script = new DeleteChargesForPaidInvoices();
script.main();

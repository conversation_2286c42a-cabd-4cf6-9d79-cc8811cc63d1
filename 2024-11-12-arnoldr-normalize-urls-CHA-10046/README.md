# Purpose:
Normalize all url formats in certain collections

# Motivation:
For url data consistency

# Running this script
`npx ts-node 2024-11-12-arnoldr-normalize-urls-CHA-10046/script.ts <dry or wet-secondary or wet-primary>`
NOTE:
- a `.env` file is expected to be present in CWD with **MONGO_URI**
- Both settings-changelogs.json and shops-changelogs.json are initialized as empty array. Please do not modify
- The updated URL (before and after) in the collections `shops` and `settings` will be logged on the files shops-changelogs.json and settings-changelogs.json for historical purposes

### How this script was tested
[x] Wet run against dev

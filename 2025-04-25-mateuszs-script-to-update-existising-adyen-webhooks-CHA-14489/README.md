# Purpose

This script updates existing Adyen webhooks with additional events to which we want to subscribe.
https://linear.app/chargeflow/issue/CHA-14489/create-migration-script-to-update-existing-adyen-webhooks

## Motivation

We need to update adyen webhooks with additional events in order to collect additional data to efficiently prepare evidence.
## Running this script

`npx ts-node ./script.ts <dry | wet-primary>`

## Modes

- dry - only logs the processors for which the script will run
- wet-primary - updates adyen webhook for processor

## Required env variables

MONGO_URI=<mongo_uri_string>
ADYEN_ENV=<TEST | LIVE>

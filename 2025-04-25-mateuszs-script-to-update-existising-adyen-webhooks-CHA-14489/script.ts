import {loadMongoClient} from "../shared/mongo-config";
import {ScriptBase} from "../shared/script-base";
import {Source} from "@chargeflow-team/data-access";
import {
    IAdyenConnectData,
    IProcessorConcrete
} from "@chargeflow-team/data-access/dist/Processor/Types";
import {Client, ManagementAPI} from "@adyen/api-library";
import {UpdateCompanyWebhookRequest} from "@adyen/api-library/lib/src/typings/management/updateCompanyWebhookRequest";


export class UpdateAdyenWebhooks extends ScriptBase {

    public getName(): string {
        return "Update adyen webhooks";
    }

    public getAuthor(): string {
        return "Mateusz Smagiel";
    }

    public getTicket(): string {
        return "cha-14489-create-migration-script-to-update-existing-adyen-webhooks";
    }

    async dryRun(): Promise<void> {
        await this.logic(true);
    }

    async wetRunOnPrimary(): Promise<void> {
        try {
            await this.logic(false);
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    async wetRunOnSecondary(): Promise<void> {
        throw new Error("Method is not implemented.");
    }

    async logic(dryRun: boolean) {
        const client = await loadMongoClient();
        const processors = await client
            .db('chargeflow_api')
            .collection<IProcessorConcrete<IAdyenConnectData>>('processors')
            .find({
                processor_name: Source.Adyen,
            })
            .toArray();
        console.log(`Found ${processors.length} processors`);
        for (const processor of processors) {
            const {apiKey, companyID, webhookID} = processor.connect;
            if (!apiKey) {
                console.log('missing apiKey %j', processor);
                continue;
            }
            if (!companyID) {
                console.log('missing companyID %j', processor);
                continue;
            }
            if (!webhookID) {
                console.log('missing webhookID %j', processor);
                continue;
            }
            if (dryRun) {
                console.log('dry run enabled, skipping updating processor webhook %j', processor);
                continue;
            }
            const api = this.createClient(apiKey);
            const updateCompanyWebhookRequest: UpdateCompanyWebhookRequest = {
                additionalSettings: {
                    includeEventCodes: [
                        'AUTHORISATION',
                        'CANCEL_OR_REFUND',
                        'AUTHORISATION_ADJUSTMENT',
                        'CHARGEBACK_REVERSED',
                        'REQUEST_FOR_INFORMATION',
                        'NOTIFICATION_OF_CHARGEBACK',
                        'CHARGEBACK',
                        'PREARBITRATION_WON',
                        'SECOND_CHARGEBACK',
                        'PREARBITRATION_LOST',
                        'DISPUTE_DEFENSE_PERIOD_ENDED',
                        'ISSUER_RESPONSE_TIMEFRAME_EXPIRED',
                    ]
                }
            }
            console.log('updating webhook %j', processor);
            try {
                const response = await api.WebhooksCompanyLevelApi.updateWebhook(companyID, webhookID, updateCompanyWebhookRequest);
                console.log('webhook updated %j', response);
            } catch (error) {
                console.error(error);
            }
        }
    }

    private createClient(apiKey: string) {
        const env = process.env.ADYEN_ENV! as Environment;
        const client = new Client({apiKey: apiKey, environment: env});
        return new ManagementAPI(client);
    }
}

const script = new UpdateAdyenWebhooks();
script.main();

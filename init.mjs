import {userInfo} from 'node:os';
import readline from 'node:readline';
import fs from 'node:fs';

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
});

const capitalized = (e) => e[0].toUpperCase() + e.slice(1);

async function question(query) {
    try {
        return await new Promise(resolve => {
            rl.question(query, resolve);
        });
    } finally {
        rl.close();
    }
}

const getPackageJson = (date, username, description) => {
    const name = description.split(' ').map(e => e.toLowerCase()).join('-');
    return `{
  "name": "${date}-${username}-${name}",
  "version": "1.0.0",
  "description": "${description}",
  "main": "script.ts",
  "scripts": {
    "start": "npx tsx script.ts"
  },
  "keywords": [],
  "author": "${capitalized(username)}",
  "license": "ISC",
  "type": "commonjs",
  "dependencies": {
    "dotenv": "*"
  },
  "devDependencies": {
    "@aws-sdk/credential-providers": "*",
    "aws4": "*"
  }
}`
}

const getScript = (username, description) => {
    const className = description.split(' ').map(capitalized).join('');
    return `import 'dotenv/config';
// @ts-ignore
import {ScriptBase} from '../shared/script-base';
import {ParamsConfig} from '@chargeflow-team/chargeflow-utils-sdk';

export class ${className} extends ScriptBase {
    public getName() {
        return '${description}';
    }

    public getAuthor() {
        return '${capitalized(username)}';
    }

    public getTicket(): string {
        return 'CHA-1111';
    }

    public async dryRun(): Promise<void> {
        await ParamsConfig.loadParameters();
        throw new Error('Not implemented');
    }

    public async wetRunOnPrimary(): Promise<void> {
        await ParamsConfig.loadParameters();
        throw new Error('Not implemented');
    }

    public async wetRunOnSecondary(): Promise<void> {
        await ParamsConfig.loadParameters();
        throw new Error('Not implemented');
    }
}

const script = new ${className}();
script.main();
`;
}

const getReadme = (description) => `
# Purpose

${description}

# Motivation

# Instruction for running this script

\`npx tsx ./script.ts\`
`

async function main() {
    const input = await question('Please describe your fix: ');
    const date = [
        new Date().getUTCFullYear(),
        String(new Date().getUTCMonth() + 1).padStart(2, '0'),
        String(new Date().getUTCDate()).padStart(2, '0'),
    ].join('-');
    const {username} = userInfo();
    const name = input.split(' ').map(e => e.toLowerCase()).join('-');
    const folder = `${date}-${username}-${name}`;
    fs.mkdirSync(`./${folder}`);
    fs.writeFileSync(`./${folder}/script.ts`, getScript(username, input));
    fs.writeFileSync(`./${folder}/package.json`, getPackageJson(date, username, input));
    fs.writeFileSync(`./${folder}/README.md`, getReadme(input));
    fs.writeFileSync(`./${folder}/.env`, `AWS_PROFILE=prod_token`);

    console.log(`Folder ${folder} created with script.ts, package.json, README.md and .env files.`);
}

main();

# Purpose:
The purpose of this script is to fix account id mapped to our entities in the database.

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with

**MONGO_URI**
**MONGO_AUTH**
**MONGO_DB_NAME**

See `.env.example` for an example.

### How this script was tested
[x] Wet run against dev & prod with data diff validation afterwards
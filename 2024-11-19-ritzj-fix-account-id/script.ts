import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { Db, ObjectId } from "mongodb";
import AWS from "aws-sdk";
import { DataAccessUnitOfWork } from "@chargeflow-team/data-access";
import { MongoConfig } from "@chargeflow-team/chargeflow-utils-sdk";
dotenv.config();

type CollectionName = "actions"
      | "alerts"
      | "alertsRegistry"
      | "automation"
      | "businessUnits"
      | "charges"
      | "companies"
      | "disputes"
      | "merchantProfiles"
      | "pastCharges"
      | "pendingCharges"
      | "processors"
      | "reconciliation-scheduler"
      | "seon"
      | "settings"
      | "shopTemplates"
      | "shopifyShopsGateways"
      | "shops"
      | "userInfos";

export class <PERSON>ript extends ScriptBase {

  public db!: Db;
  public getName(): string {
    return "Fix wow-vegas account";
  }

  public getAuthor(): string {
    return "Ritz Jumola";
  }

  public getTicket(): string {
    return "CHA-7941";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow(["MONGO_URI", "MONGO_DB_NAME"]);

      const mongo = await loadMongoClient();
      console.log("Mongo client loaded");

      this.db = mongo.db(process.env.MONGO_DB_NAME);

      const shops = await this.getShopsWithWrongAccountId();

      console.log("Shops with wrong account id:", shops.length);

      if (shops.length === 0) {
        console.log("No shops with wrong account id found");
        return;
      }

      if (isDryRun) {
        console.log("Dry run mode enabled, no changes made to the database");
        return;
      }

      for (const shop of shops) {
        console.log('Processing shop.name', shop.name);
        const account = await this.createEmptyAccount(shop.email);

        console.log('Account created %j', account);

        const collections: CollectionName[] = [
          "actions",
          "alerts",
          "alertsRegistry",
          "automation",
          "businessUnits",
          "charges",
          "companies",
          "disputes",
          "merchantProfiles",
          "pastCharges",
          "pendingCharges",
          "processors",
          "reconciliation-scheduler",
          "seon",
          "settings",
          "shopTemplates",
          "shopifyShopsGateways",
          "shops",
          "userInfos",
        ];

        for (const collection of collections) {
          await this.updateCollectionAccountId(
            collection,
            account._id,
            shop.chargeflow_id,
          );
        }

        await this.assignLegacyDataToAccount(
          account._id,
          shop.email,
          shop.chargeflow_id,
          shop._id,
          shop.customer_id
        )
      }
    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }

  async assignLegacyDataToAccount(
    accountId: ObjectId,
    email: string,
    chargeflowId: ObjectId,
    shopId: ObjectId,
    customerId: ObjectId,
) {
    console.log("Assigning legacy data to account");
    const client = await MongoConfig.getMongoClient();
    const dataAccess = new DataAccessUnitOfWork(client);
    await dataAccess.chargeflowId
        .assignAccountId(chargeflowId, accountId);
    await dataAccess.accountRepository.update(accountId, {
        email: email,
        legacy: { chargeflowId: chargeflowId, shopId: shopId, customerId: customerId },
    });
}

  async updateCollectionAccountId(
    collection: CollectionName,
    accountId: ObjectId,
    chargeflowId: ObjectId
  ) {
    console.log("Updating collection %s", collection);
    let filter: any = { chargeflowId: chargeflowId };
    let setQuery: any = { $set: { accountId } };

    if (collection === "companies") {
      filter = { "users.chargeflowId": chargeflowId };
      setQuery = { $set: { "users.accountId": accountId } };
    }

    if (
      collection === "processors" ||
      collection === "settings" ||
      collection === "shopTemplates" ||
      collection === "shops"
    ) {
      filter = { chargeflow_id: chargeflowId };
    }

    console.log('filter %j', filter);
    console.log('setQuery %j', setQuery);

    const COL = this.db.collection(collection);
    const updateManyRes = await COL.updateMany(filter, setQuery);

    console.log("updateManyRes %j", updateManyRes);
  }
  async createEmptyAccount(email: string) {
    console.log("Creating account for email", email);
    const client = await MongoConfig.getMongoClient();
    const dataAccess = new DataAccessUnitOfWork(client);
    const account = await dataAccess.accountRepository.findByEmail(email);
    if (account.items?.[0]?._id) {
      console.log("Account already exists");
      return account.items?.[0];
    }
    return await dataAccess.accountRepository.add({
      email,
    });
  }

  async getShopsWithWrongAccountId() {
    const SHOPS = this.db.collection("shops");
    return await SHOPS.aggregate([
      {
        $lookup: {
          from: "account", // The name of the `account` collection
          localField: "accountId", // The field in the `shops` collection
          foreignField: "_id", // The field in the `account` collection
          as: "accountInfo", // The output array field
        },
      },
      {
        $match: {
          accountInfo: { $size: 0 }, // Filter where no matching account was found
        },
      },
      {
        $project: {
          accountInfo: 0, // Optional: Remove the joined array from the output
        },
      },
    ]).toArray();
  }
}

const script = new Script();
script.main();

# Purpose:

To convert an existing shopify shop into a cobranded shopify shop

# Motivation:

- User registered to Chargeflow via <NAME_EMAIL> via vendor sso
- User accidentally registered a new Chargeflow shop (bergman) due to a bug in cobranded auth flow (<EMAIL>)
- User registered for a new Ai Assistant <NAME_EMAIL> (this one is not registered in our system yet)
- User has also registered 7 other different shopify shops in the past since 2022

# Desired state
- User wants to use his <NAME_EMAIL> for both Ai-Assistant (and in effect in Chargeflow)
- User wants his shopify shop (bergman) co-branded
- User does not want to see the other old shops he created in the past when he logs in via his new Ai Assistant account

# Running this script

`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with

```
SHOP_ID='SHOP_ID'
VENDOR_ACCOUNT_ID='VENDOR_ACCOUNT_ID'
USER_EMAIL='USER_EMAIL'
MONGO_URI='MONGO_URI'
MONGO_DB_NAME='MONGO_DB_NAME'
```

### How this script was tested
[x] Wet run against dev & prod with data diff validation afterwards
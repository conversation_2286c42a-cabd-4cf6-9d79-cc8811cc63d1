import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { Db, ObjectId } from "mongodb";
import AWS from "aws-sdk";

dotenv.config();

export class <PERSON>ript extends ScriptBase {
  public getName(): string {
    return "Convert 63a42939848ef77817d9b702 to a co-branded shopify shop";
  }

  public getAuthor(): string {
    return "Ritz Jumola";
  }

  public getTicket(): string {
    return "CHA-9245";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async getDb() {
    const mongo = await loadMongoClient();
    console.log("Mongo client loaded");
    return mongo.db(process.env.MONGO_DB_NAME);
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow([
        "MONGO_URI",
        "MONGO_DB_NAME",
        "SHOP_ID",
        "VENDOR_ACCOUNT_ID",
        "USER_EMAIL",
      ]);

      const SHOP_ID = process.env.SHOP_ID;
      const VENDOR_ACCOUNT_ID = process.env.VENDOR_ACCOUNT_ID;
      const USER_EMAIL = process.env.USER_EMAIL;

      if (!SHOP_ID || !VENDOR_ACCOUNT_ID || !USER_EMAIL) {
        console.error("Invalid shop id or vendor account id");
        return;
      }

      const DB = await this.getDb();
      const SHOPS = DB.collection("shops");
      const BILLINGS = DB.collection("billings");
      const ACCOUNT_MAPPINGS = DB.collection("accountMappings");

      // Find shop
      const shopDoc = await SHOPS.findOne({
        _id: new ObjectId(SHOP_ID),
        source: null,
        vendor_account_id: null,
      });

      if (!shopDoc) {
        console.error("Un-cobranded shopify shop not found");
        return;
      }

      // Find billing
      const billingDoc = await BILLINGS.findOne({
        shopId: shopDoc._id,
      });

      if (!billingDoc) {
        console.error(`Billing not found`);
        return;
      }

      console.log(`Un-cobranded shop with billing document found`);

      // Find other account mappings to this email address
      const accountMappings = await ACCOUNT_MAPPINGS.find({
        email: USER_EMAIL,
        chargeflowId: { $ne: shopDoc.chargeflow_id },
      }).toArray();

      console.log('Found other account mappings', accountMappings.length);

      if (this.mode === "dry") {
        console.log("Dry run mode ends here, not changing anything");
        return;
      }

      if (this.mode === "wet-primary") {
        console.log("Running wet primary mode");
        await this.runWriteOps(
          USER_EMAIL,
          VENDOR_ACCOUNT_ID,
          shopDoc._id,
          billingDoc._id,
          shopDoc.chargeflow_id,
        );
      }
    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }

  private async runWriteOps(
    email: string,
    vendorId: string,
    shopId: ObjectId,
    billingId: ObjectId,
    chargeflowId: ObjectId,
  ) {
    const DB = await this.getDb();

    const SHOPS = DB.collection("shops");
    const BILLINGS = DB.collection("billings");
    const ACCOUNT_MAPPINGS = DB.collection("accountMappings");

    // Update shop document source and vendor_account_id
    const updateRes = await SHOPS.updateOne(
      { _id: shopId },
      {
        $set: {
          source: "inboxify",
          vendor_account_id: vendorId,
        },
      }
    );
    console.log("Shop id", shopId, "updated", updateRes);

    // Delete billing document
    const deleteRes = await BILLINGS.deleteOne({
      _id: billingId,
    });
    console.log("Billing id", billingId, "deleted", deleteRes);

    // Delete account mappings
    const deleteAccountMappingsRes = await ACCOUNT_MAPPINGS
      .deleteMany({
        email,
        chargeflowId: { $ne: chargeflowId },
      });
    console.log("Account mappings deleted", deleteAccountMappingsRes);
  }
}

const script = new Script();
script.main();

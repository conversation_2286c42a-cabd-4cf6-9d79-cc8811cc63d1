const { AdminUpdateUserAttributesCommand, AdminCreateUserCommand, InitiateAuthCommand, AdminRespondToAuthChallengeCommand, ListUsersCommand, CognitoIdentityProviderClient } = require('@aws-sdk/client-cognito-identity-provider');
const { fromIni } = require('@aws-sdk/credential-provider-ini');
const { MongoClient } = require('mongodb');

const MONGO_URI = process.env.MONGO_URI;


function generatePassword() {
    const symbols = "!@#$%^&*()_+-=[]{}|'";
    const rand = size => Math.floor(Math.random() * size);
    const randSymbol = () => symbols[rand(symbols.length)];
    const randNumber = () => Math.floor(Math.random() * 10);
    const randLowerCase = () => String.fromCharCode(rand(26) + 97);
    const randUpperCase = () => String.fromCharCode(rand(26) + 65);
    const password = Math.random().toString(36).slice(2)
        .split('');
    for (let i = 0; i < 3; i++) {
        password.splice(rand(password.length), 0, randSymbol());
    }
    password.splice(rand(password.length), 0, String(randNumber()));
    password.splice(rand(password.length), 0, randLowerCase());
    password.splice(rand(password.length), 0, randUpperCase());
    return password.join('');
}

async function createCognitoUserIfNotExists({ poolId, clientId, cognitoSecret, email, name, chargeflowId }) {
    console.log('createCognitoUserIfNotExists', {
        poolId, clientId, cognitoSecret, email, name, chargeflowId
    })
    // try to create the user 
    // if the user already exists, ignore
    // otherwise, create
    let cognitoUsername;
    let finalPassword;

    let identityProvider = new CognitoIdentityProviderClient({
        region: 'us-east-1',
        credentials: fromIni({ profile: 'chargeflow' })
    });

    try {
        finalPassword = generatePassword();
        const res = await identityProvider.send(
            new AdminCreateUserCommand({
                MessageAction: 'SUPPRESS',
                UserPoolId: poolId,
                Username: email,
                UserAttributes: [
                    { Name: 'email', Value: email },
                    { Name: 'name', Value: name },
                    { Name: 'custom:chargeflowId', Value: chargeflowId },
                ],
                TemporaryPassword: finalPassword,
            })
        );

        cognitoUsername = res.User.Username;
        console.log('cognitoUsername', 'a', cognitoUsername)

        // mark as email verified
        await identityProvider.send(
            new AdminUpdateUserAttributesCommand({
                UserPoolId: poolId,
                Username: email,
                UserAttributes: [
                    {
                        Name: 'email_verified',
                        Value: 'true',
                    },
                ],
            })
        )

        const response = await identityProvider.send(
            new InitiateAuthCommand({
                AuthFlow: 'USER_PASSWORD_AUTH',
                AuthParameters: {
                    USERNAME: email,
                    PASSWORD: finalPassword,
                },
                ClientId: clientId,
                ClientMetadata: {
                    cognitoSecret: cognitoSecret,
                }
            })
        )

        console.log('response?.ChallengeName', response?.ChallengeName)

        if (response?.ChallengeName === 'NEW_PASSWORD_REQUIRED') {
            finalPassword = generatePassword();
            console.log('finalPassword', 'b', finalPassword)

            await identityProvider.send(
                new AdminRespondToAuthChallengeCommand({
                    UserPoolId: poolId,
                    ClientId: clientId,
                    ChallengeName: 'NEW_PASSWORD_REQUIRED',
                    ChallengeResponses: {
                        USERNAME: email,
                        NEW_PASSWORD: finalPassword,
                    },
                    Session: response.Session,
                })
            )
        }
    } catch (err) {
        console.log('err', err.code, err.message)
        if (err.code !== 'UsernameExistsException') {
            throw err;
        }
        console.log(`User with email ${email} already exists in cognito`);

        // find user in cognito
        const users = await identityProvider.send(
            new ListUsersCommand({
                UserPoolId: poolId,
                Filter: `email = "${email}"`,
            })
        );

        if (users.Users.length) {
            cognitoUsername = users.Users[0].Username;
            console.log('cognitoUsername', 'c', cognitoUsername)
        }
    } finally {
        console.log('res', res)
        return res;
    }
}

export {
    createCognitoUserIfNotExists,
}
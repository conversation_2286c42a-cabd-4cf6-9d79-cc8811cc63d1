import dotenv from 'dotenv';
import { ClientSession, Db } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { createCognitoUserIfNotExists } from './utils';

dotenv.config();

const MERCHANT_POOL_ID = process.env.AWS_MERCHANT_POOL_ID as string;
const COGNITO_SECRET = process.env.COGNITO_SECRET as string;
const MERCHANT_POOL_CLIENT_ID = process.env.AWS_MERCHANT_POOL_CLIENT_ID as string;
export class ScriptRestoreUser extends ScriptBase {
    db: Db | undefined = undefined;

    public getName(): string {
        return 'Restore a user';
    }

    public getAuthor(): string {
        return 'D<PERSON>ri Vinogradov';
    }

    public getTicket(): string {
        return 'CHA-4408-REVERSE';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async createCognitoUser(email: string, chargeflowId: string,name: string) {
        console.log('Creating cognito user', email);
        if (this.mode !== 'dry') {
            await createCognitoUserIfNotExists({
                poolId: MERCHANT_POOL_ID,
                clientId: MERCHANT_POOL_CLIENT_ID,
                cognitoSecret: COGNITO_SECRET,
                email,
                name,
                chargeflowId
            })
        }
    }

    private async insertDoc(
        db: Db,
        collectionName: string,
        document: object,
        session: ClientSession
    ): Promise<void> {
        console.log(`Restoring ${collectionName}`, JSON.stringify(document));
        if (this.mode !== 'dry') {
            const collection = db.collection(collectionName);
            const res = await collection.insertOne(document, { session });
            console.log(res);
        }
    }

    private async insertDocs(
        db: Db,
        collectionName: string,
        documents: object[],
        session: ClientSession
    ) {
        console.log(`Restoring ${collectionName}`, JSON.stringify(documents));
        if (this.mode !== 'dry') {
            const collection = db.collection(collectionName);
            const res = await collection.insertMany(documents, { session });
            console.log(res);
        }
    }

    private async restoreAddonUser(
        db: Db,
        email: string,
        shop: any,
        backupDb: Db,
        session: ClientSession
    ) {
        // throw not implemented - check where addon users get their name from...
        throw new Error('Not implemented');
        // await this.createCognitoUser(email);

        // const accountMapping = await backupDb.collection('accountMappings').findOne({ email, chargeflowId: shop.chargeflow_id });
        // if (!accountMapping) {
        //     console.log(`No account mapping found for ${email} and ${shop.chargeflow_id}`);
        //     throw new Error(`No account mapping found for ${email} and ${shop.chargeflow_id}`);
        // }
        // await this.insertDoc(db, 'accountMappings', accountMapping, session);

        // const company = await backupDb.collection('companies').findOne({ 'users.email': email, 'users.chargeflowId': shop.chargeflow_id });
        // if (!company) {
        //     console.log(`No company found for ${email} and ${shop.chargeflow_id}`);
        //     throw new Error(`No company found for ${email} and ${shop.chargeflow_id}`);
        // }
        // await this.insertDoc(db, 'companies', company, session);
    }

    private async restoreShop(
        db: Db,
        shop: any,
        backupDb: Db,
        session: ClientSession
    ) {
        const settings = await backupDb.collection('settings').findOne({ chargeflow_id: shop.chargeflow_id });
        if(!settings) {
            console.log(`No settings found for ${shop.chargeflow_id}`);
            throw new Error(`No settings found for ${shop.chargeflow_id}`);
        }
        if(!(settings as any).shop.full_name){
            console.log(`No full name found for ${shop.chargeflow_id}`);
            throw new Error(`No full name found for ${shop.chargeflow_id}`);
        }

        await this.createCognitoUser(shop.email, shop.chargeflow_id, (settings as any).shop.full_name);

        const accountMapping = await backupDb.collection('accountMappings').findOne({ email: shop.email, chargeflowId: shop.chargeflow_id });
        if (!accountMapping) {
            console.log(`No account mapping found for ${shop.email} and ${shop.chargeflow_id}`);
            throw new Error(`No account mapping found for ${shop.email} and ${shop.chargeflow_id}`);
        }
        await this.insertDoc(db, 'accountMappings', accountMapping, session);

        const chargeflowIdDoc = await backupDb.collection('chargeflow_ids').findOne({ _id: shop.chargeflow_id });
        if (!chargeflowIdDoc) {
            console.log(`No chargeflow id found for ${shop.chargeflow_id}`);
            throw new Error(`No chargeflow id found for ${shop.chargeflow_id}`);
        }
        await this.insertDoc(db, 'chargeflow_ids', chargeflowIdDoc, session);

        //const billing = await backupDb.collection('billings').findOne({ chargeflowId: shop.chargeflow_id });
        //if (!billing) {
            // console.log(`No billing found for ${shop.chargeflow_id}`);
            // throw new Error(`No billing found for ${shop.chargeflow_id}`);
        //}
        //await this.insertDoc(db, 'billings', billing, session);

        const companies = await backupDb.collection('companies').find({ 'users.email': shop.email, 'users.chargeflowId': shop.chargeflow_id }).toArray();
        await this.insertDocs(db, 'companies', companies, session);

        const shopDoc = await backupDb.collection('shops').findOne({ name: shop.name });
        if (!shopDoc) {
            console.log(`No shop found for ${shop.name}`);
            throw new Error(`No shop found for ${shop.name}`);
        }
        await this.insertDoc(db, 'shops', shopDoc, session);

        const customer = await backupDb.collection('customers').findOne({ _id: shop.customer_id });
        if (!customer) {
            console.log(`No customer found for ${shop.customer_id}`);
            throw new Error(`No customer found for ${shop.customer_id}`);
        }
        await this.insertDoc(db, 'customers', customer, session);

        // const userInfo = await backupDb.collection('userInfos').findOne({ chargeflowId: shop.chargeflow_id });
        // if (!userInfo) {
        //     console.log(`No user info found for ${shop.chargeflow_id}`);
        //     throw new Error(`No user info found for ${shop.chargeflow_id}`);
        // }
        // await this.insertDoc(db, 'userInfos', userInfo, session);

        // const settings = await backupDb.collection('settings').findOne({ chargeflow_id: shop.chargeflow_id });
        if (!settings) {
            console.log(`No settings found for ${shop.chargeflow_id}`);
            throw new Error(`No settings found for ${shop.chargeflow_id}`);
        }
        await this.insertDoc(db, 'settings', settings, session);

        const disputes = await backupDb.collection('disputes').find({ chargeflowId: shop.chargeflow_id }).toArray();
        await this.insertDocs(db, 'disputes', disputes, session);

        const processors = await backupDb.collection('processors').find({ chargeflow_id: shop.chargeflow_id }).toArray();
        await this.insertDocs(db, 'processors', processors, session);

        const campaigns = await backupDb.collection('campaign').find({ cognitoUsername: shop.email }).toArray();
        await this.insertDocs(db, 'campaign', campaigns, session);
    }

    private async logic(context: ScriptContext, isDryRun: boolean) {
        const shopName = context.args[1];
        const email = context.args[2];
        const isAddOn = context.args[3] === 'add-on';
        const client = await loadMongoClient();
        const db = client.db('chargeflow_api');
        const backupClient = await loadMongoClient(process.env.BACKUP_MONGO_URI);
        const backupDb = backupClient.db('chargeflow_api');
        const session = client.startSession();

        if (!shopName) {
            throw new Error('Shop name is required.');
        }

        const shopsCollection = backupDb.collection('shops');

        const shop = await shopsCollection.findOne({ name: shopName });
        if (!shop) {
            throw new Error('No shop found.');
        }

        try {
            await session.withTransaction(async () => {
                if (isAddOn) {
                    if (!email) {
                        throw new Error('Email is required for add-on user.');
                    }
                    await this.restoreAddonUser(db, email, shop, backupDb, session);
                } else {
                    await this.restoreShop(db, shop, backupDb, session);
                }
            });
        } catch (err) {
            console.error('Transaction aborted due to an error: ', err);
            if (err instanceof Error) {
                throw err;
            }
        } finally {
            await session.endSession();
            client.close();
            backupClient.close();
        }
    }
}

const script = new ScriptRestoreUser();
script.main();

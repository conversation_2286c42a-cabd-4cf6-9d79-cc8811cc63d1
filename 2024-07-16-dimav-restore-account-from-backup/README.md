# Purpose:
Restore a user that was previously backed up

# Motivation:
To restore a user completely into the system from a backup

# Running this script
npx ts-node ./script.ts <dry or wet-secondary or wet-primary> <shop name> <email address> <'add-on' for add-on user>| tee log.txt
NOTE: a .env file is expected to be present in CWD with 
MONGO_URI, 
BACKUP_MONGO_URI, 
MERCHANT_POOL_ID
COGNITO_SECRET,
AWS_MERCHANT_POOL_CLIENT_ID

User must also have an active AWS session with permission to create and enable a user in the merchant cognito pool.

### How this script was tested
[x] Wet run against dev & test







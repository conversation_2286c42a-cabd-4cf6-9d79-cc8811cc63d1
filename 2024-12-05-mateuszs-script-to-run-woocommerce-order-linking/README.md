# Purpose
This script resends disputes to the OrderLinkingQueue via SQS in relation to back-fill woocommerce orders for historical disputes.
https://linear.app/chargeflow/issue/CHA-10970/migration-script-for-woocommerce-historical-data

## Motivation
We need to resend disputes to the OrderLinkingQueue to ensure that the orders were linked to the disputes.

## Running this script
`npx ts-node ./script.ts <dry | wet-primary>`

## Modes
- dry - only logs the total number of disputes that are eligible for resending
- wet-primary - sends disputes to the OrderLinkingQueue via SQS

## Required env variables
MONGO_URI=<mongo_uri_string>
QUEUE_URL=<sqs queue url>

import { MongoClient } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase } from "../shared/script-base";
import { ObjectId } from 'mongodb';
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import { DataAccessUnitOfWork } from "@chargeflow-team/data-access";
import _ from "lodash";
import { IMongoUnifiedDisputeObject } from "@chargeflow-team/data-access/dist/Dispute/Types";

export class ResendOrderLinking extends ScriptBase {
  public getName(): string {
    return "Resend disputes to OrderLinkingQueue";
  }

  public getAuthor(): string {
    return "Mateusz Smagiel";
  }

  public getTicket(): string {
    return "cha-10970-migration-script-for-woocommerce-historical-data";
  }

  async dryRun(): Promise<void> {
    await this.logic();
  }

  async wetRunOnPrimary(): Promise<void> {
    await this.logic();
  }

  async wetRunOnSecondary(): Promise<void> {
    throw new Error("Method is not implemented.");
  }

  async logic() {
    if (!process.env.QUEUE_URL) {
        throw new Error("QUEUE_URL is not set");
    }
    console.log(`QUEUE_URL: ${process.env.QUEUE_URL}`);
    const client: MongoClient = await loadMongoClient();
    const dataAccess = new DataAccessUnitOfWork(client);
    const orderPlatforms = await dataAccess.orderPlatform.getConnectedBySource("wooCommerce", 1, 100)
    const distinctOrderPlatforms = _.uniqWith(orderPlatforms.items,(a, b)=> a.chargeflowId === b.chargeflowId);
    console.log(`Found ${distinctOrderPlatforms.length} orderPlatforms`);
    const disputesToOrderLinking: IMongoUnifiedDisputeObject[]  = [];
    for (const orderPlatform of distinctOrderPlatforms) {
      const disputes = client.db("chargeflow_api").collection<IMongoUnifiedDisputeObject>("disputes");
      const result:IMongoUnifiedDisputeObject[] = await disputes.find({ chargeflowId: new ObjectId(orderPlatform.chargeflowId), 'order.id': null }).toArray()
      console.log(`Found ${result.length} disputes for chargeflowId: ${orderPlatform.chargeflowId}`);
      disputesToOrderLinking.push(...result);
    }
    const SQS = new SQSClient();
    const queueUrl = process.env.QUEUE_URL;
    console.log(`Disputes to order linking: ${disputesToOrderLinking.length}`);
    const promises = disputesToOrderLinking.map(dispute => {
      const orderLinkingCommand = {
        chargeflowId: dispute.chargeflowId,
        disputeId: dispute._id,
        source: 'auto',
        disputePropertiesForLinking: {
          orderId: dispute.transaction?.orderId,
          transactionId: dispute.transaction?.id,
        },
      }
      const params = {
          MessageBody: JSON.stringify(orderLinkingCommand),
          QueueUrl: queueUrl
      };
      const command = new SendMessageCommand(params);
      if (this.mode === 'dry') {
        console.log("Dry run completed");
        return;
      }
      return SQS.send(command);
    });
    const response = await Promise.allSettled(promises);
    const success = response.filter(r => r.status === 'fulfilled').length;
    const failed = response.filter(r => r.status === 'rejected').length;
    console.log(`Sent ${success} disputes to the queue`);
    console.log(`Failed to send ${failed} disputes to the queue`);
  }
}
const script = new ResendOrderLinking();
script.main();

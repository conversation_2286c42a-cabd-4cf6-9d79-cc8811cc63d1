import { ObjectId, AnyBulkWriteOperation, Document, MongoClient } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext, ScriptMode } from '../shared/script-base';
import { ScriptDataAccess } from './data-access';
import midsData from './mids-file.json'
import { AlertsRegistry, MerchantProfile, MidDataItem} from './types';

class Script extends ScriptBase {

  public getName(): string {
    return 'Migrate alert customers from CBHit,';
  }

  public getAuthor(): string {
    return '<PERSON><PERSON>';
  }

  public getTicket(): string {
    return 'CHA-2163';
  }

  async dryRun(context: ScriptContext) {
    await this.logic(context);
  }

  wetRunOnSecondary(_context: ScriptContext): Promise<void> {
    throw new Error('Method not implemented.');
  }

  async wetRunOnPrimary(context: ScriptContext) {
    await this.logic(context);
  }

  private async logic(ctx: ScriptContext) {
    const mode = (ctx.args[0] ?? 'dry') as ScriptMode;
    const client: MongoClient = await loadMongoClient();

    const midsDataList: MidDataItem[] = midsData; //Imported from a JSON file
    console.log('Processing %s MIDs', midsDataList.length);

    const MIDsPerCFId: Record<string, MidDataItem[]> = 
      this.mapMidsToChargeflowIds(midsDataList);
    console.log('Processing %s chargeflowIds', Object.keys(MIDsPerCFId).length);
    Object.keys(MIDsPerCFId).map(id => {console.log(new ObjectId(id))});

    const mappedAlertsRegistry = await this.mapAlertsRegistryData(client, MIDsPerCFId);
    const mappedMerchantProfiles = await this.mapMerchantProfilesData(client, midsDataList);
    const updatePaymentMethodOps = await this.createUpdatePaymentMethodOps(client, midsDataList);

    if (mode === 'dry') {
      console.log('Dry run mode');
      console.log('Creating the following alerts registries: ', JSON.stringify(mappedAlertsRegistry));
      console.log('Creating the following merchant profiles: ', JSON.stringify(mappedMerchantProfiles));
      console.log('Updating query for company collections: ', JSON.stringify(updatePaymentMethodOps));
    } else {
      await this.createAlertsRegistry(client, mappedAlertsRegistry);
      await this.createMerchantProfiles(client, mappedMerchantProfiles);
      await this.updateCompany(client, updatePaymentMethodOps)
    }
  }

  private async updateCompany(client: MongoClient, updatePaymentMethodOps : AnyBulkWriteOperation<Document>[] | []) {
    const modifiedCount = await this.updatePaymentMethod(client, updatePaymentMethodOps)
    console.log('Updated payment method for %s companies', modifiedCount);
  }

  private async mapAlertsRegistryData(client: MongoClient, MIDsPerCFId: Record<string, MidDataItem[]>) : 
    Promise<AlertsRegistry[]> {
      const alertsRegistry: AlertsRegistry[] = [];

      for (const chargeflowId of Object.keys(MIDsPerCFId)){
          const authorizedAt = MIDsPerCFId[chargeflowId][0].PaymentMethodDate ?? null;
          const accountId = await this.findAccountIdByChargeflowId(client, chargeflowId);
          alertsRegistry.push({
              chargeflowId: new ObjectId(chargeflowId),
              accountId: accountId ? new ObjectId(accountId) : null,
              authorizedAt: authorizedAt ? new Date(authorizedAt) : null,
              dateCreated: this.findDateCreated(MIDsPerCFId[chargeflowId]),
              dateUpdated: authorizedAt ? new Date(authorizedAt) : null,
          })
      }
      
      return alertsRegistry;
  }

  private async findAccountIdByChargeflowId(client: MongoClient, chargeflowId: string): Promise<string | null> {
    const dataAccess = new ScriptDataAccess(client as any);
    return await dataAccess.findAccountIdByChargeflowId(chargeflowId);
  }

  private async mapMerchantProfilesData(client: MongoClient,data : MidDataItem[]) : Promise<MerchantProfile[]> {
    const merchantProfiles: MerchantProfile[] = [];

    for(const item of data) {
      const accountId = await this.findAccountIdByChargeflowId(client, item.ChargeflowID);

      merchantProfiles.push( {
          chargeflowId: new ObjectId(item.ChargeflowID),
          accountId: accountId ? new ObjectId(accountId) : null,
          dateCreated: item.EnrollmentDate ? new Date(item.EnrollmentDate) : null,
          dateUpdated: null,
          isLinked: false,
          isActive: false,
          processor: item.PSP,
          statementDescriptor: item.Descriptor
      });
    }

    return merchantProfiles;
  }

  private findDateCreated(MIDs: MidDataItem[]) : Date | null {
    const startDate = MIDs[0]?.EnrollmentDate ?? null;

    return MIDs.reduce((acc, item) => {
        const dateCreated = item.EnrollmentDate;
        if (dateCreated) {
          const currentDate = new Date(dateCreated);
          acc = acc ? (currentDate < acc ? currentDate : acc) : currentDate;
        }

        return acc;
    }, startDate ? new Date(startDate) : null)
  }

  private async createAlertsRegistry(client: MongoClient, alertsRegistrys: AlertsRegistry[]) {
    try {
        client
          .db('chargeflow_api')
          .collection('alertsRegistry')
          .insertMany(alertsRegistrys)
          .then((result) => {
            console.log('Alerts registries created: ', result.insertedIds);
          });
    }
    catch (error) {
        console.error('Failed to create alerts registry %s', error);
    }
  }

  private async createMerchantProfiles(client: MongoClient, merchantProfiles: MerchantProfile[]) {
    try {
        await client
          .db('chargeflow_api')
          .collection('merchantProfiles')
          .insertMany(merchantProfiles)
          .then((result) => {
            console.log('Merchant profiles created: ', result.insertedIds);
          });
    }
    catch (error) {
        console.error('Failed to create merchant profiles %s', error);
    }
  }

  private mapMidsToChargeflowIds(midsData: MidDataItem[]) {
    const MIDsPerCFId: Record<string, MidDataItem[]> = 
      midsData.reduce((acc, item) => {
        const chargeflowId = item.ChargeflowID;
        if (acc[chargeflowId]) {
            acc[chargeflowId].push(item);
        }
        else {
            acc[chargeflowId] = [item];
        }
        return acc;
      }, {} as Record<string, MidDataItem[]>);

    return MIDsPerCFId;
  }

  private async getUserEmail(client: MongoClient, item: MidDataItem) : Promise< string | null | undefined> {
    try {
        return client
            .db('chargeflow_api')
            .collection('companies')
            .findOne(
              { 'users.chargeflowId': new ObjectId(item.ChargeflowID) },
              {
                'projection': {
                    '_id': 0,
                    'users.email': 1,
                },
              },
            ).then((data) => data?.users?.email);
        
    }
    catch (error) {
        console.error('Failed to find user email %s', error);
        return null;
    }
  }
  
  private async getPortalUrl(client: MongoClient, item: MidDataItem) {
    const userEmail = await this.getUserEmail(client, item);
    return userEmail ? 
      `https://billing.stripe.com/p/login/3cs3cV0P9gkFeMo7ss?prefilled_email=${userEmail}` : 
      null;
  }

  private async createUpdatePaymentMethodOps(client: MongoClient, data: MidDataItem[]): Promise<AnyBulkWriteOperation<Document>[]> {
    const handledChargeflowIds: string[] = [];
    const updatePaymentMethodOps: AnyBulkWriteOperation<Document>[] = [];

    for (const item of data) {
      // We can have multiple MIDs for the same ChargeflowID, so we need to make sure we only update each ChargeflowID once
      if (handledChargeflowIds.includes(item.ChargeflowID)) {
        continue;
      }
      handledChargeflowIds.push(item.ChargeflowID);

      const customerPortalUrl = await this.getPortalUrl(client,item);
      updatePaymentMethodOps.push({
        updateOne: {
          filter: { 'users.chargeflowId': new ObjectId(item.ChargeflowID) as ObjectId },
          update: {
            $set: {
              'users.billing.alertsSubscription': {
                customerPortalUrl,
                dateCreated: item.PaymentMethodDate ? new Date(item.PaymentMethodDate) : null,
                id: item.StripeCustomerID,
                paymentMethodId: item.PaymentMethodID,
                source: "Stripe",
                isSourceExpired: false,
              }
            }
          }
        }
      });
    }

    return updatePaymentMethodOps;
  }

  private async updatePaymentMethod(client: MongoClient, updateOps : AnyBulkWriteOperation<Document>[] | []): Promise<number> {
      try {
        return client
          .db('chargeflow_api')
          .collection('companies')
          .bulkWrite(
            updateOps,
            { ordered: false })
          .then((result) => result.modifiedCount);
      }
      catch (error) {
        console.error('Failed to update payment method %s', error);
        return 0;
      }
  }
}


const script = new Script();
script.main();

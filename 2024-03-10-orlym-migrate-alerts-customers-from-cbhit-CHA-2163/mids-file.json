[{"Descriptor": "BOOST COLLECTIVE", "PSP": "Stripe", "ChargeflowID": "33e885649b3ea300d23097b1", "EnrollmentDate": "August 17, 2023", "MerchantId": "b9b69bc7-5fdf-4015-8f6f-ff16a43b779d", "StripeCustomerID": "cus_NBbGfn4SPAQsQn", "PaymentMethodID": "pm_1NLaCaFB8zXks4nkSuaxnx35", "PaymentMethodDate": "June 21, 2023"}, {"Descriptor": "Legendary Courses", "PSP": "PaymentCloud", "ChargeflowID": "4d47f4de2bc4699dba55c288", "EnrollmentDate": "November 13, 2023", "MerchantId": "a3d4b3a7-8d5f-437c-bd69-85793298c7f6", "StripeCustomerID": "cus_OXn5lEDvPhYsJz", "PaymentMethodID": "pm_1NkhUsFB8zXks4nkxrFiHPa0", "PaymentMethodDate": "August 30, 2023"}, {"Descriptor": "HU2.IO", "PSP": "PaymentCloud", "ChargeflowID": "4d47f4de2bc4699dba55c288", "EnrollmentDate": "November 13, 2023", "MerchantId": "55958d07-8125-4ab8-a16f-762096a2e536", "StripeCustomerID": "cus_OXn5lEDvPhYsJz", "PaymentMethodID": "pm_1NkhUsFB8zXks4nkxrFiHPa0", "PaymentMethodDate": "August 30, 2023"}, {"Descriptor": "IM FOLLOWING", "PSP": "PaymentCloud", "ChargeflowID": "4d47f4de2bc4699dba55c288", "EnrollmentDate": "November 13, 2023", "MerchantId": "f9ff1c5a-b2b3-4559-b0ef-cd490a5e32dd", "StripeCustomerID": "cus_OXn5lEDvPhYsJz", "PaymentMethodID": "pm_1NkhUsFB8zXks4nkxrFiHPa0", "PaymentMethodDate": "August 30, 2023"}, {"Descriptor": "WWW.THEREALWORLD.AI", "PSP": "PaymentCloud", "ChargeflowID": "4d47f4de2bc4699dba55c288", "EnrollmentDate": "November 13, 2023", "MerchantId": "1728de35-6e5c-4572-8d5c-c0d7eefca631", "StripeCustomerID": "cus_OXn5lEDvPhYsJz", "PaymentMethodID": "pm_1NkhUsFB8zXks4nkxrFiHPa0", "PaymentMethodDate": "August 30, 2023"}, {"Descriptor": "THERLWORLD.COM", "PSP": "paymentcloud2", "ChargeflowID": "4d47f4de2bc4699dba55c288", "EnrollmentDate": "September 26, 2023", "MerchantId": "0a600bf2-4511-41a4-b2ea-fc720f6ed396", "StripeCustomerID": "cus_OXn5lEDvPhYsJz", "PaymentMethodID": "pm_1NkhUsFB8zXks4nkxrFiHPa0", "PaymentMethodDate": "August 30, 2023"}, {"Descriptor": "JOINTHEREALWORLD.COM", "PSP": "PaymentCloud", "ChargeflowID": "4d47f4de2bc4699dba55c288", "EnrollmentDate": "November 24, 2023", "MerchantId": "2b4661cc-2fd0-4e1b-ad6f-7660079d5511", "StripeCustomerID": "cus_OXn5lEDvPhYsJz", "PaymentMethodID": "pm_1NkhUsFB8zXks4nkxrFiHPa0", "PaymentMethodDate": "August 30, 2023"}, {"Descriptor": "RLPORTAL.CO", "PSP": "Stripe", "ChargeflowID": "4d47f4de2bc4699dba55c288", "EnrollmentDate": "November 24, 2023", "MerchantId": "9e85bc52-bb24-4cc2-8972-fe761d8262a8", "StripeCustomerID": "cus_OXn5lEDvPhYsJz", "PaymentMethodID": "pm_1NkhUsFB8zXks4nkxrFiHPa0", "PaymentMethodDate": "August 30, 2023"}, {"Descriptor": "BIOHARVEST INC", "PSP": "Stripe", "ChargeflowID": "621de0079681e6ede296b57e", "EnrollmentDate": "December 15, 2023", "MerchantId": "6c5f5d6c-c6b0-4446-9027-c3d433527009", "StripeCustomerID": "cus_OfSA65P3orp6L5", "PaymentMethodID": "pm_1O1lXRFB8zXks4nkyKyfuJIY", "PaymentMethodDate": "October 16, 2023"}, {"Descriptor": "SP VINIA.COM", "PSP": "Shopify Payments", "ChargeflowID": "621de0079681e6ede296b57e", "EnrollmentDate": "August 24, 2023", "MerchantId": "054ddbdf-3103-462f-ae5d-1e6cf4d1fbd1", "StripeCustomerID": "cus_OfSA65P3orp6L5", "PaymentMethodID": "pm_1O1lXRFB8zXks4nkyKyfuJIY", "PaymentMethodDate": "October 16, 2023"}, {"Descriptor": "VINIA - BIOHARVEST", "PSP": "Stripe", "ChargeflowID": "621de0079681e6ede296b57e", "EnrollmentDate": "December 15, 2023", "MerchantId": "2c53a397-f809-4c51-b542-a9cb61292e6f", "StripeCustomerID": "cus_OfSA65P3orp6L5", "PaymentMethodID": "pm_1O1lXRFB8zXks4nkyKyfuJIY", "PaymentMethodDate": "October 16, 2023"}, {"Descriptor": "SP INTERIORICONS", "PSP": "Shopify Payments", "ChargeflowID": "6278eb0f49ca01252354cc77", "EnrollmentDate": "September 18, 2023", "MerchantId": "694c1ce7-2a97-4a7a-81d0-bd2de71f2810", "StripeCustomerID": "cus_OatQkcNZy6b2RP", "PaymentMethodID": "pm_1O9PjvFB8zXks4nklbHeyyZ6", "PaymentMethodDate": "November 06, 2023"}, {"Descriptor": "LUXUS", "PSP": "Stripe", "ChargeflowID": "6283856600044e45fb3bbbbb", "EnrollmentDate": "August 26, 2023", "MerchantId": "bf327a40-e224-4049-a4ca-1954400163be", "StripeCustomerID": "cus_Oipf4KR5k7fzbL", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "SP BLUNTTIQUE", "PSP": "Shopify Payments", "ChargeflowID": "634afc7075ad75f003d84778", "EnrollmentDate": "January 02, 2024", "MerchantId": "320466f9-6e5f-4a7a-8ab0-48dfcd615c71", "StripeCustomerID": "cus_PD6fvvojUySkHZ", "PaymentMethodID": "pm_1OPKOJFB8zXks4nk6IuKS5r6", "PaymentMethodDate": "December 20, 2023"}, {"Descriptor": "SP ORTHOFIT", "PSP": "Shopify Payments", "ChargeflowID": "63ad108b8587e476cec680fd", "EnrollmentDate": "October 13, 2023", "MerchantId": "7c673cd2-208c-4e81-9320-7a217a3f7e95", "StripeCustomerID": "cus_OkzDkT4qhF3gAn", "PaymentMethodID": "pm_1NxhbDFB8zXks4nki1Ywd3Wn", "PaymentMethodDate": "October 05, 2023"}, {"Descriptor": "SP THE MOUSE MERCH B", "PSP": "Shopify Payments", "ChargeflowID": "63bc36aa63e9dc71a9e9f3d6", "EnrollmentDate": "January 11, 2024", "MerchantId": "c8ae2c92-5341-4f82-87b1-1252460<PERSON>bee", "StripeCustomerID": "cus_P8s28J6HRuZ2Wt", "PaymentMethodID": "pm_1ONgGPFB8zXks4nk5gapx6Al", "PaymentMethodDate": "December 15, 2023"}, {"Descriptor": "JULEZSUPPLY", "PSP": "Stripe", "ChargeflowID": "63bd9f88d1310b77d7d7f6c6", "EnrollmentDate": null, "MerchantId": "df4cba3e-eb0b-42a4-8d9b-e3c30e97ce2f", "StripeCustomerID": "cus_PQ3YKGPDsLcXyi", "PaymentMethodID": "pm_1OmXMwFB8zXks4nkXCGaFVHM", "PaymentMethodDate": "February 22, 2024"}, {"Descriptor": "MARNETIC", "PSP": "Stripe", "ChargeflowID": "63be8536419f6ac3164c13fa", "EnrollmentDate": "August 24, 2023", "MerchantId": "1a40e805-179f-4e12-8dfd-4b182a8bbcdc", "StripeCustomerID": "cus_NBjdMwXpbZ1VKK", "PaymentMethodID": "pm_1OEtdVFB8zXks4nkonDMiMK1", "PaymentMethodDate": "November 21, 2023"}, {"Descriptor": "SP MSDRESSLY.COM", "PSP": "Shopify Payments", "ChargeflowID": "63e5d6d643a8fcfbbc769466", "EnrollmentDate": "December 26, 2023", "MerchantId": "6ed7531d-f372-4392-8a93-1a9d9cef25a1", "StripeCustomerID": "cus_PF2o6fOjZ41s0u", "PaymentMethodID": "pm_1OUQt0FB8zXks4nktl9m61i8", "PaymentMethodDate": "January 03, 2024"}, {"Descriptor": "SP PRICE CONNECTION", "PSP": "Shopify Payments", "ChargeflowID": "63e7e06c2885fd180ac3c153", "EnrollmentDate": "November 02, 2023", "MerchantId": "5978af82-7cd4-45fa-8533-6116756fcbf2", "StripeCustomerID": "cus_OrzDXcMnb4hjn6", "PaymentMethodID": "pm_1O5IycFB8zXks4nk1HHbrokj", "PaymentMethodDate": "October 26, 2023"}, {"Descriptor": "SP AVANQUIL", "PSP": "Shopify Payments", "ChargeflowID": "63f515c557f1626fd6f84cb1", "EnrollmentDate": "October 11, 2023", "MerchantId": "9687c413-c013-49c5-b93d-1cf3e3395e69", "StripeCustomerID": "cus_OdqRB4SbeztjWk", "PaymentMethodID": "pm_1Nqi1fFB8zXks4nkOObGlpdD", "PaymentMethodDate": "September 15, 2023"}, {"Descriptor": "SP BILLINGTON FARMS", "PSP": "Shopify Payments", "ChargeflowID": "63f82a4a7cd602379e3eff70", "EnrollmentDate": "February 12, 2024", "MerchantId": "a5eece26-2501-4567-8c0a-80fc2f24702e", "StripeCustomerID": "cus_PNTVLLQLstH1Lw", "PaymentMethodID": "pm_1OdbvbFB8zXks4nkCNwNXrrj", "PaymentMethodDate": "January 16, 2024"}, {"Descriptor": "FIRA-LA", "PSP": "Shopify Payments", "ChargeflowID": "640f27b13c684ef74854fc29", "EnrollmentDate": "August 15, 2023", "MerchantId": "41330637-cbfd-4e0a-b3cf-59afbefd797b", "StripeCustomerID": "cus_P8iwF3fVoXAvU4", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "FIR3", "PSP": "Shopify Payments", "ChargeflowID": "640f27b13c684ef74854fc29", "EnrollmentDate": "August 24, 2023", "MerchantId": "109a205e-8db8-40df-ac0a-899473ad3716", "StripeCustomerID": "cus_P8iwF3fVoXAvU4", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "SP FREEBIRD", "PSP": "Shopify Payments", "ChargeflowID": "641b960b98fd0556906c9eaf", "EnrollmentDate": "October 12, 2023", "MerchantId": "74f91f6d-6747-418e-841e-58c0593236a1", "StripeCustomerID": "cus_OaykLvm3RNYJLS", "PaymentMethodID": "pm_1NnmmiFB8zXks4nkeMjqydDi", "PaymentMethodDate": "September 07, 2023"}, {"Descriptor": "SP EDGESSENTIALS", "PSP": "Shopify Payments", "ChargeflowID": "641d35e3af42faac4fdb3bf6", "EnrollmentDate": null, "MerchantId": "6a5eb37b-484b-4d67-aeb3-0af2417e0d86", "StripeCustomerID": "cus_PCNXijRZj2Odc9", "PaymentMethodID": "pm_1OVJbbFB8zXks4nk78MvDglQ", "PaymentMethodDate": "January 05, 2024"}, {"Descriptor": "ViewMyScores", "PSP": "Stripe", "ChargeflowID": "642c752237253108a85d0f85", "EnrollmentDate": "August 15, 2023", "MerchantId": "0638bcf0-8ecc-4aff-9ba1-1725870e9928", "StripeCustomerID": "cus_Nv33HxtP8aIcmf", "PaymentMethodID": "pm_1NItGDFB8zXks4nku8y7touh", "PaymentMethodDate": "June 14, 2023"}, {"Descriptor": "ViewMyScores", "PSP": "Ma<PERSON><PERSON>", "ChargeflowID": "642c752237253108a85d0f85", "EnrollmentDate": "August 24, 2023", "MerchantId": "0638bcf0-8ecc-4aff-9ba1-1725870e9928", "StripeCustomerID": "cus_Nv33HxtP8aIcmf", "PaymentMethodID": "pm_1NItGDFB8zXks4nku8y7touh", "PaymentMethodDate": "June 14, 2023"}, {"Descriptor": "SP MAGICJOHN", "PSP": "Shopify Payments", "ChargeflowID": "642fbe979609a4b25fc1a85e", "EnrollmentDate": "November 13, 2023", "MerchantId": "27453005-cd5f-4dc9-bc91-bf48291b2814", "StripeCustomerID": "cus_OxFwhPR6aOjTtm", "PaymentMethodID": "pm_1O9uCGFB8zXks4nkTJWvrcPx", "PaymentMethodDate": "November 07, 2023"}, {"Descriptor": "www.thekliniqueipl.com", "PSP": "Stripe", "ChargeflowID": "645744a5f6234c1941387923", "EnrollmentDate": "August 24, 2023", "MerchantId": "83b393dc-66b1-4894-8974-2da27ee40790", "StripeCustomerID": "cus_NqiXekwhR6lIN3", "PaymentMethodID": "pm_1NItGEFB8zXks4nkrjKTa06Q", "PaymentMethodDate": "June 14, 2023"}, {"Descriptor": "circuitmess.com", "PSP": "Stripe", "ChargeflowID": "645b56d93e311c437d67c46a", "EnrollmentDate": "September 13, 2023", "MerchantId": "df1b725d-bb17-4ac3-b9f0-95de5d9b8169", "StripeCustomerID": "cus_OmQlg9I8msPlR4", "PaymentMethodID": "pm_1O1ldbFB8zXks4nk8zX94XCm", "PaymentMethodDate": "October 16, 2023"}, {"Descriptor": "SP Rustic Luxe Designs", "PSP": "Shopify Payments", "ChargeflowID": "64625f60e69e4ca9f827bc79", "EnrollmentDate": "October 31, 2023", "MerchantId": "a3360d48-691e-4969-a1ee-2ede76d625fc", "StripeCustomerID": "cus_OiR6QU3PKm0rZ2", "PaymentMethodID": "pm_1O56RPFB8zXks4nkKSY635ed", "PaymentMethodDate": "October 25, 2023"}, {"Descriptor": "SP THE LUMI SHOP", "PSP": "Shopify Payments", "ChargeflowID": "646e39d5de5130eb08197e7f", "EnrollmentDate": "December 01, 2023", "MerchantId": "3280f4c3-3cac-41c8-982d-900b1e6711f9", "StripeCustomerID": "cus_OifVYn7AqAcJnw", "PaymentMethodID": "pm_1OHYvfFB8zXks4nkbybmpddM", "PaymentMethodDate": "November 28, 2023"}, {"Descriptor": "WAB STUDIO", "PSP": "Stripe", "ChargeflowID": "6478f5a43de5ce8eaa2b4519", "EnrollmentDate": "October 11, 2023", "MerchantId": "bc31a1af-dbad-419c-91c1-8e96ac3cb290", "StripeCustomerID": "cus_O0NmN5nvdyGKyL", "PaymentMethodID": "pm_1NItGCFB8zXks4nkBLtwO8W1", "PaymentMethodDate": "June 14, 2023"}, {"Descriptor": "TECHNOLOGY AVENUE", "PSP": "Stripe", "ChargeflowID": "6478f5a43de5ce8eaa2b4519", "EnrollmentDate": "October 13, 2023", "MerchantId": "6ebb13e5-296a-40c9-bce4-019660fbaf6b", "StripeCustomerID": "cus_O0NmN5nvdyGKyL", "PaymentMethodID": "pm_1NItGCFB8zXks4nkBLtwO8W1", "PaymentMethodDate": "June 14, 2023"}, {"Descriptor": "SQ  JumpTheLine", "PSP": "Square", "ChargeflowID": "6499dc7d880ba504347e1e5e", "EnrollmentDate": "December 07, 2023", "MerchantId": "1af489d3-6731-418b-85c6-b9e04a60f7b7", "StripeCustomerID": "cus_O9yVFThWUWsVaS", "PaymentMethodID": "pm_1NkGdzFB8zXks4nkuivrKMAG", "PaymentMethodDate": "August 29, 2023"}, {"Descriptor": "SQ  JTLTechnologies", "PSP": "Square", "ChargeflowID": "6499dc7d880ba504347e1e5e", "EnrollmentDate": "November 29, 2023", "MerchantId": "e38cdbd0-aa87-48bb-961d-06089ab25718", "StripeCustomerID": "cus_O9yVFThWUWsVaS", "PaymentMethodID": "pm_1NkGdzFB8zXks4nkuivrKMAG", "PaymentMethodDate": "August 29, 2023"}, {"Descriptor": "Jumptheline", "PSP": "Stripe", "ChargeflowID": "6499dc7d880ba504347e1e5e", "EnrollmentDate": "August 24, 2023", "MerchantId": "e7fc83e0-ca3a-4da5-91c6-65aa3c323f7d", "StripeCustomerID": "cus_O9yVFThWUWsVaS", "PaymentMethodID": "pm_1NkGdzFB8zXks4nkuivrKMAG", "PaymentMethodDate": "August 29, 2023"}, {"Descriptor": "R3VIEWBLOG", "PSP": "Stripe", "ChargeflowID": "649d6c301e0368be17bbc34a", "EnrollmentDate": "August 29, 2023", "MerchantId": "761c1764-dfc1-4117-92f1-b9465bc6e931", "StripeCustomerID": "cus_OAeTEc2Qh9vn2o", "PaymentMethodID": "pm_1O9patFB8zXks4nkxROkh1W4", "PaymentMethodDate": "November 07, 2023"}, {"Descriptor": "ValuewalkPremium", "PSP": "Stripe", "ChargeflowID": "649dcf64dae42e66f4707311", "EnrollmentDate": "August 24, 2023", "MerchantId": "61443955-08b7-48e9-9a31-3f5ad68602ab", "StripeCustomerID": "cus_OAlgZoDvHsbfh9", "PaymentMethodID": "pm_1NOQ9BFB8zXks4nkEWLcdOUV", "PaymentMethodDate": "June 29, 2023"}, {"Descriptor": "SP DELIGHTFUL YARD", "PSP": "Shopify Payments", "ChargeflowID": "64a323c6ca7ce5df3d80ed04", "EnrollmentDate": "September 22, 2023", "MerchantId": "525719ec-4578-4d2c-a56c-8752181dc508", "StripeCustomerID": "cus_OcQpySCZtUrXGG", "PaymentMethodID": "pm_1NpzOyFB8zXks4nk3emOfkvS", "PaymentMethodDate": "September 13, 2023"}, {"Descriptor": "IvorySmile", "PSP": "Stripe", "ChargeflowID": "64ac983ae10f7d1094825cc4", "EnrollmentDate": "January 05, 2024", "MerchantId": "b28ba5cf-c81c-49f1-bcc5-aaacc6b188b1", "StripeCustomerID": "cus_P6Eh8YSg7pnC9q", "PaymentMethodID": "pm_1OIDtvFB8zXks4nk7Iw7zh2x", "PaymentMethodDate": "November 30, 2023"}, {"Descriptor": "VEDIX", "PSP": "Shopify Payments", "ChargeflowID": "64b6784e6739d67f61c5d12b", "EnrollmentDate": "August 24, 2023", "MerchantId": "ef5251bc-656d-4eb7-b415-d3712cac1f2f", "StripeCustomerID": "cus_OfSR3Kdhv9Zpeh", "PaymentMethodID": "pm_1ODoVlFB8zXks4nkidynjrzk", "PaymentMethodDate": "November 18, 2023"}, {"Descriptor": "SP * ZOMETI", "PSP": "Shopify Payments", "ChargeflowID": "64baec1970272e6c85e98986", "EnrollmentDate": "August 22, 2023", "MerchantId": "fd495374-ad76-404f-8b55-7f0a9a41b993", "StripeCustomerID": "cus_ORXOu1prvQV6xh", "PaymentMethodID": "pm_1NegJ1FB8zXks4nk7m9OI5Xh", "PaymentMethodDate": "August 13, 2023"}, {"Descriptor": "KEYMATE.AI", "PSP": "Stripe", "ChargeflowID": "64bf613d1bf4e0ce14c2cae1", "EnrollmentDate": "November 17, 2023", "MerchantId": "0969e36e-e143-422c-8b6a-70d017ebe44b", "StripeCustomerID": "cus_OKIi0HtKX6Kpsh", "PaymentMethodID": "pm_1OWFv6FB8zXks4nk0ZbxzqoR", "PaymentMethodDate": "January 08, 2024"}, {"Descriptor": "SP YVONNEBEAUTE", "PSP": "Shopify Payments", "ChargeflowID": "64c443b3f6035a64f1c56d5f", "EnrollmentDate": null, "MerchantId": "f285ba8d-85a0-4d6e-b9e0-084cd5ffa20c", "StripeCustomerID": "cus_OiPTUwmFFOe77i", "PaymentMethodID": "pm_1NvTJrFB8zXks4nkuUAcL0MW", "PaymentMethodDate": "September 28, 2023"}, {"Descriptor": "SP FINEPETHEALTH.COM", "PSP": "Shopify Payments", "ChargeflowID": "64c8e93f11cb427649874350", "EnrollmentDate": "August 24, 2023", "MerchantId": "86c298fc-f935-4b44-9a59-b4024c86fafa", "StripeCustomerID": "cus_OQ0pFUQsQlweMO", "PaymentMethodID": "pm_1NdCOMFB8zXks4nkCmziImNI", "PaymentMethodDate": "August 09, 2023"}, {"Descriptor": "URBANEBOX", "PSP": "Stripe", "ChargeflowID": "64caf6843217afdd45078834", "EnrollmentDate": "August 31, 2023", "MerchantId": "35ce1db4-aec9-4f1c-ac8b-56c68f779583", "StripeCustomerID": "cus_ONalvSazFKebsz", "PaymentMethodID": "pm_1NapaMFB8zXks4nkIi4akeLM", "PaymentMethodDate": "August 03, 2023"}, {"Descriptor": "SP NINALO", "PSP": "Shopify Payments", "ChargeflowID": "64ce617a9af1cb32412c4536", "EnrollmentDate": null, "MerchantId": "8f245bcf-ca8d-4c5f-94af-c6f7e6715ee7", "StripeCustomerID": "cus_P8iwILgygAY1KQ", "PaymentMethodID": "pm_1OPgu6FB8zXks4nkbClKy3SW", "PaymentMethodDate": "December 21, 2023"}, {"Descriptor": "T<PERSON>b", "PSP": "Stripe", "ChargeflowID": "64d65d7fe3a38c4b590c16c9", "EnrollmentDate": null, "MerchantId": "b234792f-0f79-4b13-b08e-f568222f0887", "StripeCustomerID": "cus_OQpdSUZ4WqTe9P", "PaymentMethodID": "pm_1NdxxnFB8zXks4nkSpBXuVod", "PaymentMethodDate": "August 11, 2023"}, {"Descriptor": "REACHOUT.AI", "PSP": "Stripe", "ChargeflowID": "64d7c2cee4ad05cd4723ed48", "EnrollmentDate": "December 19, 2023", "MerchantId": "8afb9930-bcf9-4847-a166-788c7145d4a1", "StripeCustomerID": "cus_ORIMQJSaKKXcK2", "PaymentMethodID": "pm_1NePlOFB8zXks4nkaLWkKYeK", "PaymentMethodDate": "August 12, 2023"}, {"Descriptor": "Xfinity Solution LLC", "PSP": "Authorize.net", "ChargeflowID": "64d967ad406d492afdfe9fe0", "EnrollmentDate": "December 26, 2023", "MerchantId": "5f4b8818-61a4-4094-8def-2c0558c3a550", "StripeCustomerID": "cus_OVGBAxu7lhTeUj", "PaymentMethodID": "pm_1NiFfBFB8zXks4nkrXXJ1lwP", "PaymentMethodDate": "August 23, 2023"}, {"Descriptor": "SQ *Xfinity Solution LLC", "PSP": "Square", "ChargeflowID": "64d967ad406d492afdfe9fe0", "EnrollmentDate": "December 25, 2023", "MerchantId": "a3632a08-b59b-43cc-8e84-74a2f39c69e7", "StripeCustomerID": "cus_OVGBAxu7lhTeUj", "PaymentMethodID": "pm_1NiFfBFB8zXks4nkrXXJ1lwP", "PaymentMethodDate": "August 23, 2023"}, {"Descriptor": "Gamegrid", "PSP": "Stripe", "ChargeflowID": "64da189bea8076a7be736fbb", "EnrollmentDate": null, "MerchantId": "69e735da-0c88-4faa-997c-9d9bfa326922", "StripeCustomerID": "cus_ORtso14uKRfTRJ", "PaymentMethodID": "pm_1Nf04ZFB8zXks4nk4gChBM6o", "PaymentMethodDate": "August 14, 2023"}, {"Descriptor": "SP * NANO HEARING AIDS", "PSP": "Shopify Payments", "ChargeflowID": "64da5585a5be86780e7945bf", "EnrollmentDate": "August 29, 2023", "MerchantId": "3168d30c-7a20-4f36-9e57-5bc0d957b85c", "StripeCustomerID": "cus_OaURMM09phPEZt", "PaymentMethodID": "pm_1Nnl9SFB8zXks4nkxy37GBj2", "PaymentMethodDate": "September 07, 2023"}, {"Descriptor": "SP Bynelo", "PSP": "Shopify Payments", "ChargeflowID": "64dcd27121a48c5e9a4123a4", "EnrollmentDate": "October 27, 2023", "MerchantId": "2e64b08d-0e49-42be-b833-dc4c5c5bcea5", "StripeCustomerID": "cus_Ot8l4w90Q7mw74", "PaymentMethodID": "pm_1O5PmHFB8zXks4nkEdQUnWH8", "PaymentMethodDate": "October 26, 2023"}, {"Descriptor": "Minea", "PSP": "Stripe", "ChargeflowID": "64ddd637b6371056fc4c7445", "EnrollmentDate": null, "MerchantId": "a65c46f9-568a-42f5-a02f-b721de157f7e", "StripeCustomerID": "cus_OSxFssXUiYnOMC", "PaymentMethodID": "pm_1NkQ9EFB8zXks4nkbzVOAovZ", "PaymentMethodDate": "August 29, 2023"}, {"Descriptor": "Cupids Fragrances", "PSP": "Maverick Payments", "ChargeflowID": "64de448b195a9268d62af493", "EnrollmentDate": "September 26, 2023", "MerchantId": "df91aa4b-a489-4207-8239-dcc73fc1bb0c", "StripeCustomerID": "cus_Oa6PDO3NZUL05q", "PaymentMethodID": "pm_1Nn3YHFB8zXks4nk4X2V2yed", "PaymentMethodDate": "September 05, 2023"}, {"Descriptor": "CUPID FRAGRANCES", "PSP": "Stripe", "ChargeflowID": "64de448b195a9268d62af493", "EnrollmentDate": "September 03, 2023", "MerchantId": "6fbf1d46-d35a-4d54-8702-f2e0bc7c5b51", "StripeCustomerID": "cus_Oa6PDO3NZUL05q", "PaymentMethodID": "pm_1Nn3YHFB8zXks4nk4X2V2yed", "PaymentMethodDate": "September 05, 2023"}, {"Descriptor": "SP ALPHATOUCH", "PSP": "Shopify Payments", "ChargeflowID": "64e13a84f6a2d4332915f07a", "EnrollmentDate": "November 06, 2023", "MerchantId": "747c6689-9d54-4e23-aee2-93cd673d5593", "StripeCustomerID": "cus_OumDhLfAf8KonB", "PaymentMethodID": "pm_1OK6KwFB8zXks4nk4QHiB1xL", "PaymentMethodDate": "December 05, 2023"}, {"Descriptor": "TRENDWOOD.STORE", "PSP": "Stripe", "ChargeflowID": "64e25e554676ab59c7e531e4", "EnrollmentDate": "November 02, 2023", "MerchantId": "cffcf8cd-7a9d-47f6-8bb9-67bea9dcc645", "StripeCustomerID": "cus_OefwoMK8M1pq8w", "PaymentMethodID": "pm_1NrMaHFB8zXks4nkMq6e56Xb", "PaymentMethodDate": "September 17, 2023"}, {"Descriptor": "OUTFITTERSADVENTURE", "PSP": "Stripe", "ChargeflowID": "64e32e82320cedb7829008af", "EnrollmentDate": "September 14, 2023", "MerchantId": "b5f83f3e-fe57-41dd-8ee6-c0034363e25b", "StripeCustomerID": "cus_PDjQjWCfP7n45J", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "SP BUTIK JOY", "PSP": "Shopify Payments", "ChargeflowID": "64e796eb2f113e7ba5625244", "EnrollmentDate": "September 05, 2023", "MerchantId": "4fd62606-1e8a-46b5-a24e-97255f43a868", "StripeCustomerID": "cus_OWmlyIaoJsVm9b", "PaymentMethodID": "pm_1NjpPpFB8zXks4nkMtpRvtwr", "PaymentMethodDate": "August 27, 2023"}, {"Descriptor": "UNISON AUDIO", "PSP": "Stripe", "ChargeflowID": "64e91e2880e2d967f7f59e4e", "EnrollmentDate": "October 16, 2023", "MerchantId": "48a467f0-a7ce-4ed9-8273-905dee770c7f", "StripeCustomerID": "cus_OWA0C76DEUZmVM", "PaymentMethodID": "pm_1Nj7fvFB8zXks4nklPR3Hp5f", "PaymentMethodDate": "August 25, 2023"}, {"Descriptor": "SP BINZZY", "PSP": "Shopify Payments", "ChargeflowID": "64f2951a8f72db67166c36b3", "EnrollmentDate": "January 16, 2024", "MerchantId": "b0abb3d7-6037-412d-88e9-5253d3893083", "StripeCustomerID": "cus_PNrdiBFYCf8cMO", "PaymentMethodID": "pm_1ObdY0FB8zXks4nkOKXRHTQT", "PaymentMethodDate": "January 23, 2024"}, {"Descriptor": "I Done This", "PSP": "Stripe", "ChargeflowID": "64f4a13cf550653571d8dae9", "EnrollmentDate": "September 15, 2023", "MerchantId": "e9ab8e3b-e5fd-4b51-8ffc-861ff8a18ccd", "StripeCustomerID": "cus_OZQo6K63aSW5I4", "PaymentMethodID": "pm_1O54USFB8zXks4nkiofWdd3h", "PaymentMethodDate": "October 25, 2023"}, {"Descriptor": "SP BABIMATE", "PSP": "Shopify Payments", "ChargeflowID": "64f50157c9f43958c92d3ceb", "EnrollmentDate": "October 03, 2023", "MerchantId": "6e71f0dc-f089-4410-a915-6fca334debe8", "StripeCustomerID": "cus_OkHz2BJ9iax2P4", "PaymentMethodID": "pm_1NwnS2FB8zXks4nkcYYRj7Rs", "PaymentMethodDate": "October 02, 2023"}, {"Descriptor": "<PERSON><PERSON><PERSON>", "PSP": "Stripe", "ChargeflowID": "6501be94c71f3a2038d87cbb", "EnrollmentDate": "October 04, 2023", "MerchantId": "936b2de7-43f8-400e-8abd-c1f588bbf6b4", "StripeCustomerID": "cus_OkldAyyeaGKrno", "PaymentMethodID": "pm_1NxG5eFB8zXks4nkzWeWGc6Z", "PaymentMethodDate": "October 03, 2023"}, {"Descriptor": "PHEROMONEWONDERS", "PSP": "Stripe", "ChargeflowID": "6504b264adbf8b2f7f941c84", "EnrollmentDate": "September 21, 2023", "MerchantId": "559e8d7f-6cb1-461d-a348-295dcec32e30", "StripeCustomerID": "cus_Odzw2EaUTxw1Nf", "PaymentMethodID": "pm_1NqhwDFB8zXks4nkHlerVPoO", "PaymentMethodDate": "September 15, 2023"}, {"Descriptor": "SP VELARRIO", "PSP": "Shopify Payments", "ChargeflowID": "6508420cc402c0ccbe70962e", "EnrollmentDate": null, "MerchantId": "0ac65327-2f78-421f-83d0-3aab433672dc", "StripeCustomerID": "cus_OfMSP4BDuG4E4c", "PaymentMethodID": "pm_1OPtm4FB8zXks4nkgODXch0h", "PaymentMethodDate": "December 21, 2023"}, {"Descriptor": "ACTIVATESOFTWARE.COM", "PSP": "Stripe", "ChargeflowID": "65086b78ebad3e738674c9ca", "EnrollmentDate": "September 22, 2023", "MerchantId": "f82e3e40-d312-466e-b523-744b2484e334", "StripeCustomerID": "cus_Of3RPzw57Nec3v", "PaymentMethodID": "pm_1OZEgzFB8zXks4nk2gAKfZge", "PaymentMethodDate": "January 16, 2024"}, {"Descriptor": "Noosfera", "PSP": "Stripe", "ChargeflowID": "650902b46d70a7e70c2ee243", "EnrollmentDate": "November 02, 2023", "MerchantId": "0bc1872c-2dbe-4143-a08f-978f22c3082b", "StripeCustomerID": "cus_OfDt8T8btivxjj", "PaymentMethodID": "pm_1NrtRMFB8zXks4nk2WPmAHDa", "PaymentMethodDate": "September 19, 2023"}, {"Descriptor": "PAGEPILOT.AI", "PSP": "Stripe", "ChargeflowID": "650bfd7c1e97cf3f0a57e582", "EnrollmentDate": "November 23, 2023", "MerchantId": "151f0bb7-8cef-4b1c-9ffc-0b5fb0c9e2d1", "StripeCustomerID": "cus_Og4T7AmIXj0gKb", "PaymentMethodID": "pm_1OIVGiFB8zXks4nk1CwYiCSN", "PaymentMethodDate": "December 01, 2023"}, {"Descriptor": "ONLINE-QR-GENERATOR", "PSP": "Stripe", "ChargeflowID": "650c6dd8c445367334627ff7", "EnrollmentDate": "October 13, 2023", "MerchantId": "379baf7c-a2aa-4438-adb7-aca8d10ce59c", "StripeCustomerID": "cus_OgZzG0cPacbaR7", "PaymentMethodID": "pm_1NtCpDFB8zXks4nka2LUsJOG", "PaymentMethodDate": "September 22, 2023"}, {"Descriptor": "GAMEOFADVERTISING", "PSP": "Stripe", "ChargeflowID": "650d8df987568a8f6b881a20", "EnrollmentDate": "October 13, 2023", "MerchantId": "9f586900-4033-4adf-8c8e-bd62d51597a6", "StripeCustomerID": "cus_OgWHXbWY87GwdM", "PaymentMethodID": "pm_1Nt9EuFB8zXks4nkHPv2G3Su", "PaymentMethodDate": "September 22, 2023"}, {"Descriptor": "GETGUAC", "PSP": "Stripe", "ChargeflowID": "650dc333730fb724ea4d444f", "EnrollmentDate": "October 04, 2023", "MerchantId": "ea98db4d-0f47-477d-a886-6228c03fc072", "StripeCustomerID": "cus_OgZcYQvXPhaVwZ", "PaymentMethodID": "pm_1NtCShFB8zXks4nkCK0pd30O", "PaymentMethodDate": "September 22, 2023"}, {"Descriptor": "SP Liberty Hearing", "PSP": "Shopify Payments", "ChargeflowID": "65122bf41305b48cdbcf8489", "EnrollmentDate": "October 13, 2023", "MerchantId": "4fa31fde-24c3-42f2-875c-273fe341db61", "StripeCustomerID": "cus_Op8B5jHYmCgYZF", "PaymentMethodID": "pm_1O1XTrFB8zXks4nkvu2KKhg8", "PaymentMethodDate": "October 15, 2023"}, {"Descriptor": "WINDEN", "PSP": "Stripe", "ChargeflowID": "65125ccccd1d8938d464d369", "EnrollmentDate": "October 16, 2023", "MerchantId": "da726915-862e-413d-b348-223f8432506a", "StripeCustomerID": "cus_OhsbYBRGClv6Pt", "PaymentMethodID": "pm_1NuSqPFB8zXks4nkagUXO5Xe", "PaymentMethodDate": "September 26, 2023"}, {"Descriptor": "SECRETOSOCULTOS", "PSP": "Stripe", "ChargeflowID": "6514a2e64a610f5862320318", "EnrollmentDate": null, "MerchantId": "58424a2a-21c5-42cb-8263-9f96df218448", "StripeCustomerID": "cus_OiWgXvWZdyH1Qt", "PaymentMethodID": "pm_1NyeU4FB8zXks4nkPLbiq8ue", "PaymentMethodDate": "October 07, 2023"}, {"Descriptor": "DESCUBRIENDOLAV", "PSP": "Stripe", "ChargeflowID": "6514a2e64a610f5862320318", "EnrollmentDate": null, "MerchantId": "e5e25d4f-c37e-48a2-8093-d29b8889976b", "StripeCustomerID": "cus_OiWgXvWZdyH1Qt", "PaymentMethodID": "pm_1NyeU4FB8zXks4nkPLbiq8ue", "PaymentMethodDate": "October 07, 2023"}, {"Descriptor": "OCTUS", "PSP": "Stripe", "ChargeflowID": "6514a2e64a610f5862320318", "EnrollmentDate": null, "MerchantId": "9121412b-1e6d-40c2-bb7a-20d826113edb", "StripeCustomerID": "cus_OiWgXvWZdyH1Qt", "PaymentMethodID": "pm_1NyeU4FB8zXks4nkPLbiq8ue", "PaymentMethodDate": "October 07, 2023"}, {"Descriptor": "PLUS", "PSP": "Stripe", "ChargeflowID": "6514a2e64a610f5862320318", "EnrollmentDate": null, "MerchantId": "b7114f39-eba2-49c5-a2b8-7fbc61bce53e", "StripeCustomerID": "cus_OiWgXvWZdyH1Qt", "PaymentMethodID": "pm_1NyeU4FB8zXks4nkPLbiq8ue", "PaymentMethodDate": "October 07, 2023"}, {"Descriptor": "RADAR", "PSP": "Stripe", "ChargeflowID": "6514a2e64a610f5862320318", "EnrollmentDate": null, "MerchantId": "2fc095bb-2e27-4688-9c6a-4ea30ac4d7c9", "StripeCustomerID": "cus_OiWgXvWZdyH1Qt", "PaymentMethodID": "pm_1NyeU4FB8zXks4nkPLbiq8ue", "PaymentMethodDate": "October 07, 2023"}, {"Descriptor": "MISTERIOSENLINE", "PSP": "Stripe", "ChargeflowID": "6514a2e64a610f5862320318", "EnrollmentDate": null, "MerchantId": "0fa6cdb0-b7d9-404f-b687-9a57fa609b8d", "StripeCustomerID": "cus_OiWgXvWZdyH1Qt", "PaymentMethodID": "pm_1NyeU4FB8zXks4nkPLbiq8ue", "PaymentMethodDate": "October 07, 2023"}, {"Descriptor": "OCTUSPAY", "PSP": "Stripe", "ChargeflowID": "6514a2e64a610f5862320318", "EnrollmentDate": null, "MerchantId": "57b797c7-d894-4bc6-a31d-cf195ba24078", "StripeCustomerID": "cus_OiWgXvWZdyH1Qt", "PaymentMethodID": "pm_1NyeU4FB8zXks4nkPLbiq8ue", "PaymentMethodDate": "October 07, 2023"}, {"Descriptor": "LAVERDAD", "PSP": "Stripe", "ChargeflowID": "6514a2e64a610f5862320318", "EnrollmentDate": null, "MerchantId": "9dfe61a7-aad9-4380-9927-978cd8135412", "StripeCustomerID": "cus_OiWgXvWZdyH1Qt", "PaymentMethodID": "pm_1NyeU4FB8zXks4nkPLbiq8ue", "PaymentMethodDate": "October 07, 2023"}, {"Descriptor": "SP FURLIFE", "PSP": "Shopify Payments", "ChargeflowID": "651d337ee12e9aab37d2c16b", "EnrollmentDate": "November 14, 2023", "MerchantId": "39d3a09d-6db8-4bbb-8e8e-3698f23bb40b", "StripeCustomerID": "cus_OpSu5n5pPK6p2x", "PaymentMethodID": "pm_1O1q2BFB8zXks4nkzz2zTswI", "PaymentMethodDate": "October 16, 2023"}, {"Descriptor": "SP SPARTAN", "PSP": "Shopify Payments", "ChargeflowID": "651d489262cdb46d535f8c3d", "EnrollmentDate": "October 30, 2023", "MerchantId": "6cc9ee19-ea1f-49e7-84fd-e6eaba46215f", "StripeCustomerID": "cus_OpSu5n5pPK6p2x", "PaymentMethodID": "pm_1O1q2BFB8zXks4nkzz2zTswI", "PaymentMethodDate": "October 16, 2023"}, {"Descriptor": "SP ARTAN", "PSP": "Shopify Payments", "ChargeflowID": "651d489262cdb46d535f8c3d", "EnrollmentDate": "October 19, 2023", "MerchantId": "79cc2355-1a4f-4971-8aac-71555e5f2517", "StripeCustomerID": "cus_OpSu5n5pPK6p2x", "PaymentMethodID": "pm_1O1q2BFB8zXks4nkzz2zTswI", "PaymentMethodDate": "October 16, 2023"}, {"Descriptor": "Tracelo.com", "PSP": "Stripe", "ChargeflowID": "651ef6df4c8fd89271310efe", "EnrollmentDate": "October 17, 2023", "MerchantId": "09c84bb4-521b-42a7-8a54-f701cbe72146", "StripeCustomerID": "cus_OlUnN8AwgIfPnL", "PaymentMethodID": "pm_1NxxnzFB8zXks4nkd6Ekv9a8", "PaymentMethodDate": "October 05, 2023"}, {"Descriptor": "S-CONSULTANT", "PSP": "Stripe", "ChargeflowID": "6521f5c93c2c1e80dffa3459", "EnrollmentDate": "October 23, 2023", "MerchantId": "de6652a9-9cae-4fe7-9e60-e8ddcf041410", "StripeCustomerID": "cus_OmJZqUmzfyuiMD", "PaymentMethodID": "pm_1NykwfFB8zXks4nkX2Apgrhb", "PaymentMethodDate": "October 08, 2023"}, {"Descriptor": "STDL-CONS", "PSP": "Stripe", "ChargeflowID": "6521f5c93c2c1e80dffa3459", "EnrollmentDate": "January 22, 2024", "MerchantId": "33bc36b4-5f8f-4af0-be0e-d59466a069d1", "StripeCustomerID": "cus_OmJZqUmzfyuiMD", "PaymentMethodID": "pm_1NykwfFB8zXks4nkX2Apgrhb", "PaymentMethodDate": "October 08, 2023"}, {"Descriptor": "ART3LIER", "PSP": "Stripe", "ChargeflowID": "6521fa6d1fe4d25f1cbdc621", "EnrollmentDate": "October 23, 2023", "MerchantId": "91b1bf24-4e87-4d03-a389-63feb8e81f05", "StripeCustomerID": "cus_OmJkJDOLXsxjk4", "PaymentMethodID": "pm_1Nyl6VFB8zXks4nk7X3Tyfkd", "PaymentMethodDate": "October 08, 2023"}, {"Descriptor": "Authorization", "PSP": "Stripe", "ChargeflowID": "6526e3da753bc484284e4e73", "EnrollmentDate": "October 17, 2023", "MerchantId": "e21d4c02-cc32-4c6b-8419-25408c964a6a", "StripeCustomerID": "cus_OpvZ8MDGJm7QRb", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "Klassi.", "PSP": "Stripe", "ChargeflowID": "6530294160426fb04e79900e", "EnrollmentDate": "October 20, 2023", "MerchantId": "f688173c-53ec-4554-ace7-1e469f54a9ea", "StripeCustomerID": "cus_P2CM8hr4boRRER", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "STP ACADEMY", "PSP": "Stripe", "ChargeflowID": "65315a8d50ff6b2b278f4254", "EnrollmentDate": "January 15, 2024", "MerchantId": "b628eaa7-8564-46a3-9a35-2373f437a112", "StripeCustomerID": "cus_PIAwrUu4D0RxGY", "PaymentMethodID": "pm_1OTaaqFB8zXks4nkV7Z3teXM", "PaymentMethodDate": "January 01, 2024"}, {"Descriptor": "GROWTH BU.* TRIAL OVER", "PSP": "Stripe", "ChargeflowID": "653169b921cf3827fba47f96", "EnrollmentDate": "October 26, 2023", "MerchantId": "a4c8de39-c8b0-4d9b-8df0-ee58ecdc10b3", "StripeCustomerID": "cus_Oqhh4WgQaF4y8i", "PaymentMethodID": "pm_1OjFkEFB8zXks4nkuUeg8dRa", "PaymentMethodDate": "February 13, 2024"}, {"Descriptor": "GROWTH BUNDLE", "PSP": "Stripe", "ChargeflowID": "653169b921cf3827fba47f96", "EnrollmentDate": "January 08, 2024", "MerchantId": "8af2f592-99a5-484e-8f44-034515af3547", "StripeCustomerID": "cus_Oqhh4WgQaF4y8i", "PaymentMethodID": "pm_1OjFkEFB8zXks4nkuUeg8dRa", "PaymentMethodDate": "February 13, 2024"}, {"Descriptor": "GROWTH BUN.", "PSP": "Stripe", "ChargeflowID": "653169b921cf3827fba47f96", "EnrollmentDate": "January 23, 2024", "MerchantId": "80c6b381-2764-43a0-b59d-f6efd299a15a", "StripeCustomerID": "cus_Oqhh4WgQaF4y8i", "PaymentMethodID": "pm_1OjFkEFB8zXks4nkuUeg8dRa", "PaymentMethodDate": "February 13, 2024"}, {"Descriptor": "SP VALLEYVIOLET.COM", "PSP": "Shopify Payments", "ChargeflowID": "6532ea027b38ba85e71714e6", "EnrollmentDate": "November 14, 2023", "MerchantId": "1a3ceb7d-abca-40d2-b223-d3bee3dece16", "StripeCustomerID": "cus_OrGA5d3Z3wSvWs", "PaymentMethodID": "pm_1OCHUvFB8zXks4nkxB354IDR", "PaymentMethodDate": "November 14, 2023"}, {"Descriptor": "SPIKERZ DIGITALARN", "PSP": "Stripe", "ChargeflowID": "6537b5405276c7d2b579d542", "EnrollmentDate": null, "MerchantId": "6cd0b8dd-a7cc-438c-9570-50b89fdfdabc", "StripeCustomerID": "cus_OsUXxnzAdCvvX2", "PaymentMethodID": "pm_1O4jYIFB8zXks4nktPNCcMCV", "PaymentMethodDate": "October 24, 2023"}, {"Descriptor": "SI HEALTH", "PSP": "Stripe", "ChargeflowID": "65393664f16ba8a563756eda", "EnrollmentDate": null, "MerchantId": "f6c15a74-7303-43a8-928a-36d26a6d3be6", "StripeCustomerID": "cus_OxO0Ve990AfrUa", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "SAUCY", "PSP": "Stripe", "ChargeflowID": "65399d1007ba33dc3e0262aa", "EnrollmentDate": "November 10, 2023", "MerchantId": "785690db-caca-4e6c-a5b8-2b7d40586e5b", "StripeCustomerID": "cus_OvZdXYFksBp5Gl", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "auraskin", "PSP": "Stripe", "ChargeflowID": "653b4247d3746d5e2cd14ee6", "EnrollmentDate": null, "MerchantId": "a9cb339d-b347-41ec-ab2d-6c19cba48879", "StripeCustomerID": "cus_POxraRI6FP6riZ", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "SP SIRENICEJEWELRY INC", "PSP": "Shopify Payments", "ChargeflowID": "653c879c5666daca15683bcd", "EnrollmentDate": null, "MerchantId": "69331164-f578-44c2-a234-499ac31687dc", "StripeCustomerID": "cus_OuNamBaC5is62Z", "PaymentMethodID": "pm_1O6dWhFB8zXks4nkHJM62AGD", "PaymentMethodDate": "October 29, 2023"}, {"Descriptor": "BLOOMHEALTHY.CO", "PSP": "Stripe", "ChargeflowID": "653cc03403eabbef99518ef2", "EnrollmentDate": "February 05, 2024", "MerchantId": "c7dbb3e6-c4f0-46cf-b7d4-e6c3e6eca2d2", "StripeCustomerID": "cus_PD0dTwU4E0rALZ", "PaymentMethodID": "pm_1OOabiFB8zXks4nkbr1v9xJH", "PaymentMethodDate": "December 18, 2023"}, {"Descriptor": "<PERSON><PERSON><PERSON>", "PSP": "Stripe", "ChargeflowID": "654128822048874ad71cfb79", "EnrollmentDate": "November 06, 2023", "MerchantId": "9f5efe49-2f6b-427d-83ff-fbcd9d71f677", "StripeCustomerID": "cus_PTKEwkqE0VTSry", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "Yoshfilmss", "PSP": "Stripe", "ChargeflowID": "654a69095d2117345c24f543", "EnrollmentDate": "November 15, 2023", "MerchantId": "1e681b5b-0dcc-4250-b533-840a127a9d16", "StripeCustomerID": "cus_OxoAklUeboQOUB", "PaymentMethodID": "pm_1O9sYVFB8zXks4nkgQwMcmnC", "PaymentMethodDate": "November 07, 2023"}, {"Descriptor": "JELLY CASES", "PSP": "Stripe", "ChargeflowID": "654ae1d62da6c2aa05912281", "EnrollmentDate": "January 03, 2024", "MerchantId": "602c019e-020c-439a-b69a-09186311a200", "StripeCustomerID": "cus_PBVi04URKz1vup", "PaymentMethodID": "pm_1ON8pEFB8zXks4nk389B5a0J", "PaymentMethodDate": "November 07, 2023"}, {"Descriptor": "SP Goyard World", "PSP": "Shopify Payments", "ChargeflowID": "654b4c3511ac813b1e9618ac", "EnrollmentDate": "November 28, 2023", "MerchantId": "baa1ab67-37c9-4444-b00d-e46bc80961ac", "StripeCustomerID": "cus_Oy8hP9LsuWqFeZ", "PaymentMethodID": "pm_1ORqsPFB8zXks4nk5hHqTbVv", "PaymentMethodDate": "December 14, 2023"}, {"Descriptor": "SP PUBLICITYHIVE", "PSP": "Shopify Payments", "ChargeflowID": "654be13ce31e5e427d0fa549", "EnrollmentDate": "November 16, 2023", "MerchantId": "76025684-d482-439d-92de-bfecb1517daf", "StripeCustomerID": "cus_OyQejfq9RdIpWT", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "BROKEALTY", "PSP": "Stripe", "ChargeflowID": "654d98e773ae9bd6424e1fe6", "EnrollmentDate": null, "MerchantId": "f98d80f7-2b39-4c1c-a361-85b28dd102d8", "StripeCustomerID": "cus_OyiNPrD3NSG6ge", "PaymentMethodID": "pm_1OAkwFFB8zXks4nkqheZJqDz", "PaymentMethodDate": "November 10, 2023"}, {"Descriptor": "JPAUTOSHOP", "PSP": "Stripe", "ChargeflowID": "654da589adabc11796bb32ea", "EnrollmentDate": "November 20, 2023", "MerchantId": "29fafe02-0304-4180-8efa-72355f4efa71", "StripeCustomerID": "cus_Oyj3iY7t9n0FVL", "PaymentMethodID": "pm_1OAlbnFB8zXks4nkHi4WOOPJ", "PaymentMethodDate": "November 10, 2023"}, {"Descriptor": "INDIGOTRANSIT LLC", "PSP": "Stripe", "ChargeflowID": "654f1a317c25e111ead2aaaf", "EnrollmentDate": "January 30, 2024", "MerchantId": "fc1b070d-c113-4c2b-93a5-a6d7ef5ffe51", "StripeCustomerID": "cus_Oz8pioGeS9vpwS", "PaymentMethodID": "pm_1OBAXQFB8zXks4nkbzMdMf5M", "PaymentMethodDate": "November 11, 2023"}, {"Descriptor": "SP LIFEINBOX", "PSP": "Shopify Payments", "ChargeflowID": "654f9c0bb322ce89000f36a7", "EnrollmentDate": "December 08, 2023", "MerchantId": "605737b4-2bbf-4aa5-a451-d9edcfe1a5b0", "StripeCustomerID": "cus_OzvIjvZ3SfBIu5", "PaymentMethodID": "pm_1OC1RoFB8zXks4nkl5tiLw4Y", "PaymentMethodDate": "November 13, 2023"}, {"Descriptor": "VIB_PAYMENTS_BOT", "PSP": "Stripe", "ChargeflowID": "654fa7358f83b2f8ce556e32", "EnrollmentDate": "November 20, 2023", "MerchantId": "d54945c2-5988-4f2e-8a53-1249c0ea112a", "StripeCustomerID": "cus_OzINdQyyqaKfSp", "PaymentMethodID": "pm_1OBJmVFB8zXks4nk8dNBQXZz", "PaymentMethodDate": "November 11, 2023"}, {"Descriptor": "ARIDEKVM", "PSP": "Stripe", "ChargeflowID": "655055988036bb11ae162b22", "EnrollmentDate": null, "MerchantId": "ab793f6f-8219-4cde-8317-3d548839e79b", "StripeCustomerID": "cus_OzUQurmlyOrNyC", "PaymentMethodID": "pm_1OBVRUFB8zXks4nkVVufrXLH", "PaymentMethodDate": "November 12, 2023"}, {"Descriptor": "SP THE WATER GUYS", "PSP": "Stripe", "ChargeflowID": "6553ea678150ed1d53e49ef1", "EnrollmentDate": "November 27, 2023", "MerchantId": "2e767529-cd9f-4add-adb5-174c3e7c95ac", "StripeCustomerID": "cus_P0hZ1vX9qmgt7t", "PaymentMethodID": "pm_1OG76BFB8zXks4nkz9iPFy5C", "PaymentMethodDate": "November 24, 2023"}, {"Descriptor": "PREQUEL", "PSP": "Stripe", "ChargeflowID": "655648927d1f73216d07d6ad", "EnrollmentDate": "November 28, 2023", "MerchantId": "a1e074e4-b117-4056-b87a-2f0bd244bceb", "StripeCustomerID": "cus_P1BIVov8MrKYAz", "PaymentMethodID": "pm_1OD8vkFB8zXks4nkfY9kPC92", "PaymentMethodDate": "November 16, 2023"}, {"Descriptor": "COLONDONMEDIA LLC", "PSP": "Stripe", "ChargeflowID": "6558bca2341024322617ef20", "EnrollmentDate": "November 20, 2023", "MerchantId": "f75e15d9-5de9-4b88-82ac-3704f6d90d4a", "StripeCustomerID": "cus_PTcn4OU3ZkSaH9", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "WWW.DROPSHIP.IO", "PSP": "Stripe", "ChargeflowID": "655b892630d354a2f30b452a", "EnrollmentDate": "November 24, 2023", "MerchantId": "349eb771-4db9-4b53-a03f-4915b13dcf76", "StripeCustomerID": "cus_P2fnVlZTMklIn2", "PaymentMethodID": "pm_1OJcp0FB8zXks4nkAhveZmaC", "PaymentMethodDate": "December 04, 2023"}, {"Descriptor": "SP Modern Monday", "PSP": "Shopify Payments", "ChargeflowID": "655c15eac230f86f14600440", "EnrollmentDate": null, "MerchantId": "0ecf79ae-ab47-49a7-99e7-b7c9d078be44", "StripeCustomerID": "cus_P2s72SKG25SXGr", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "SP MIRA FERTILITY", "PSP": "Shopify Payments", "ChargeflowID": "655c7dced85079a0ec1be3fa", "EnrollmentDate": "December 21, 2023", "MerchantId": "a4ae4798-e63e-4097-88f6-2c55c4fb64bc", "StripeCustomerID": "cus_P6Pl6An8GuxHuJ", "PaymentMethodID": "pm_1OIIFWFB8zXks4nksJWPgFQU", "PaymentMethodDate": "November 30, 2023"}, {"Descriptor": "<PERSON><PERSON><PERSON>", "PSP": "BankCard USA ( authorize.net  )", "ChargeflowID": "655cc0dc5b47937e186b6a00", "EnrollmentDate": "November 23, 2023", "MerchantId": "12a1018b-4392-4ee6-9a7a-42b7e471cf7d", "StripeCustomerID": "cus_P3JNkt25xZD7vj", "PaymentMethodID": "pm_1OFOOlFB8zXks4nkYKKuKqNw", "PaymentMethodDate": "November 22, 2023"}, {"Descriptor": "<PERSON> SweatVault", "PSP": "BankCard USA ( authorize.net  )", "ChargeflowID": "655cc0dc5b47937e186b6a00", "EnrollmentDate": "November 27, 2023", "MerchantId": "b8992179-399f-4509-9886-9d210f51b383", "StripeCustomerID": "cus_P3JNkt25xZD7vj", "PaymentMethodID": "pm_1OFOOlFB8zXks4nkYKKuKqNw", "PaymentMethodDate": "November 22, 2023"}, {"Descriptor": "SP RIMINI CHOCOLATE", "PSP": "Shopify Payments", "ChargeflowID": "655d3d193b58db1e1f304423", "EnrollmentDate": "December 22, 2023", "MerchantId": "6c9e7673-6e0f-4685-9dc8-02a9d3d4e5f2", "StripeCustomerID": "cus_PCNfNewEYsFHCl", "PaymentMethodID": "pm_1OPU35FB8zXks4nkpTQfhXGJ", "PaymentMethodDate": "December 20, 2023"}, {"Descriptor": "SP INSTREAME", "PSP": "Shopify Payments", "ChargeflowID": "655d8634f4f345e1f5f045fa", "EnrollmentDate": "December 20, 2023", "MerchantId": "6eb5dbb8-b35d-48cb-82bf-0a401264cb97", "StripeCustomerID": "cus_P3Fy9GmMIVeGt7", "PaymentMethodID": "pm_1ORE8kFB8zXks4nkOJv1yMZ6", "PaymentMethodDate": "December 25, 2023"}, {"Descriptor": "FOLLOWERSCO", "PSP": "Checkout.com", "ChargeflowID": "655dea324696599196c6cdf3", "EnrollmentDate": "December 13, 2023", "MerchantId": "4ece1d12-3daa-4e46-a7a0-3e8cc35647f1", "StripeCustomerID": "cus_P3eTaOT5tdY28e", "PaymentMethodID": "pm_1OOc73FB8zXks4nknEHOpCbW", "PaymentMethodDate": "December 18, 2023"}, {"Descriptor": "FOLLOWERSADS", "PSP": "Stripe", "ChargeflowID": "655dea324696599196c6cdf3", "EnrollmentDate": "December 18, 2023", "MerchantId": "19e72177-d60f-49be-b9da-b4f011a68e75", "StripeCustomerID": "cus_P3eTaOT5tdY28e", "PaymentMethodID": "pm_1OOc73FB8zXks4nknEHOpCbW", "PaymentMethodDate": "December 18, 2023"}, {"Descriptor": "SPECTUM LITE", "PSP": "Stripe", "ChargeflowID": "655e726202c0b2516a81632b", "EnrollmentDate": null, "MerchantId": "bce4e592-64a9-4e9e-b0cb-2f7b0ea019f9", "StripeCustomerID": "cus_P3V3MCi1PsaoN3", "PaymentMethodID": "pm_1OFO3YFB8zXks4nkLRhtMkmD", "PaymentMethodDate": "November 22, 2023"}, {"Descriptor": "KINFLOWERS", "PSP": "Stripe", "ChargeflowID": "655ec2837f6b3058ab3e9b78", "EnrollmentDate": "December 21, 2023", "MerchantId": "edfda143-21bb-4420-a613-e29f6a917db3", "StripeCustomerID": "cus_P6Eh8YSg7pnC9q", "PaymentMethodID": "pm_1OIDtvFB8zXks4nk7Iw7zh2x", "PaymentMethodDate": "November 30, 2023"}, {"Descriptor": "WWW.HORMIFY.COM", "PSP": "Stripe", "ChargeflowID": "655f2a19a7172e79876f3895", "EnrollmentDate": "November 30, 2023", "MerchantId": "e5d19870-5b74-4975-bb18-0bee15f8c566", "StripeCustomerID": "cus_ORtso14uKRfTRJ", "PaymentMethodID": "pm_1Nf04ZFB8zXks4nk4gChBM6o", "PaymentMethodDate": "August 14, 2023"}, {"Descriptor": "XTECH COMMUNICATIONS", "PSP": "Stripe", "ChargeflowID": "6561261ca642bc6f6da25cb2", "EnrollmentDate": "November 28, 2023", "MerchantId": "11edb937-9cb0-4cae-97fb-2d99dadb60ec", "StripeCustomerID": "cus_P4GdRrjiNeS1tJ", "PaymentMethodID": null, "PaymentMethodDate": null}, {"Descriptor": "SP SUPERSUNDAYSHOP.COM", "PSP": "Shopify Payments", "ChargeflowID": "65617c7d1c4e9e251b181a15", "EnrollmentDate": "December 20, 2023", "MerchantId": "ff6b5910-4570-41d0-8529-de2d4434c06e", "StripeCustomerID": "cus_P5sjGSNqv2pxwk", "PaymentMethodID": "pm_1OHqoNFB8zXks4nkwwzcWTmc", "PaymentMethodDate": "November 29, 2023"}, {"Descriptor": "SP PLAYBENCHI", "PSP": "Shopify Payments", "ChargeflowID": "6565f8a307692688d8eb6b3c", "EnrollmentDate": "January 26, 2024", "MerchantId": "23d7fe40-10e5-43c0-8c28-343b699bd9c3", "StripeCustomerID": "cus_PNXmCgqCzHJemu", "PaymentMethodID": "pm_1OZMTrFB8zXks4nkUUjCO1Bi", "PaymentMethodDate": "January 16, 2024"}, {"Descriptor": "Sofort HR", "PSP": "Stripe", "ChargeflowID": "656623bb4ddcad0542d22ef9", "EnrollmentDate": "November 29, 2023", "MerchantId": "056ff311-4eb1-4959-97e9-c2b26d6e27f0", "StripeCustomerID": "cus_P5gXxfTRhJvpIi", "PaymentMethodID": "pm_1OHVAKFB8zXks4nkF2St7gW1", "PaymentMethodDate": "November 28, 2023"}, {"Descriptor": "ONLINE SERVICES", "PSP": "Stripe", "ChargeflowID": "656623bb4ddcad0542d22ef9", "EnrollmentDate": "November 29, 2023", "MerchantId": "1bf919ae-7619-4bea-a5e3-a03ab09d4b03", "StripeCustomerID": "cus_P5gXxfTRhJvpIi", "PaymentMethodID": "pm_1OHVAKFB8zXks4nkF2St7gW1", "PaymentMethodDate": "November 28, 2023"}, {"Descriptor": "MDHAIR.CO", "PSP": "Stripe", "ChargeflowID": "65662703758f87b2271698b1", "EnrollmentDate": "December 20, 2023", "MerchantId": "f973482c-5e33-4a1e-958f-4083efbe5fa9", "StripeCustomerID": "cus_P5gxIxhOs5IiOP", "PaymentMethodID": "pm_1OHVZUFB8zXks4nk1vNsQatE", "PaymentMethodDate": "November 28, 2023"}, {"Descriptor": "MDACNE.COM", "PSP": "Stripe", "ChargeflowID": "65662703758f87b2271698b1", "EnrollmentDate": "January 08, 2024", "MerchantId": "db27eddc-b252-4ac0-8172-5bd6bac9a675", "StripeCustomerID": "cus_P5gxIxhOs5IiOP", "PaymentMethodID": "pm_1OHVZUFB8zXks4nk1vNsQatE", "PaymentMethodDate": "November 28, 2023"}, {"Descriptor": "MADHAI.CO", "PSP": "Stripe", "ChargeflowID": "65662703758f87b2271698b1", "EnrollmentDate": null, "MerchantId": "e8c318d4-1f96-4d6f-abf3-2fe1fd762b75", "StripeCustomerID": "cus_P5gxIxhOs5IiOP", "PaymentMethodID": "pm_1OHVZUFB8zXks4nk1vNsQatE", "PaymentMethodDate": "November 28, 2023"}, {"Descriptor": "PAYPAL*MDHAIR", "PSP": "Stripe", "ChargeflowID": "65662703758f87b2271698b1", "EnrollmentDate": null, "MerchantId": "62bb45d3-03b9-4c88-ad6b-acd46c8c587b", "StripeCustomerID": "cus_P5gxIxhOs5IiOP", "PaymentMethodID": "pm_1OHVZUFB8zXks4nk1vNsQatE", "PaymentMethodDate": "November 28, 2023"}, {"Descriptor": "learngenesis.com", "PSP": "Stripe", "ChargeflowID": "6568451862e8c6266f5bf024", "EnrollmentDate": "December 06, 2023", "MerchantId": "d6092e94-b01a-4dcf-9488-90ccc80a395b", "StripeCustomerID": "cus_P6L0XwJLf1uUaS", "PaymentMethodID": "pm_1OI8KoFB8zXks4nk6GTZIGQo", "PaymentMethodDate": "November 30, 2023"}, {"Descriptor": "SP NJORD GEAR", "PSP": "Shopify Payments", "ChargeflowID": "6568a0002a0d0a73bd090497", "EnrollmentDate": null, "MerchantId": "3f9a4a63-eb0b-4bd3-b05a-d4d565033378", "StripeCustomerID": "cus_PT6zzutDewSRN6", "PaymentMethodID": "pm_1OhCORFB8zXks4nkWgZFpnIC", "PaymentMethodDate": "February 07, 2024"}, {"Descriptor": "TECHREALTO", "PSP": "Stripe", "ChargeflowID": "6568d15f89ceacd4a06da744", "EnrollmentDate": "December 06, 2023", "MerchantId": "8a84f811-904a-4770-a741-748c06dc9e5a", "StripeCustomerID": "cus_P6RnBd1A7YzCd0", "PaymentMethodID": "pm_1OIEtQFB8zXks4nk4NFJHgLc", "PaymentMethodDate": "November 30, 2023"}, {"Descriptor": "OLAVITASKIN.COM", "PSP": "Stripe", "ChargeflowID": "6569bc8878723db5e60b036c", "EnrollmentDate": "December 20, 2023", "MerchantId": "d24a730f-c654-41aa-bd05-4c462c3c7119", "StripeCustomerID": "cus_P6i1dj8R1RzsXK", "PaymentMethodID": "pm_1OIUbBFB8zXks4nkLK80XK7P", "PaymentMethodDate": "December 01, 2023"}, {"Descriptor": "OLAVITA", "PSP": "Stripe", "ChargeflowID": "6569bc8878723db5e60b036c", "EnrollmentDate": "December 14, 2023", "MerchantId": "8c5d3a29-289d-40f0-ae60-8872784e555b", "StripeCustomerID": "cus_P6i1dj8R1RzsXK", "PaymentMethodID": "pm_1OIUbBFB8zXks4nkLK80XK7P", "PaymentMethodDate": "December 01, 2023"}, {"Descriptor": "VISION TECH", "PSP": "Stripe", "ChargeflowID": "6569cfd2643f60bbcfe616ee", "EnrollmentDate": "December 21, 2023", "MerchantId": "ff48a382-961e-4d42-a962-749c68235c2b", "StripeCustomerID": "cus_P6jLYRyglmeSBc", "PaymentMethodID": "pm_1OIVsyFB8zXks4nkkf1urYZG", "PaymentMethodDate": "December 01, 2023"}, {"Descriptor": "CERTIFIED TRANSLATION", "PSP": "Stripe", "ChargeflowID": "656c8fa143f2b96899bf7023", "EnrollmentDate": "December 21, 2023", "MerchantId": "6486832c-fafe-4ca5-8c34-001f6cac1403", "StripeCustomerID": "cus_P7VfceFl08NpPu", "PaymentMethodID": "pm_1OJGdgFB8zXks4nkeQ70X0FI", "PaymentMethodDate": "December 03, 2023"}, {"Descriptor": "SP BRASE", "PSP": "Shopify Payments", "ChargeflowID": "656d569c007aac182785ff9e", "EnrollmentDate": "January 10, 2024", "MerchantId": "8de246bb-ffb9-46f0-b08b-7697dc680b7a", "StripeCustomerID": "cus_PD65Dnm3o2YuRM", "PaymentMethodID": "pm_1ORWlFFB8zXks4nkvcTwzSQl", "PaymentMethodDate": "December 26, 2023"}, {"Descriptor": "SP ZENLIFE", "PSP": "Shopify Payments", "ChargeflowID": "656dcf5a7b7df46966f870a9", "EnrollmentDate": "December 06, 2023", "MerchantId": "b564de1f-aaf5-45d2-be35-3c378b6643d3", "StripeCustomerID": "cus_P880EtYMhiFKRx", "PaymentMethodID": "pm_1OeTtbFB8zXks4nkgDKTv68D", "PaymentMethodDate": "January 31, 2024"}, {"Descriptor": "ZENLIFE GROUNDING", "PSP": "Stripe", "ChargeflowID": "656dcf5a7b7df46966f870a9", "EnrollmentDate": "February 12, 2024", "MerchantId": "b10aa9c3-7f8a-44b9-b879-fd5ad72edea2", "StripeCustomerID": "cus_P880EtYMhiFKRx", "PaymentMethodID": "pm_1OeTtbFB8zXks4nkgDKTv68D", "PaymentMethodDate": "January 31, 2024"}, {"Descriptor": "LUNCHBREAK AI", "PSP": "Stripe", "ChargeflowID": "656f1d072e184f97d9db9433", "EnrollmentDate": "December 19, 2023", "MerchantId": "5c3cd79a-b384-49d4-89e5-c314c77ce8a1", "StripeCustomerID": "cus_P8EdONhwyYl7x9", "PaymentMethodID": "pm_1OJyALFB8zXks4nk3s4HVDJQ", "PaymentMethodDate": "December 05, 2023"}, {"Descriptor": "CARTPANDAGLOBAL", "PSP": "Stripe", "ChargeflowID": "656f5eb55e5f056f725b5266", "EnrollmentDate": "December 13, 2023", "MerchantId": "fbe58c96-b3a5-443c-b46d-4f969c1ae5d4", "StripeCustomerID": "cus_P8JXFOFjr6SR43", "PaymentMethodID": "pm_1OK2ubFB8zXks4nkYaZ8KscT", "PaymentMethodDate": "December 05, 2023"}, {"Descriptor": "BELLEZAPLENITUD", "PSP": "Stripe", "ChargeflowID": "65707f1c0b952529ae50a95d", "EnrollmentDate": null, "MerchantId": "ed95b715-9fa2-440f-af66-28238d5d369d", "StripeCustomerID": "cus_PDjZyrYb1ZWPA2", "PaymentMethodID": "pm_1OPNtQFB8zXks4nkXzgKHDx7", "PaymentMethodDate": "December 20, 2023"}, {"Descriptor": "SP MISSYMILANO", "PSP": "Shopify Payments", "ChargeflowID": "6571cdc4e60ed3323ceee99c", "EnrollmentDate": "January 16, 2024", "MerchantId": "39733a79-c2ff-41b9-afaa-55fd24c48145", "StripeCustomerID": "cus_PNbKhk1zHHsWfu", "PaymentMethodID": "pm_1OYqp6FB8zXks4nkndLqieSx", "PaymentMethodDate": "January 15, 2024"}, {"Descriptor": "SP BLUERIDGE CO", "PSP": "Shopify Payments", "ChargeflowID": "65722c1809bf33aa055dd2d2", "EnrollmentDate": "January 03, 2024", "MerchantId": "c798c68c-b2e3-44ba-9de9-6087f4bedcd5", "StripeCustomerID": "cus_P9y4QUNytPaVMj", "PaymentMethodID": "pm_1OROpmFB8zXks4nkLutdFvla", "PaymentMethodDate": "December 25, 2023"}, {"Descriptor": "SP DREAM", "PSP": "Shopify Payments", "ChargeflowID": "6575fa939c5fcb63c8e7878f", "EnrollmentDate": null, "MerchantId": "17271867-18c5-4d3c-a347-9ceb880351cd", "StripeCustomerID": "cus_PAJNzp9qO8J4tR", "PaymentMethodID": "pm_1OPTNJFB8zXks4nk3xtUfjbN", "PaymentMethodDate": "December 20, 2023"}, {"Descriptor": "SP SOOTHLYSHOP.COM", "PSP": "Shopify Payments", "ChargeflowID": "65790351870030cce76471d4", "EnrollmentDate": "December 20, 2023", "MerchantId": "4cfb154c-91c4-4689-981a-8576a3584be0", "StripeCustomerID": "cus_PDM7ESiGkSTmGd", "PaymentMethodID": "pm_1OP94YFB8zXks4nkBbFLzL22", "PaymentMethodDate": "December 19, 2023"}, {"Descriptor": "SP MALLIVA", "PSP": "Stripe", "ChargeflowID": "6579e2f260ddf776cea8aa0d", "EnrollmentDate": "December 18, 2023", "MerchantId": "4ac1039a-9bfe-4ac7-9711-9245636d9b4f", "StripeCustomerID": "cus_PCNrvaxvhs4j2x", "PaymentMethodID": "pm_1OO1IWFB8zXks4nkXqCCx2je", "PaymentMethodDate": "December 16, 2023"}, {"Descriptor": "OZZIETECHN", "PSP": "Stripe", "ChargeflowID": "657b1a934a7988c507ca1b6d", "EnrollmentDate": "December 30, 2023", "MerchantId": "c1053f03-6ddc-46e9-8b91-a65150319416", "StripeCustomerID": "cus_PBduK7knuKPj6W", "PaymentMethodID": "pm_1OhXkNFB8zXks4nkqqync3si", "PaymentMethodDate": "February 08, 2024"}, {"Descriptor": "<EMAIL>", "PSP": "Stripe", "ChargeflowID": "657b2ae1c57c442765cbed3d", "EnrollmentDate": "December 18, 2023", "MerchantId": "83ae23d4-96f8-4f41-8558-a4fa30340116", "StripeCustomerID": "cus_PBfMmGXzqqOFEi", "PaymentMethodID": "pm_1ONI1wFB8zXks4nkoUjceHkN", "PaymentMethodDate": "December 14, 2023"}, {"Descriptor": "BBUMZ.ENT", "PSP": "Stripe", "ChargeflowID": "657c914ca26874feb3e56c11", "EnrollmentDate": "January 02, 2024", "MerchantId": "372c7fc6-9d8f-43c7-ba93-a806dc0cef09", "StripeCustomerID": "cus_PC3hs5PXsyD1mB", "PaymentMethodID": "pm_1ONfaFFB8zXks4nkC2c8m3GM", "PaymentMethodDate": "December 15, 2023"}, {"Descriptor": "SP W SOUTH", "PSP": "Stripe", "ChargeflowID": "657d5566f8e9094379ada955", "EnrollmentDate": "January 04, 2024", "MerchantId": "591b5921-aa46-43e1-bd26-c4a64da93735", "StripeCustomerID": "cus_PCHN5pQ1z1QeW9", "PaymentMethodID": "pm_1OacSBFB8zXks4nkc00O9tcW", "PaymentMethodDate": "January 20, 2024"}, {"Descriptor": "Rentals United Properties", "PSP": "MX Merchant", "ChargeflowID": "657d59ab795294097625fb8f", "EnrollmentDate": "December 22, 2023", "MerchantId": "f882aa1d-3a59-4124-aa74-e38557fafd27", "StripeCustomerID": "cus_PEAVL5mJVxlwVN", "PaymentMethodID": "pm_1OPs0bFB8zXks4nkAnQ81woI", "PaymentMethodDate": "December 21, 2023"}, {"Descriptor": "SP YOURENVIE", "PSP": "Shopify Payments", "ChargeflowID": "657ec8cbbc50e31a10cb488b", "EnrollmentDate": null, "MerchantId": "81837f37-55f1-4dbc-a4ae-864c48e6632b", "StripeCustomerID": "cus_PD6QxE7FLIEzVG", "PaymentMethodID": "pm_1OPJldFB8zXks4nk6fNkGW0w", "PaymentMethodDate": "December 20, 2023"}, {"Descriptor": "SP FULLBEAUTY", "PSP": "Shopify Payments", "ChargeflowID": "658070115639e468f7defab0", "EnrollmentDate": "December 27, 2023", "MerchantId": "39725689-3004-4eec-b582-43eb968a8e7d", "StripeCustomerID": "cus_PDji1znq5yp6pE", "PaymentMethodID": "pm_1OPNpXFB8zXks4nkHNj3YwdB", "PaymentMethodDate": "December 20, 2023"}, {"Descriptor": "SP NCUK", "PSP": "Shopify Payments", "ChargeflowID": "65814ba893e07a0a1f6cd248", "EnrollmentDate": "January 01, 2024", "MerchantId": "076d69df-1a44-4844-8ae6-b3aadfa70e65", "StripeCustomerID": "cus_OpSu5n5pPK6p2x", "PaymentMethodID": "pm_1O1q2BFB8zXks4nkzz2zTswI", "PaymentMethodDate": "October 16, 2023"}, {"Descriptor": "SP DOLAMEX", "PSP": "Shopify Payments", "ChargeflowID": "6582ba564b8bd29ca1b88cd9", "EnrollmentDate": "December 26, 2023", "MerchantId": "c962319e-8211-4a52-ba6d-c5212cab4122", "StripeCustomerID": "cus_P8iwILgygAY1KQ", "PaymentMethodID": "pm_1OPgu6FB8zXks4nkbClKy3SW", "PaymentMethodDate": "December 21, 2023"}, {"Descriptor": "SP TOP FLOOR SAINT", "PSP": "Shopify Payments", "ChargeflowID": "6583365863b6d2e47d116bf6", "EnrollmentDate": "January 02, 2024", "MerchantId": "72d03dcd-59e1-4c52-b8f9-0b1003743507", "StripeCustomerID": "cus_PF2PRXH81xdJrM", "PaymentMethodID": "pm_1OR7rqFB8zXks4nkkpXpIvsd", "PaymentMethodDate": "December 25, 2023"}, {"Descriptor": "NFTPAY.XYZ", "PSP": "Stripe", "ChargeflowID": "6584be09b863a995c09d6ed3", "EnrollmentDate": "December 25, 2023", "MerchantId": "8e6eb1e3-0ce4-478d-9ec9-168039a24aa5", "StripeCustomerID": "cus_PENfeYrDcZOZsp", "PaymentMethodID": "pm_1OPuuPFB8zXks4nk7BlpJToM", "PaymentMethodDate": "December 21, 2023"}, {"Descriptor": "SP SHOPFONDORS.COM", "PSP": "Shopify Payments", "ChargeflowID": "6586f87e524056784d980156", "EnrollmentDate": "December 29, 2023", "MerchantId": "90d073ec-e829-407c-b846-719c08580812", "StripeCustomerID": "cus_PGjtepdcq86KKy", "PaymentMethodID": "pm_1OSTW3FB8zXks4nkayhzHXaQ", "PaymentMethodDate": "December 29, 2023"}, {"Descriptor": "SP MORI OPTICAL", "PSP": "Shopify Payments", "ChargeflowID": "658bc9a6716f815e95fc9dc8", "EnrollmentDate": "December 29, 2023", "MerchantId": "1397986f-1ef8-4872-97e3-f81bb81bc192", "StripeCustomerID": "cus_PGlCWrmr7gIjqF", "PaymentMethodID": "pm_1OSdJSFB8zXks4nkc2oLTptG", "PaymentMethodDate": "December 29, 2023"}, {"Descriptor": "SUPPLIER HQ HTE ECOM", "PSP": "Stripe", "ChargeflowID": "658dce33302f89b1352b4490", "EnrollmentDate": "January 02, 2024", "MerchantId": "80b35623-891f-41a3-8bf5-ce2c01405761", "StripeCustomerID": "cus_PGxOCf986KqTTb", "PaymentMethodID": "pm_1OSPTgFB8zXks4nkUvrxlck1", "PaymentMethodDate": "December 28, 2023"}, {"Descriptor": "MOODENZA", "PSP": "Stripe", "ChargeflowID": "658f1033e6965c2505588522", "EnrollmentDate": "January 05, 2024", "MerchantId": "0add8916-a539-4cfc-9f09-dcd9ea9edeaa", "StripeCustomerID": "cus_PHJcHVpXrUjVA3", "PaymentMethodID": "pm_1OSkzEFB8zXks4nkY9GQ7XM8", "PaymentMethodDate": "December 29, 2023"}, {"Descriptor": "SP SELINEY", "PSP": "Shopify Payments", "ChargeflowID": "658f4539c66beab7cb5e5321", "EnrollmentDate": "February 06, 2024", "MerchantId": "eec55db8-fb6c-44ee-8eda-4c24bcd967e6", "StripeCustomerID": "cus_PNYnEOAc0sVGGI", "PaymentMethodID": "pm_1OYt6QFB8zXks4nkeCyOTtss", "PaymentMethodDate": "January 15, 2024"}, {"Descriptor": "STUDYMONKEY.AI", "PSP": "Stripe", "ChargeflowID": "6590ecb894bded852e7dcbd6", "EnrollmentDate": "January 02, 2024", "MerchantId": "2774cae4-e650-44ce-9c07-a8cbb9595520", "StripeCustomerID": "cus_PHqexsZ66UQ96G", "PaymentMethodID": "pm_1OTGwrFB8zXks4nkjA7zhb19", "PaymentMethodDate": "December 31, 2023"}, {"Descriptor": "SP TJSOUTLET", "PSP": "Shopify Payments", "ChargeflowID": "65955c3af8b649bbc5050134", "EnrollmentDate": null, "MerchantId": "065fd6b1-76ed-40d6-9db3-d2bed0b278a5", "StripeCustomerID": "cus_PJNF4gAMmfOy61", "PaymentMethodID": "pm_1OUrsBFB8zXks4nk3eRzzsoX", "PaymentMethodDate": "January 04, 2024"}, {"Descriptor": "PROXIFLY", "PSP": "Stripe", "ChargeflowID": "6596c63e2a67ca2af450c05b", "EnrollmentDate": "January 29, 2024", "MerchantId": "47868814-d362-47a1-bfb1-00c0fc0731a4", "StripeCustomerID": "cus_PJVNynVNwLr8av", "PaymentMethodID": "pm_1OUsMiFB8zXks4nk5odKhwPL", "PaymentMethodDate": "January 04, 2024"}, {"Descriptor": "SP BENVENABRAND.COM", "PSP": "Shopify Payments", "ChargeflowID": "6596cf0c1008120b4f379082", "EnrollmentDate": "January 15, 2024", "MerchantId": "c6a8b021-38f9-4d08-9c07-16d33760bc9c", "StripeCustomerID": "cus_PKU23S3unjG4TR", "PaymentMethodID": "pm_1OWT7VFB8zXks4nkzOdGf4ii", "PaymentMethodDate": "January 09, 2024"}, {"Descriptor": "PAY2SHOPP.COM", "PSP": "Stripe", "ChargeflowID": "65974bcb4e5a39f75a7f9c13", "EnrollmentDate": "January 08, 2024", "MerchantId": "9d17a5ba-2672-42d0-a96e-44a8df298d22", "StripeCustomerID": "cus_PJen0J986GiRJT", "PaymentMethodID": "pm_1OV1U3FB8zXks4nkQRewpPf2", "PaymentMethodDate": "January 05, 2024"}, {"Descriptor": "SP SHOPATBENO", "PSP": "Shopify Payments", "ChargeflowID": "65982416d44a3d3c5cabb318", "EnrollmentDate": "January 16, 2024", "MerchantId": "52007e4f-d4c9-4fda-806e-28ebc5cc70cf", "StripeCustomerID": "cus_PNZuvPJfD4MxEJ", "PaymentMethodID": "pm_1OYor1FB8zXks4nkBZNYiFI4", "PaymentMethodDate": "January 15, 2024"}, {"Descriptor": "BGVTHEBEST", "PSP": "Stripe", "ChargeflowID": "6599f0e1e7abadc875d8b146", "EnrollmentDate": "January 09, 2024", "MerchantId": "f7d9ea8e-971a-45e3-ba93-54a6250302a0", "StripeCustomerID": "cus_PKPNhoOQU7wHVD", "PaymentMethodID": "pm_1OWNJVFB8zXks4nkIQX7XFmC", "PaymentMethodDate": "January 08, 2024"}, {"Descriptor": "CHAMBER", "PSP": "Stripe", "ChargeflowID": "659ef7c8197b43015f113dbd", "EnrollmentDate": "January 16, 2024", "MerchantId": "0096b7b7-188b-4c11-a401-f04ae98806ed", "StripeCustomerID": "cus_PNVs8ERVKbT6fG", "PaymentMethodID": "pm_1OYsZtFB8zXks4nkrvUxzAjG", "PaymentMethodDate": "January 15, 2024"}, {"Descriptor": "SP KALLISTIA", "PSP": "Shopify Payments", "ChargeflowID": "65a14cb0721c12b73a6f506d", "EnrollmentDate": "February 01, 2024", "MerchantId": "fe9fdb19-e4a2-41bb-9c3c-6dfc11043ec5", "StripeCustomerID": "cus_PNXPaJZOl8rphe", "PaymentMethodID": "pm_1OYn0YFB8zXks4nkGVNc9r7V", "PaymentMethodDate": "January 15, 2024"}, {"Descriptor": "PAY2COMMERCE.IO", "PSP": "Stripe", "ChargeflowID": "65a83d7755dd7775c4c7cfd8", "EnrollmentDate": "January 18, 2024", "MerchantId": "7a99728b-f790-4690-b892-3db6f2b2df7e", "StripeCustomerID": "cus_PKPNhoOQU7wHVD", "PaymentMethodID": "pm_1OWNJVFB8zXks4nkIQX7XFmC", "PaymentMethodDate": "January 08, 2024"}, {"Descriptor": "<PERSON><PERSON>", "PSP": "<PERSON><PERSON>", "ChargeflowID": "65a983ad1f3ded5fad687828", "EnrollmentDate": "February 26, 2024", "MerchantId": "142ca826-a8a6-4997-bab3-3dd12da3852e", "StripeCustomerID": "cus_PPHVTvsMFFUCVj", "PaymentMethodID": "pm_1OaSwAFB8zXks4nkGMvOx0DH", "PaymentMethodDate": "January 20, 2024"}, {"Descriptor": "HIGH TICKET ECOMMERCE", "PSP": "Stripe", "ChargeflowID": "65a983ad1f3ded5fad687828", "EnrollmentDate": null, "MerchantId": "d782a188-948c-4c40-ada5-8e904c1e3b9d", "StripeCustomerID": "cus_PPHVTvsMFFUCVj", "PaymentMethodID": "pm_1OaSwAFB8zXks4nkGMvOx0DH", "PaymentMethodDate": "January 20, 2024"}, {"Descriptor": "ONLINE TRAINING ENTERP", "PSP": "Nuvei", "ChargeflowID": "65b04c56ce4395b237297c77", "EnrollmentDate": "February 08, 2024", "MerchantId": "b851a3d6-47dc-46d7-8b4c-011aa9f0c539", "StripeCustomerID": "cus_PREMRz84ezRffX", "PaymentMethodID": "pm_1OcLtHFB8zXks4nk92A6S6jA", "PaymentMethodDate": "January 25, 2024"}, {"Descriptor": "ORTHOSPARKLE", "PSP": "Stripe", "ChargeflowID": "65b2a2475adcabe267fe93aa", "EnrollmentDate": null, "MerchantId": "cf7244bb-8289-4114-abe2-c22938468481", "StripeCustomerID": "cus_PSiTpzoFcNU0Gx", "PaymentMethodID": "pm_1Ods54FB8zXks4nkvV1PcF8A", "PaymentMethodDate": "January 29, 2024"}, {"Descriptor": "SP HARA BOUTIQUE", "PSP": "Shopify Payments", "ChargeflowID": "65b46ad34c7481e995886195", "EnrollmentDate": null, "MerchantId": "6d4d4b17-7abb-4dd7-91e1-2bfd4b0d50ff", "StripeCustomerID": "cus_PWs60ymffCDW9z", "PaymentMethodID": "pm_1OjpItFB8zXks4nk0f740X9C", "PaymentMethodDate": "February 09, 2024"}, {"Descriptor": "SONIC BRUSH", "PSP": "Stripe", "ChargeflowID": "65b629498f3d1a9c0be182b5", "EnrollmentDate": "February 13, 2024", "MerchantId": "8fa59d27-9635-4323-ab10-228d88bb1c9a", "StripeCustomerID": "cus_PSiTpzoFcNU0Gx", "PaymentMethodID": "pm_1Ods54FB8zXks4nkvV1PcF8A", "PaymentMethodDate": "January 29, 2024"}, {"Descriptor": "OPTIGUARD", "PSP": "Stripe", "ChargeflowID": "65b81d9c224b8e40b163c07b", "EnrollmentDate": "February 05, 2024", "MerchantId": "adaa1d20-a27a-4abf-84df-9b9b8965a545", "StripeCustomerID": "cus_PSyoj9Kc9zRW0u", "PaymentMethodID": "pm_1Oe2qiFB8zXks4nkVXWSmye4", "PaymentMethodDate": "January 29, 2024"}, {"Descriptor": "SENSEPRO", "PSP": "Stripe", "ChargeflowID": "65b81d9c224b8e40b163c07b", "EnrollmentDate": "January 31, 2024", "MerchantId": "2719c041-17bf-45f8-b866-83364ec09078", "StripeCustomerID": "cus_PSyoj9Kc9zRW0u", "PaymentMethodID": "pm_1Oe2qiFB8zXks4nkVXWSmye4", "PaymentMethodDate": "January 29, 2024"}, {"Descriptor": "EQUIWARMPRO", "PSP": "Stripe", "ChargeflowID": "65b81d9c224b8e40b163c07b", "EnrollmentDate": "January 31, 2024", "MerchantId": "b0bc1b55-263c-4d0f-a890-67569c7c15e6", "StripeCustomerID": "cus_PSyoj9Kc9zRW0u", "PaymentMethodID": "pm_1Oe2qiFB8zXks4nkVXWSmye4", "PaymentMethodDate": "January 29, 2024"}, {"Descriptor": "PHOTOTEK", "PSP": "Stripe", "ChargeflowID": "65b81d9c224b8e40b163c07b", "EnrollmentDate": "February 01, 2024", "MerchantId": "bf457683-a0cd-4cf0-add3-16d3bbf980f8", "StripeCustomerID": "cus_PSyoj9Kc9zRW0u", "PaymentMethodID": "pm_1Oe2qiFB8zXks4nkVXWSmye4", "PaymentMethodDate": "January 29, 2024"}, {"Descriptor": "EVOHEAT", "PSP": "Stripe", "ChargeflowID": "65b81d9c224b8e40b163c07b", "EnrollmentDate": "January 31, 2024", "MerchantId": "c147a4d3-814a-4988-9aac-b8262b7da64b", "StripeCustomerID": "cus_PSyoj9Kc9zRW0u", "PaymentMethodID": "pm_1Oe2qiFB8zXks4nkVXWSmye4", "PaymentMethodDate": "January 29, 2024"}, {"Descriptor": "EKOMSHOP", "PSP": "Stripe", "ChargeflowID": "65b81d9c224b8e40b163c07b", "EnrollmentDate": "January 31, 2024", "MerchantId": "99e1a94f-ef17-4aa0-a2de-0357b41fd0ce", "StripeCustomerID": "cus_PSyoj9Kc9zRW0u", "PaymentMethodID": "pm_1Oe2qiFB8zXks4nkVXWSmye4", "PaymentMethodDate": "January 29, 2024"}, {"Descriptor": "ConvertFlow", "PSP": "Stripe", "ChargeflowID": "65b81d9c224b8e40b163c07b", "EnrollmentDate": "February 01, 2024", "MerchantId": "85f9f23f-8ccf-445e-9cc1-e1bd16790552", "StripeCustomerID": "cus_PSyoj9Kc9zRW0u", "PaymentMethodID": "pm_1Oe2qiFB8zXks4nkVXWSmye4", "PaymentMethodDate": "January 29, 2024"}, {"Descriptor": "<PERSON><PERSON><PERSON>", "PSP": "Stripe", "ChargeflowID": "65b8f7bb9b4dddec5248769c", "EnrollmentDate": "February 21, 2024", "MerchantId": "71e42c3e-87e0-4205-a2f5-8560b10b10bd", "StripeCustomerID": "cus_PTURiP1irER5nv", "PaymentMethodID": "pm_1OfRvHFB8zXks4nkw9QxYygm", "PaymentMethodDate": "February 02, 2024"}, {"Descriptor": "CB4TRVL", "PSP": "Stripe", "ChargeflowID": "65bd3d0a3bd23d60851f20b4", "EnrollmentDate": "February 08, 2024", "MerchantId": "d98db96e-7a63-4817-a9a5-daa7295b4bea", "StripeCustomerID": "cus_PViSUi5V4ma7IC", "PaymentMethodID": "pm_1OhUMeFB8zXks4nkSGla8FT3", "PaymentMethodDate": "February 08, 2024"}, {"Descriptor": "Freight Broker Training", "PSP": "Authorized.net", "ChargeflowID": "65c1ffa69b97f76da0af4c57", "EnrollmentDate": "February 08, 2024", "MerchantId": "a9973672-00b6-480b-bb57-38c3f019206e", "StripeCustomerID": "cus_PVmpbzAgQhefBO", "PaymentMethodID": "pm_1OglFVFB8zXks4nk3iGdkBIl", "PaymentMethodDate": "February 06, 2024"}, {"Descriptor": "AI App", "PSP": "Stripe", "ChargeflowID": "65cc646a1a3d3cc1ebd63a16", "EnrollmentDate": null, "MerchantId": "727dafeb-86c6-495f-ad76-e7a68b55d294", "StripeCustomerID": "cus_PYjv0CUUOeZRCg", "PaymentMethodID": "pm_1OjcRLFB8zXks4nkIDGVPoSC", "PaymentMethodDate": "February 14, 2024"}, {"Descriptor": "SensePro Toothbrush", "PSP": "Stripe", "ChargeflowID": "65d3ca415d0078cf4692516a", "EnrollmentDate": null, "MerchantId": "8ddf4421-cc17-431d-972e-9dd60bc5a535", "StripeCustomerID": "cus_PSiTpzoFcNU0Gx", "PaymentMethodID": "pm_1Ods54FB8zXks4nkvV1PcF8A", "PaymentMethodDate": "January 29, 2024"}, {"Descriptor": "SONIC GLOW BRUSH", "PSP": "Stripe", "ChargeflowID": "65d4ce535bfe852a0986e4a2", "EnrollmentDate": "February 27, 2024", "MerchantId": "100c3ebe-4f81-48cf-805f-c0ad6899255a", "StripeCustomerID": "cus_PSiTpzoFcNU0Gx", "PaymentMethodID": "pm_1Ods54FB8zXks4nkvV1PcF8A", "PaymentMethodDate": "January 29, 2024"}, {"Descriptor": "minderse.com", "PSP": "Stripe", "ChargeflowID": "b5d994219cb3ef4ee0a782d9", "EnrollmentDate": "August 24, 2023", "MerchantId": "b5e3a861-1ea0-4db8-948a-dd84cd1493aa", "StripeCustomerID": "cus_NP9ksExZj7stL8", "PaymentMethodID": "pm_1MeLQcFB8zXks4nk8s0R4M7g", "PaymentMethodDate": "February 22, 2023"}, {"Descriptor": "ASKJENSANFORD", "PSP": "Stripe", "ChargeflowID": "6549329726d58ae8f408534e", "EnrollmentDate": "August 24, 2023", "MerchantId": "3ffca095-01f9-4c19-ab4e-2b3cc6029b7e", "StripeCustomerID": "cus_MrArItnl0eyIwc", "PaymentMethodID": "pm_1M7SVoFB8zXks4nkAAlaxDFx", "PaymentMethodDate": "February 23, 2023"}, {"Descriptor": "SP PRIVACY 250518", "PSP": "Shopify Payments", "ChargeflowID": "65aa2a2832ae2b39f4827c2b", "EnrollmentDate": "February 06, 2024", "MerchantId": "62daa22d-d4f0-4695-acfb-a9da37e1e054", "StripeCustomerID": "cus_PQ8jebs5CI0iNa", "PaymentMethodID": "pm_1ObZ9sFB8zXks4nkzdRyy2t6", "PaymentMethodDate": "January 22, 2024"}, {"Descriptor": "CROSSPPC.ORG", "PSP": "Stripe", "ChargeflowID": "65afb0be7e4e36cec3fad40c", "EnrollmentDate": "February 05, 2024", "MerchantId": "1e07518f-f692-4b71-8ec0-aafa887d70a4", "StripeCustomerID": "cus_PQaMA83pdiwl9W", "PaymentMethodID": "pm_1ObjC4FB8zXks4nkzp57455g", "PaymentMethodDate": "January 23, 2024"}, {"Descriptor": "CELESTIAL INSPIRATION", "PSP": "Stripe", "ChargeflowID": "65a118ab75864251fd545d58", "EnrollmentDate": "February 02, 2024", "MerchantId": "d94793c0-a4a8-4bc7-8a09-00dd03a31017", "StripeCustomerID": "cus_PMRDkCVF4NTvLM", "PaymentMethodID": "pm_1OXiLFFB8zXks4nkAFB68ftq", "PaymentMethodDate": "January 12, 2024"}, {"Descriptor": "VIRTUALSHIELD", "PSP": "Stripe", "ChargeflowID": "658620481c45a20cddce3b1c", "EnrollmentDate": "January 31, 2024", "MerchantId": "c193412f-75b8-4b34-8aa4-29c11a961595", "StripeCustomerID": "cus_PEm4M5jV5w7W23", "PaymentMethodID": "pm_1OQIVvFB8zXks4nkdElIfNgK", "PaymentMethodDate": "December 22, 2023"}, {"Descriptor": "SP GLOVBEAUTY.COM", "PSP": "Shopify Payments", "ChargeflowID": "65b1797eec2fac30a183acef", "EnrollmentDate": "February 23, 2024", "MerchantId": "035f93cb-cf46-4c6f-8c9d-aac9f45b4569", "StripeCustomerID": "cus_PREYvMZs97bXZ7", "PaymentMethodID": "pm_1OcZ7DFB8zXks4nkKry6E3bi", "PaymentMethodDate": "January 25, 2024"}, {"Descriptor": "ReviveHeat", "PSP": "Stripe", "ChargeflowID": "6599ca4771a96f4af8750d0d", "EnrollmentDate": "January 25, 2024", "MerchantId": "3d3abd17-7bf2-4def-b37a-e53111da2d24", "StripeCustomerID": "cus_POIf4rrxTCh1eX", "PaymentMethodID": "pm_1Oc9tNFB8zXks4nkfEfacv6e", "PaymentMethodDate": "January 19, 2024"}, {"Descriptor": "SP BELLEZAPLENITUD", "PSP": "Shopify Payments", "ChargeflowID": "65707f1c0b952529ae50a95d", "EnrollmentDate": "February 12, 2024", "MerchantId": "7f8c7cae-63c5-49f0-96fc-988af32a9acf", "StripeCustomerID": "cus_PDjZyrYb1ZWPA2", "PaymentMethodID": "pm_1OPNtQFB8zXks4nkXzgKHDx7", "PaymentMethodDate": "December 20, 2023"}, {"Descriptor": "PAWSTORE", "PSP": "Stripe", "ChargeflowID": "65c23f0ddefce62857b6a3f1", "EnrollmentDate": "March 04, 2024", "MerchantId": "c97b496e-c4d7-4805-be08-10058cceecc4", "StripeCustomerID": "cus_PWspIq2xVr42RU", "PaymentMethodID": "pm_1OoRoDFB8zXks4nkW9PDnCYh", "PaymentMethodDate": "February 09, 2024"}, {"Descriptor": "PAWSTORE", "PSP": "Stripe", "ChargeflowID": "65c23f0ddefce62857b6a3f1", "EnrollmentDate": "February 28, 2024", "MerchantId": "c97b496e-c4d7-4805-be08-10058cceecc4", "StripeCustomerID": "cus_PWspIq2xVr42RU", "PaymentMethodID": "pm_1OoRoDFB8zXks4nkW9PDnCYh", "PaymentMethodDate": "February 09, 2024"}, {"Descriptor": "Baked Bags", "PSP": "Authorize.net", "ChargeflowID": "65b9878b6d84cef7844a9592", "EnrollmentDate": null, "MerchantId": "0d8b4e10-9699-49a7-849d-bb169f4a0798", "StripeCustomerID": "cus_PYimJeIyVtGpc3", "PaymentMethodID": "pm_1OjnswFB8zXks4nkzBqSKXDl", "PaymentMethodDate": "February 14, 2024"}]
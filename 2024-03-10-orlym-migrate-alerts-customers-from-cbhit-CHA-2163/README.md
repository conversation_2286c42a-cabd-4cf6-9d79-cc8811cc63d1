# Purpose
This script migrates alerts customers from json file with existing customer from CBHit. 

https://linear.app/chargeflow/issue/CHA-2163/script-to-migrate-alerts-customers-existing-in-cbhit

## Motivation
Alerts is a subscription-based service which we are rolling out to our clients. 
We need to migrate the enrolled customers from CBHit to the new data structure: https://app.eraser.io/workspace/********************
Existing customers: https://docs.google.com/spreadsheets/d/1d2JPi-ns5myyqt3OpcWSGniIK54rctPLfe_vZIyyGok/edit#gid=1173271118

## Running this script
`npx ts-node ./migrate-existing-customers-from-cbhit.ts <dry or wet-secondary or wet-primary> 2>&1 | tee log.txt`

NOTE: a `.env` file is expected to be present in CWD - using `MONGO_URI`
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console - 
wet-secondary - not utilized in this script
wet-primary - updates the ALERTSREGISTRY & MERCHANTPROFILES & COMPANIES collections.

### How this script was tested
[x] New doc was created in alertsRegistry successfully.
[x] AlertsRegistry contains:
  [x] `chargeflowId`
  [x] `accountId`
  [x] `dateCreated`
  [x] `dateUpdated`
  [x] `authorizedAt`  
[x] New doc was created in merchantProfiles successfully.
[x] MerchantProfiles contains:
  [x] `chargeflowId`
  [x] `accountId`
  [x] `dateCreated`
  [x] `dateUpdated`
  [x] `isLinked`
  [x] `isActive`
  [x] `processor`
  [x] `statementDescriptor`
[x] 'users.billing.alertsSubscription' object was created in companies successfull.
[x] 'users.billing.alertsSubscription' contains:
  [x] `customerPortalUrl`      
  [x] `id`
  [x] `dateCreated`
  [x] `paymentMethodId`
  [x] `source`
  [x] `isSourceExpired`
import { ObjectId } from 'mongodb';

export type AlertsRegistry = {
  chargeflowId: ObjectId;
  accountId: ObjectId | null;
  dateCreated: Date | null,
  dateUpdated: Date | null;
  authorizedAt: Date | null;
};

export type MerchantProfile = {
  chargeflowId: ObjectId;
  accountId?: ObjectId | null;
  dateCreated: Date | null;
  dateUpdated?: Date | null;
  isLinked: boolean;
  isActive: boolean;
  processor: string;
  statementDescriptor: string;
};

export type MidDataItem = {
  ChargeflowID: string; 
  Descriptor: string,
  PSP: string,
  EnrollmentDate: string | null,
  MerchantId: string,
  StripeCustomerID: string | null,
  PaymentMethodID: string | null,
  PaymentMethodDate: string | null,
}
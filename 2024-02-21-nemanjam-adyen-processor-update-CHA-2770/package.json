{"name": "2024-02-21-nemanjam-adyen-processor-update-cha-2770", "version": "1.0.0", "description": "This scripts adds new properties to processor object: Connect object is changes and has 2 new properties. Also \"apiKey\" is flattened and is no longer object ```json \"connect\" : {   \"apiKey\": \"string\",   \"hmac\": \"string\",   \"webhookID\": \"string\",   \"companyID\": \"string\" } ``` Also additional data object now stores meData and WebhookData ```json \"additionalData: {   \"meData\": Object,   \"additionalData\": Object, } ```", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@adyen/api-library": "^15.1.0", "@chargeflow-team/chargeflow-utils-sdk": "^1.0.77"}}
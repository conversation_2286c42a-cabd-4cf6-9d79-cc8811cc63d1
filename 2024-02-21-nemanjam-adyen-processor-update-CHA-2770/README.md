# Purpose
This scripts adds new properties to processor object:
Connect object is changes and has 2 new properties. Also "apiKey" is flattened and is no longer object
```json
"connect" : {
  "apiKey": "string",
  "hmac": "string",
  "webhookID": "string",
  "companyID": "string"
}
```
Also additional data object now stores meData and WebhookData
```json
"additionalData: {
  "meData": Object,
  "additionalData": Object,
}
```

Additionally we need to remove old hooks and add new hooks, so we use processorId as identifier, and to update configuration.
https://linear.app/chargeflow/issue/CHA-2770/update-existing-processors-and-webhooks

## Motivation
We wanted to onboard clients and check data that we were getting from webhooks, so we deployed code that was in beta state.
Now we added HMAC for security and changed webhooks to use processorId as identifier and not company slug

### Modes
dry - does not update anything, writes the results to the console</br>
wet-secondary - Not implemented<br>
wet-primary - UPDATES THE PROCESSORS TABLE AND DELETES EXISTING WEBHOOKS AND CREATES NEW ONES. Use with extreme caution!

### How this script was tested
[x] We copied data from the production database, and changed webhook ids to match webhook ids that we have on test env
[x] We changed apiKey to be our test api key
[x] We ran the script and checked if processor api had new information

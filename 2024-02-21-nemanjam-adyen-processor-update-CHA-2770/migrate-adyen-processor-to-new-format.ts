import {<PERSON><PERSON>tB<PERSON>, ScriptContext} from "../shared/script-base";
import {loadMongoClient} from "../shared/mongo-config";
import { ObjectId } from "mongodb";
import {getMeData, removeWebhook, addHMAC, registerWebhook} from "./adyen-util";

class Script extends ScriptBase {
    getAuthor(): string {
        return "Nemanja Malocic";
    }

    getName(): string {
        return "Migrate adyen processor to new format";
    }

    getTicket(): string {
        return "CHA-2770";
    }

    async dryRun(context: ScriptContext) {
        console.log("Running in dry run (not changing anything)");
        const client = await loadMongoClient();
        const processorsToUpdate = await client.db('chargeflow_api')
            .collection('processors')
            .find( { 'processor_name': 'adyen' })
            .toArray();
        const chargeFlowIds = processorsToUpdate.map( (processor: any) => processor.chargeflow_id);
        const shops = await client.db('chargeflow_api').collection('shops').find(
            {
                'chargeflow_id': { $in: chargeFlowIds },
            },
        ).toArray();

        for (const processor of processorsToUpdate) {
            try {
                console.log(`Processing processor with id: ${processor._id}`);
                const apiKey = processor.connect.apiKey['api_key'];
                const webhookID = processor['additional_data'].id;
                const processorId = processor._id.toString();
                const shopName
                    = shops
                    .filter(_ => _.chargeflow_id.toString() ===
                        processor.chargeflow_id.toString())[0].name;
                console.table([apiKey, webhookID, processorId, shopName]);
                const meData = await getMeData(apiKey, shopName);
                console.log('Got my data object');
                console.log(meData);
                // await removeWebhook(apiKey, meData.companyName!, webhookID);
                console.log("Here we do 3rd party work that we shouldn't to in dry run");
                console.log(`Removed webhook with id: ${webhookID}, for processor: ${processor._id}`);
                //const webhookData = await registerWebhook(apiKey, processorId, meData, shopName);
                console.log("Here we register webhook and we shouldn't to that in dry run.")
                console.log('Registered new webhook');
                // console.log(webhookData);
                // const hmac = await addHMAC(apiKey, meData.companyName!, webhookData.id!, shopName);
                console.log("Here we should add hmac to webhook but we don't have webhook because it is dry run")
                console.log('Added HMAC');
                // console.log(hmac);
                console.log("Here we construct new processor object but we are missing bunch of data so we don't do that")
                // const updateProcessor = {
                //     ...processor,
                //     'date_updated': new Date(),
                //     connect: {
                //         apiKey: apiKey,
                //         hmac: hmac.hmacKey,
                //         webhookID: webhookData.id,
                //         companyID: meData.companyName,
                //     },
                //     'additional_data': {
                //         meData,
                //         webhookData,
                //     },
                // };
                console.log('updating processor');
                // console.log(updateProcessor);
                // await client.db('chargeflow_api').collection('processors')
                //     .replaceOne(
                //         {
                //             '_id': new ObjectId(processorId),
                //         },
                //         processor,
                //     );
                console.log('Processor updated.');
            } catch ( err) {
                console.error(`Error updating processor data for ${processor._id}`);
                console.error(err);
            }
        }
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        return await this.doWetRun(context)
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        return Promise.resolve(undefined);
    }

    private async doWetRun(context: ScriptContext): Promise<void> {
        const client = await loadMongoClient();
        const processorsToUpdate = await client.db('chargeflow_api')
            .collection('processors')
            .find( { 'processor_name': 'adyen' })
            .toArray();
        const chargeFlowIds = processorsToUpdate.map( (processor: any) => processor.chargeflow_id);
        const ids = chargeFlowIds.map( (item:string) => new ObjectId(item));
        const shops = await client.db('chargeflow_api').collection('shops').find(
            {
                'chargeflow_id': { $in: ids },
            },
        ).toArray();

        for (const processor of processorsToUpdate) {
            try {
                console.log(`Processing processor with id: ${processor._id}`);
                const apiKey = processor.connect.apiKey['api_key'];
                const webhookID = processor['additional_data'].id;
                const processorId = processor._id.toString();
                const shopName
                    = shops
                    .filter(_ => _.chargeflow_id.toString() ===
                        processor.chargeflow_id.toString())[0].name;
                console.table([apiKey, webhookID, processorId, shopName]);
                const meData = await getMeData(apiKey, shopName);
                console.log('Got my data object');
                console.log(meData);
                await removeWebhook(apiKey, meData.companyName!, webhookID);
                console.log(`Removed webhook with id: ${webhookID}, for processor: ${processor._id}`);
                const webhookData = await registerWebhook(apiKey, processorId, meData, shopName);
                console.log('Registered new webhook');
                console.log(webhookData);
                const hmac = await addHMAC(apiKey, meData.companyName!, webhookData.id!, shopName);
                console.log('Added HMAC');
                console.log(hmac);
                const updateProcessor = {
                    ...processor,
                    'date_updated': new Date(),
                    connect: {
                        apiKey: apiKey,
                        hmac: hmac.hmacKey,
                        webhookID: webhookData.id,
                        companyID: meData.companyName,
                    },
                    'additional_data': {
                        meData,
                        webhookData,
                    },
                };
                console.log('updating processor');
                console.log(updateProcessor);
                await client.db('chargeflow_api').collection('processors')
                    .replaceOne(
                        {
                            '_id': new ObjectId(processorId),
                        },
                        processor,
                    );
                console.log('Processor updated.');
            } catch ( err) {
                console.error(`Error updating processor data for ${processor._id}`);
                console.error(err);
            }
        }
    }
}
new Script().main();
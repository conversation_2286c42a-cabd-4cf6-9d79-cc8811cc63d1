import { Client, ManagementAPI} from '@adyen/api-library';
import  { CreateCompanyWebhookRequest } from "@adyen/api-library/lib/src/typings/management/createCompanyWebhookRequest"
import { ErrorUtil} from "@chargeflow-team/chargeflow-utils-sdk";
import {MeApiCredential} from "@adyen/api-library/lib/src/typings/management/meApiCredential";

export const getMeData = async (apiKey: string, shopName: string): Promise<MeApiCredential> => {
    const api = createClient(apiKey);
    let meData: MeApiCredential;

    try {
        meData = await api.MyAPICredentialApi.getApiCredentialDetails();
    } catch (err) {
        const error = err as any
        console.error(
            'Error checking api credential details: %s, shopName: %s', error.toString(), shopName);
        if (error.statusCode === 401) {
            ErrorUtil.throwError('UnauthorizedError',
                `Error checking api credentials for shop: ${shopName}`, error);
        } else {
            ErrorUtil.throwError('BadRequestError',
                `Error getting client data, shopName: ${shopName}`, error);
        }
    }

    if (!meData.companyName) {
        // Not logging meData object because it might contain sensitive information
        console.error(
            'Could not resolve company name, company level api_key needed shopName: %s', shopName);
        ErrorUtil.throwError('BadRequestError','Company level api_key needed');
    }

    if (!meData.roles.includes('Management API - Webhooks read and write')) {
        console.info('Insufficient roles in token', meData.roles);
        ErrorUtil.throwError('BadRequestError',
            'Manage web hook read write role required', meData.roles);

    }
    return meData;
};

export const registerWebhook = async (apiKey: string, processorId: string, meData: MeApiCredential, shopName:string) => {
    const api = createClient(apiKey);
    const url = new URL(processorId, process.env.ADYEN_WEBHOOK_URL);

    const createWebHookRequest = {
        type : WebHookType,
        url: url.href,
        active : true,
        communicationFormat : CreateCompanyWebhookRequest.CommunicationFormatEnum.Json,
        filterMerchantAccountType :
        CreateCompanyWebhookRequest.FilterMerchantAccountTypeEnum.AllAccounts,
        filterMerchantAccounts: [],
        additionalSettings: AdditionalSettings,
    };

    try {
        return await api
            .WebhooksCompanyLevelApi
            .setUpWebhook(meData.companyName!, createWebHookRequest);
    } catch (err) {
        const error = err as any
        console.error(
            'Error creating a webhook: %s, shopName: %s', error.toString(), shopName);
        ErrorUtil.throwError('BadRequestError',
            `Error creating webhook for: ${shopName}`, createWebHookRequest);
    }
};

export const removeWebhook = async (apiKey:string, companyId: string, webhookId: string) => {
    const client = createClient(apiKey);
    await client.WebhooksCompanyLevelApi.removeWebhook(companyId, webhookId);
};

export const addHMAC = async (apiKey: string, companyName: string, webhookId: string, shopName: string) => {
    const api = createClient(apiKey);

    try {
        return await api.WebhooksCompanyLevelApi.generateHmacKey(companyName, webhookId);
    } catch (err) {
        const error = err as Error
        console.error(
            'Error creating a hmac: %s, shopName: %s', error.toString(), shopName);
        ErrorUtil.throwError('BadRequestError',
            `Error creating hmac for: ${shopName}`);
    }
};

const createClient = (apiKey: string) => {
    const environment : Environment = (process.env.ADYEN_ENV || "TEST") as Environment;
    const client = new Client( { apiKey: apiKey, environment: environment });
    return  new ManagementAPI(client);
};

const AdditionalData= {
    includePosTerminalInfo: true,
    includeARN: true,
    includeExtraCostsSurcharge : true,
    includePaymentAccountOwnerDetails : true,
    includePosDetails : true,
    includeCardInfoForRecurringContractNotification : true,
    includeRiskData : true,
    includeRiskExperimentReference : true,
    includeSoapSecurityHeader : true,
    includeContactlessWalletTokenInformation : true,
    includeExtraCostsGratuity : true,
    includeAdjustAuthorisationData : true,
    includeAcquirerReference : true,
    includeRiskProfileReference : true,
    includeOriginalMerchantReferenceCancelOrRefundNotification : true,
    includeNfcTokenInformation : true,
    includeSubvariant : true,
    includeThreeDSVersion : true,
    includeInstallmentsInfo : true,
    includeAliasInfo : true,
    includeShopperCountry : true,
    includeRawThreeDSecureResult : true,
    includeAirlineData : true,
    includeGrossCurrencyChargebackDetails : true,
    includeThreeDSecureResult : true,
    includeMetadataIn3DSecurePaymentNotification : true,
    includeGiftCardNumber : true,
    includeOriginalReferenceForChargebackReversed : true,
    addAcquirerResult : true,
    includeDeliveryAddress : true,
    includeRetryAttempts : true,
    includeVisaRdrDisputeIndicator : true,
    includeExtraCosts : true,
    includeCardHolderName : true,
    includeShopperDetail : true,
    includeBankAccountDetails : true,
    includeMandateDetails : true,
    includeAuthAmountForDynamicZeroAuth : true,
    includeIssuerCountry : true,
    includeAcquirerErrorDetails : true,
    includeCoBrandedWith : true,
    includeShopperInteraction : true,
    includeDeviceAndBrowserInfo : true,
    includeUpiVpa : true,
    includePixEndToEndId : true,
    addRawAcquirerResult : true,
    includeCardBin : true,
    includeFundingSource : true,
    includeThreeDS2ChallengeInformation : true,
    includeRiskProfile : true,
    includeRealtimeAccountUpdaterStatus : true,
    includePixPayerInfo : true,
    includeDunningProjectData : true,
    includePaymentResultInOrderClosedNotification : true,
    includeCardBinDetails : true,
    includeNotesInManualReviewNotifications : true,
    includeZeroAuthFlag : true,
    addCaptureReferenceToDisputeNotification : true,
    includePayPalDetails : true,
    includeRawThreeDSecureDetailsResult : true,
    includeBankVerificationResults : true,
    includeCaptureDelayHours : true,
    addPaymentAccountReference : true,
    includePayULatamDetails : true,
    includeStore : true,
    returnAvsData : true,
    includeWeChatPayOpenid : true,
    includeCustomRoutingFlagging : true,
    includeTokenisedPaymentMetaData : true,
};
const AdditionalSettings = {
    properties: AdditionalData,
    includeEventCodes: [
        'CHARGEBACK_REVERSED',
        'REQUEST_FOR_INFORMATION',
        'NOTIFICATION_OF_CHARGEBACK',
        'CHARGEBACK',
        'PREARBITRATION_WON',
        'SECOND_CHARGEBACK',
        'PREARBITRATION_LOST',
        'DISPUTE_DEFENSE_PERIOD_ENDED',
        'ISSUER_RESPONSE_TIMEFRAME_EXPIRED',
    ],
};
const WebHookType = 'standard';

import dotenv from 'dotenv';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Db, ObjectId } from 'mongodb';
import fetch from 'node-fetch';
import crt from 'aws-crt';
import { HttpRequest } from 'aws-crt/dist/native/http';

dotenv.config();

const disputesWithDates = [
    {"_id":"","dispute":{"id":""},"date":"YYYY-MM-DD"},
]

const MongoClient = require('mongodb').MongoClient;
require('dotenv').config();

const MANUAL_CHARGE_SOURCE = 'Manual Charge';
const USAGE_CHARGE_TYPE =  'Usage Charge';
const USD_CURRENCY = 'USD';

function signV4({ method, endpoint, service, AWS_REGION }) {
    const host = new URL(endpoint).host;
    const request = new HttpRequest(method, endpoint);
    request.headers.add('host', host);

    const config = {
        service: service,
        region: AWS_REGION,
        algorithm: crt.auth.AwsSigningAlgorithm.SigV4,
        'signature_type': crt.auth.AwsSignatureType.HttpRequestViaHeaders,
        'signed_body_header': crt.auth.AwsSignedBodyHeaderType.XAmzContentSha256,
        provider: crt.auth.AwsCredentialsProvider.newDefault(),
    };

    crt.auth.aws_sign_request(request, config);
    return request.headers;
}

const toObjectIds = charges => charges.map(charge => new ObjectId(charge.disputeId));
const createBulkToUpdateCharges = (mappedCharges) => {
    const bulk = mappedCharges.reduce((acc, mappedCharge) => {
        const chargesQuery = {
            updateOne :
                {
                    'filter' : {
                        'caseId' : mappedCharge.caseId,
                    },
                    'update' : {
                        $set : mappedCharge,
                    },
                    'upsert' : true,
                },
        };
        acc.push(chargesQuery);
        return acc;
    }, []);
    

    return bulk;
};

let client;
async function getMongoClient() {
    if (!client) {
        client = await MongoClient.connect(process.env.MONGO_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        }).catch(err => {
            console.trace('MongoClient.connect failed: %s', err);
            throw err;
        });
    }
    return client;
}


export class MarkShopifyDisputesAsCharged extends ScriptBase {
  db: Db | undefined = undefined;

  public getName(): string {
    return "Create script to mark shopify disputes as charged";
  }

  public getAuthor(): string {
    return "Rafael Schuster";
  }

  public getTicket(): string {
    return "CHA-12015";
  }
  async dryRun(context: ScriptContext): Promise<void> {
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(context: ScriptContext, isDryRun: boolean): Promise<void> {
    console.log('Marking Shopify disputes as charged..');

    const mappedCharges: Array<{
        dateCreated: Date;
        chargeflowId: any;
        billingId: null;
        billingDate: null;
        billingStatus: null;
        disputeId: any;
        caseId: any;
        disputeProcessor: any;
        shopId: any;
        shopName: any;
        shopPlatform: any;
        disputeRecoveredAmount: number | null;
        successAmount: number;
        chargedAmount: number;
        currency: string;
        resolvedDate: Date;
        chargeSource: string;
        chargeType: string;
        chargeId: null;
        recurringAppChargeId: null;
        chargeDescription: null;
        rawData: {};
    }> = [];

    for (const disputeWithDate of disputesWithDates) {
        try {
            console.log('Processing dispute id: %s', disputeWithDate._id);
            const mappedCharge = await processManualCharge(disputeWithDate);
            mappedCharges.push(mappedCharge);

        } catch (err) {
            console.error('Failed to process dispute id: %s, err: %s', disputeWithDate._id, err);
        }
    }

    const bulk = createBulkToUpdateCharges(mappedCharges);
    const resBulkWrite= await bulkWriteCharges(bulk);
    console.log(
        'Successfully upserted %s charges from %s disputes',
        resBulkWrite.nUpserted,
        disputesWithDates.length,
    );
    console.log('Upserted charges: %j', resBulkWrite);

    const objectIds = toObjectIds(mappedCharges);
    const resDelete = await deleteManyPendingCharges(objectIds);
    console.log('Successfully deleted %s pending charges', resDelete.deletedCount);
  }
}

async function processManualCharge(disputeWithDate) {

    if (!disputeWithDate) {
        throw new Error('Missing disputeWithDate');
    }

    const disputeId = disputeWithDate._id;

    const dispute = await findOneDispute(disputeId);
    if (!dispute) {
        throw new Error(`Failed to find dispute by id: ${disputeId}`);
    }

    const shopName = dispute.shopName;
    const shop = await findOneShopByShopName(shopName);
    if (!shop) {
        throw new Error(`Failed to find shop: ${shopName}`);
    }

    const roundedFinalPrice = await calculateRoundedFinalPrice(shop, dispute);
    if (!roundedFinalPrice) {
        throw new Error(`Failed to calculateRoundedFinalPrice: ${shopName}, ` +
            `dispute: ${disputeId}`);
    }

    const mappedCharge = mapManualCharge(dispute, roundedFinalPrice, shop, disputeWithDate);
    if (!mappedCharge) {
        throw new Error(`Failed to mapManualCharge for dispute: ${disputeId}`);
    }

    return mappedCharge;
}

const sendS2SRequest = async ({
    url,
    method,
    body = undefined,
}) => {
    const AWS_REGION  = 'us-east-1'

    const apiUrl = new URL(url);

    try {
        const sigV4Params = {
            service: 'execute-api',
            endpoint:url,
            method,
            AWS_REGION,
        };
        const headers = signV4(sigV4Params);

        const signParams = {
            method,
            hostname: apiUrl.host,
            path: apiUrl.pathname,
            protocol: apiUrl.protocol,
            headers: Object.fromEntries(headers._flatten()),
            ...(body ? { body: JSON.stringify(body) }: {}),
        };
        const fetchRes = await fetch(apiUrl, signParams);
        if (!fetchRes.ok) {
            const errMsg = await fetchRes.text();
            throw new Error(errMsg);
        } else {
            const json = await fetchRes.json();
            return json;
        }
    } catch (error) {
        console.log('An error occurred', error);
        throw error;
    }
};


async function getUsdQuote(currency) {

    return await sendS2SRequest({
        url: `https://1v7l0yqhhf.execute-api.us-east-1.amazonaws.com/prod?currencies=${currency}`,
        method: 'GET',
    }).then(res => 1/Number(res.rates[currency]) || 1);
}


async function bulkWriteCharges(bulk) {
    const client = await getMongoClient();
    return client.db('chargeflow_api')
        .collection('charges')
        .bulkWrite(bulk)
        .catch(err => {
            console.error('Failed to bulkWriteCharges %s, err: %s', err);
            throw err;
        });
}

async function deleteManyPendingCharges(disputeIds) {

    if (!disputeIds.length) {
        throw new Error('Missing disputeIds');
    }

    const client = await getMongoClient();
    return client
        .db('chargeflow_api')
        .collection('pendingCharges')
        .deleteMany({ disputeId: { $in: disputeIds } })
        .catch(err => {
            if (err.code === 'ECONNREFUSED') {
                throw new Error(`No internet connection: ${err}`);
            }
            throw err;
        });
}

async function findOneDispute(disputeId) {

    if (!disputeId) {
        throw new Error('Missing disputeId');
    }

    const client = await getMongoClient();

    const projection = {
        'chargeflowId': 1,
        'shopName': 1,
        'dispute.id': 1,
        'dispute.amount': 1,
        'dispute.currency': 1,
        'dispute.processor': 1,
        'chargeflow': 1,
        'shopId': 1,
    };

    return client
        .db('chargeflow_api')
        .collection('disputes')
        .findOne({ _id: new ObjectId(disputeId) }, { projection })
        .catch(err => {
            if (err.code === 'ECONNREFUSED') {
                throw new Error(`No internet connection: ${err}`);
            }
            throw err;
        });
}

async function findOneShopByShopName(shopName) {

    if (!shopName) {
        throw new Error('Missing shopName');
    }

    const client = await getMongoClient();

    const projection = {
        'chargeflow_id': 1,
        'name': 1,
        'chargeflow_success_rate': 1,
        'connect': 1,
        'platform': 1,
        'chargeflow_status': 1,
    };

    return client
        .db('chargeflow_api')
        .collection('shops')
        .findOne( { 'name': shopName }, { projection })
        .catch(err => {
            if (err.code === 'ECONNREFUSED') {
                throw new Error(`No internet connection: ${err}`);
            }
            throw err;
        });
}

async function calculateRoundedFinalPrice(shop, dispute) {

    if (!shop) {
        throw new Error('Missing shop');
    }

    if (!dispute) {
        throw new Error('Missing dispute');
    }

    const successRate = shop.chargeflow_success_rate;
    if (!successRate) {
        throw new Error(`Missing success rate for shop: ${shop.name}`);
    }

    const amount = dispute.dispute.amount;
    if (!amount) {
        throw new Error(`Missing dispute amount for shop: ${shop.name}`);
    }

    const currency = dispute.dispute.currency;
    if (!currency) {
        throw new Error(`Missing currency for shop: ${shop.name}`);
    }

    let usdQuote = 1;
    if (currency !== 'USD') {
        usdQuote = await getUsdQuote(currency);
        if (!usdQuote) {
            throw new Error(`Failed to get USD quote for shop: ${shop.name}, ` +
                `currency: ${currency}`);
        }
    }

    const finalPrice = amount * (successRate / 100) * usdQuote;
    const roundedFinalPrice = Number(finalPrice.toFixed(4));
    if (isNaN(roundedFinalPrice)) {
        throw new Error(`Invalid final price for shop: ${shop.name}, ` +
            `amount: ${amount}, ` +
            `success rate: ${successRate}, ` +
            `usd quote: ${usdQuote}`);
    }

    return roundedFinalPrice;
}

function mapManualCharge(dispute, finalPrice, shop, disputeWithDate) {

    if (!dispute) {
        throw new Error('Missing dispute');
    }

    if (!finalPrice) {
        throw new Error('Missing finalPrice');
    }

    if (!shop) {
        throw new Error('Missing shop');
    }

    const chargeObj = {
        dateCreated: new Date(disputeWithDate.date),
        chargeflowId: dispute.chargeflowId,
        billingId: null,
        billingDate: null,
        billingStatus: null,
        disputeId: dispute._id,
        caseId: dispute.dispute?.id || null,
        disputeProcessor: dispute.dispute?.processor || null,
        shopId: dispute.shopId || null,
        shopName: dispute.shopName || null,
        shopPlatform: shop.platform || null,
        disputeRecoveredAmount: dispute.dispute?.amount ? Number(dispute.dispute?.amount) : null,
        successAmount: finalPrice,
        chargedAmount: Number(finalPrice),
        currency: USD_CURRENCY,
        resolvedDate: new Date(disputeWithDate.date),
        chargeSource: MANUAL_CHARGE_SOURCE,
        chargeType: USAGE_CHARGE_TYPE,
        chargeId: null,
        recurringAppChargeId: null,
        chargeDescription: null,
        rawData: {},
    };

    return chargeObj;
}

const script = new MarkShopifyDisputesAsCharged();
script.main();
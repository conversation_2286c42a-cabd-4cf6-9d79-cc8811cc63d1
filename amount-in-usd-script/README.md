This script is for updating  'amountInUsd' field retro actively for our insights project.

How to populate historic data file (from camount-in-usd-script folder):
-Important to note this script does not touch our DB...
    1. run `npm install`
    2. In your .env file, within this folder, set value for `OPEN_EXCHANGE_RATES_APP_ID` (ssm param name: prod-open-exchange-app-id )
    3.run `node populateHistoricData.js`


How to run the script (from amount-in-usd-script folder):
    1. In your .env file, within this folder, set value for `MONGO_URI`
    2. run `node convertToUsd.js`

Finally to execute the queries:
    1. run `npx ts-node executeUpdates.ts`
require('dotenv').config();
const fs = require('fs');
const { MongoClient, ObjectId } = require('mongodb');
const exchangeRates = require('./year-exchange-rate.json');

const MONGO_URI = process.env.MONGO_URI;

let mongoClient;

const unableToGetUsdDisputes = [];

async function run() {
  try {
    mongoClient = await loadMongoClient();
    await populateAmountInUsd();
  } catch (err) {
    console.log(err);
  }
  if(unableToGetUsdDisputes.length > 0) {
  fs.writeFileSync(
    'unable-get-usd.json',
    JSON.stringify(unableToGetUsdDisputes)
  );
  }

  process.exit(1);
}

run()
  .catch(console.dir)
  .finally(() => {
    process.exit(1);
  });

async function loadMongoClient() {
  if (mongoClient) {
    return mongoClient;
  }
  const client = await MongoClient.connect(MONGO_URI).catch(err => {
    console.error('MongoClient.connect failed: %s', err);
    throw err;
  });
  return client;
}

function computeUSDAmount(exchangeDate, amount, currency) {
  const rate = exchangeRates[exchangeDate][currency];
  const amountInUsd = getTwoDigitsNumber(Number(amount) / Number(rate))
  // console.log(Number(amount), Number(rate), amountInUsd);
  return amountInUsd;
}

function getUSDAmount(dispute) {
  const { amount, currency, dateReceived } = dispute?.dispute || {};
  const conversionDate = dateReceived ? dateReceived : dispute.dateCreated
  if (amount === 0 || amount===null || currency === 'USD') {
    return amount;
  }
  if (amount === undefined || !currency || !conversionDate) {
    return;
  }
  const exchangeDate = conversionDate.toISOString().slice(0, 10);
  if (Object.keys(exchangeRates).includes(exchangeDate)) {
    return Number(computeUSDAmount(exchangeDate, amount, currency));
  }

  unableToGetUsdDisputes.push({disputeId:dispute._id, reason:'exchangeDate not found in exchangeRates'});
}

async function populateAmountInUsd() {
  let lastId = null;
  const limit = 1000;
  let totalProcessed = 0;
  let updatedIds = [];
  let hasMore = true;
  while (hasMore) {
    const disputesToReconcile = await getDisputesToReconciliate(lastId, limit);
    console.log('disputesToReconcile', disputesToReconcile.length);
    if (disputesToReconcile.length === 0) {
      break;
    }

    console.log(
      'Processing batch starting from _id:',
      disputesToReconcile[0]._id
    );

    lastId = disputesToReconcile[disputesToReconcile.length - 1]._id;

    const updateQueries = disputesToReconcile
      .map(getUpdateQuery)
      .filter(Boolean);

    const res = await updateAmountInUsd(updateQueries);
    fs.appendFile('updateResults.json', JSON.stringify(res || ''), logError);

    updatedIds = updatedIds.concat(disputesToReconcile.map(d => d._id));
    const numProcessed = res?.result?.nModified || 0;
    totalProcessed += numProcessed;
    hasMore = disputesToReconcile.length === limit;
  }

  console.log('Total disputes processed:', totalProcessed, updatedIds);
}

async function getDisputesToReconciliate(lastId, limit = 1000) {
  const matchQuery = lastId ? { _id: { $gt: new ObjectId(lastId) } } : {};

  const agg = [
    {
      $match: {
        ...matchQuery,
        $or: [
          { 'dispute.amountInUsd': { $exists: false } },
          { "dispute.amountInUsd": { $not: { $type: "number" } } }
        ]
      },
    },
    {
      $project: {
        'dateCreated':1,
        'dispute.dateReceived': 1,
        'dispute.amount': 1,
        'dispute.currency': 1,
      },
    },
    { $sort: { _id: 1 } },
    { $limit: limit },
  ];
  return mongoClient
    .db('chargeflow_api')
    .collection('disputes')
    .aggregate(agg)
    .toArray();
}

function getUpdateQuery(dispute) {
  const findDispute = { _id: new ObjectId(dispute._id) };
  const usdAmount = getUSDAmount(dispute);
  if (usdAmount===undefined) {
    unableToGetUsdDisputes.push({disputeId:dispute._id, reason:'not able to calculate usdAmount due to missing data'});
    return;
  }
  const updateUsdAmount = {
    $set: {
      dateUpdated: new Date(),
      'dispute.amountInUsd': usdAmount,
    },
  };
  return { updateOne: { filter: findDispute, update: updateUsdAmount } };
}

async function updateAmountInUsd(operationsArray) {
  if (!operationsArray.length) {
    console.log('no operationsArray', JSON.stringify(operationsArray));
    return;
  }
  fs.appendFile(
    'updateQueries.json',
    JSON.stringify(operationsArray),
    logError
  );

  // return await mongoClient
  //   .db('chargeflow_api')
  //   .collection('disputes')
  //   .bulkWrite(operationsArray)
  //   .catch(err => {
  //     console.log(err);
  //     throw err;
  //   });
}

function getTwoDigitsNumber(amount) {
  return Number(amount).toFixed(2);
}

function logError(error) {
  if (error) console.log('Error occured: ', error);
}

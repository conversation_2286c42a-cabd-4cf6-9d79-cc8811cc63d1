import dotenv from 'dotenv';
import fs from 'fs';
import { MongoClient, ObjectId, BulkWriteResult } from 'mongodb';

dotenv.config();

const MONGO_URI = process.env.MONGO_URI as string;

interface UpdateQuery {
  updateOne: {
    filter: {
      _id: string;
    };
    update: {
      $set: {
        dateUpdated: string;
        'dispute.amountInUsd': number | null;
      };
    };
  };
}

interface FormattedUpdate {
  updateOne: {
    filter: {
      _id: ObjectId;
    };
    update: {
      $set: {
        dateUpdated: string;
        'dispute.amountInUsd': number | null;
      };
    };
  };
}

let mongoClient: MongoClient | null = null;

async function run(): Promise<void> {
  try {
    mongoClient = await loadMongoClient();
    await executeUpdateQueries();
  } catch (err) {
    console.log(err);
  }
  process.exit(1);
}

run()
  .catch(console.dir)
  .finally(() => {
    process.exit(1);
  });

async function loadMongoClient(): Promise<MongoClient> {
  if (mongoClient) {
    return mongoClient;
  }
  const client = await MongoClient.connect(MONGO_URI).catch((err: Error) => {
    console.error('MongoClient.connect failed: %s', err);
    throw err;
  });
  return client;
}

async function executeUpdateQueries(): Promise<void> {
  try {
    // Read the file content
    const fileContent = fs.readFileSync('updateQueries.json', 'utf-8');
    
    // Clean up the JSON content
    // Replace multiple JSON arrays with a single array
    const cleanedContent = '[' + fileContent
      .replace(/\]\[/g, ',')  // Replace adjacent arrays
      .replace(/^\[|\]$/g, '') // Remove outer brackets
      + ']';

    // Parse the cleaned JSON
    const updateQueries: UpdateQuery[] = JSON.parse(cleanedContent);

    // Format the updates to ensure ObjectId is properly converted
    const formattedUpdates: FormattedUpdate[] = updateQueries.map(query => ({
      updateOne: {
        filter: {
          _id: new ObjectId(query.updateOne.filter._id)
        },
        update: query.updateOne.update
      }
    }));

    console.log(`Total updates to process: ${formattedUpdates.length}`);

    // Split the updates into batches of 1000
    const batchSize = 1000;
    let totalProcessed = 0;
    
    for (let i = 0; i < formattedUpdates.length; i += batchSize) {
      const batch = formattedUpdates.slice(i, i + batchSize);
      
      console.log(`Processing batch starting from index: ${i}`);
      
      const result = await updateBatch(batch);
      
      if (result) {
        fs.appendFile('updateResults.json', JSON.stringify(result) + '\n', logError);
        totalProcessed += result.modifiedCount;
        
        console.log(`Batch results:`, {
          matched: result.matchedCount,
          modified: result.modifiedCount
        });
      }
    }

    console.log('Total documents processed:', totalProcessed);

  } catch (error) {
    console.error('Error executing updates:', error);
    throw error;
  }
}

async function updateBatch(operationsArray: FormattedUpdate[]): Promise<BulkWriteResult | null> {
  if (!operationsArray.length || !mongoClient) {
    return null;
  }

  return await mongoClient
    .db('chargeflow_api')
    .collection('disputes')
    .bulkWrite(operationsArray)
    .catch((err: Error) => {
      console.log(err);
      throw err;
    });
}

function logError(error: Error | null): void {
  if (error) console.log('Error occurred: ', error);
}
const dayjs = require('dayjs');
const fs = require('fs').promises;
const axios = require('axios');
const exchangeRates = require('./year-exchange-rate.json');
const dotenv = require('dotenv');
dotenv.config();

const startDate = dayjs('2021-10-01');

const endDate = dayjs();

async function getHistoricalConvRate(date) {
    try {
        const url = `https://openexchangerates.org/api/historical/${date}.json?app_id=${process.env.OPEN_EXCHANGE_RATES_APP_ID}`;
        const response = await axios.get(url);
        return response.data.rates;
    } catch (error) {
        throw new Error(error.message);
    }
}

let currentDate = startDate;
const dates = [];
while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
    dates.push(currentDate.format('YYYY-MM-DD'));
    currentDate = currentDate.add(1, 'day');
}

const missingRatesDates = dates.filter(date => !Object.keys(exchangeRates).includes(date));
console.log('Missing rates:', missingRatesDates);

async function fetchAndUpdateRates() {
    try {
        for (const date of missingRatesDates) {
            const rates = await getHistoricalConvRate(date);
            exchangeRates[date] = rates;
            console.log('Rates fetched for:', date);
        }
        // Write updated exchangeRates object back to file
        await fs.writeFile('./year-exchange-rate.json', JSON.stringify(exchangeRates, null, 2));
        console.log('Missing rates have been fetched and added to year-exchange-rate.json');
    } catch (error) {
        console.error('Error:', error.message);
    }
}

fetchAndUpdateRates();

import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Collection, Db } from 'mongodb';
import {Pool, PoolClient} from 'pg';
import knex, { Knex } from 'knex';

dotenv.config();
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export class FlagPendingChargesForPaidInvoices extends ScriptBase {
    db: Db | undefined = undefined;

    public getName(): string {
        return 'Flag pending charges with paid invoices';
    }

    public getAuthor(): string {
        return '<PERSON>/<PERSON>';
    }

    public getTicket(): string {
        return 'CHA-9353';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async fetchPaidInvoices(page: number = 1, limit: number, client: PgService, knexClient: Knex): Promise<string[]> {
     
      const query = knexClient.queryBuilder()
      .select('i.productReference', 'p.status')
      .from('Invoice as i')
      .innerJoin('PaymentCollections as p', function() {
        this.on('i.id', '=', 'p.invoiceId')
          .andOn('p.status', '=', knexClient.raw('?', ['paid']));
      })
      .limit(limit)
      .offset((page - 1) * limit)
    
      const data = await client.query(query.toQuery())

      return data.map((row: any) => row.productReference);
    }

    async flagManyPendingCharges(caseIds: string[], collection: Collection): Promise<any> {
      if (!caseIds.length) throw new Error('Missing caseIds');
  
      return collection.updateMany(
        { caseId: { $in: caseIds } },
        { $set: { manuallyProcessedAsPromotionalCharge: true } }
      ).catch((err) => {
        if (err.code === 'ECONNREFUSED') throw new Error(`No internet connection: ${JSON.stringify(err)}`);
        throw err;
      });
    }

    private async logic(context: ScriptContext, isDryRun: boolean) {
      console.log('Processing pending charges to flag');
      let hasMore = true;
      let page = 1;
      let limit: number = Number(process.env.BATCH_SIZE) || 100;
      let totalFlaggedPendingCharges = 0;
      let totalInvoices = 0;

      console.log('Creating RDS client and connecting');
      const client = new PgService();
      const knexClient = knex({ client: "pg" });
      await client.connect();
      const mongoClient = await loadMongoClient();
      const pendingChargesMongoCollection = mongoClient.db("chargeflow_api").collection("pendingCharges");
      while (hasMore) {
        console.log('Fetching invoices collected');
        const invoicesProductRefs = await this.fetchPaidInvoices(page, limit, client, knexClient);
        if (!invoicesProductRefs.length) {
          console.log('No collected invoices returned. Skipping');
          break;
        }
        if (!isDryRun) {
          console.log(`Not dry run - deleting charges for ${invoicesProductRefs.length} invoices`);
          const flagPendingChargesRes = await this.flagManyPendingCharges(invoicesProductRefs, pendingChargesMongoCollection);
          console.log(`Flagged ${flagPendingChargesRes.modifiedCount} pending charges from ${invoicesProductRefs.length} invoices`);
          totalFlaggedPendingCharges += flagPendingChargesRes.modifiedCount;
        }
        page++;
        hasMore = invoicesProductRefs.length === limit;
        totalInvoices += invoicesProductRefs.length;
        await delay(2000);
      }
      console.log(`Total invoices processed: ${totalInvoices}`);
      if (!isDryRun) {
        console.log(`Total pending charges flagged: ${totalFlaggedPendingCharges}`);
      }
      console.log('Disconnecting RDS client');
      await client.disconnect();
      console.log('Disconnecting MongoDB client');
      await mongoClient.close();
    }
}

interface IDbService {
  query<T>(queryString: string, params: any[]): Promise<T[]>
}

class PgService implements IDbService {
  private pool: Pool;
  private client: PoolClient | null = null;

  constructor() {
    const connectionString = process.env.PG_READER_CONNECTION_STRING_DEV!
    if (!connectionString) {
      console.error('InternalServerError', 'No connection string provided for Postgres');
    }
    this.pool = new Pool({connectionString, ssl: {rejectUnauthorized: false}, max: 1});
  }

  public async connect() {
    try {
      this.client = await this.pool.connect();
    } catch (error) {
      console.error('InternalServerError', 'Error connecting to Postgres', {error});
      throw new Error('Error connecting to Postgres');
    }
  }

  public async disconnect() {
    if (this.client) {
      await this.client.release();
      this.client = null;
    }
  }

  public async query<T>(queryString: string, params: any[] = []): Promise<T[]> {
    try {
      const result = await this.client?.query(queryString, params);
      return result?.rows || [];
    } catch (error) {
      console.error('InternalServerError', 'Error running query', { error, queryString });
      throw new Error('No data returned from Postgres');
    }
  }
}

const script = new FlagPendingChargesForPaidInvoices();
script.main();

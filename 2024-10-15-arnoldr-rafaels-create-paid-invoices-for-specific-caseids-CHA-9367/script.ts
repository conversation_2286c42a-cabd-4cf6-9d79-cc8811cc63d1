import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Collection, Db } from 'mongodb';
import knex, { Knex } from 'knex';
import fs from 'fs/promises';
import path from 'path';
import { getAmountInUsd } from '../shared/currency-util';
import {Pool, PoolClient} from 'pg';

dotenv.config();

type DisputeData = {
  _id: string;
  chargeflowId: string;
  dispute: {
    id: string;
    amount: number;
    currency: string;
  };
  submittedAt: Date;
  shopSuccessRate: number;
  accountId: string;
  accountIdInShop: string;
};

type InvoiceData = {
  accountId: number;
  paymentMethodId: number;
  status: string;
  amount: number;
  productReference: string;
  currency: string;
  description: string;
  productType: string;
  issueDate: Date;
};

interface JsonData {
  caseId: string;
  finalAmount: string;
  issueDate: string;
  PaymentMethodType: string;
}

const CHUNK_SIZE = 50;
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export class DeleteChargesForPaidInvoices extends ScriptBase {
  db: Db | undefined = undefined;

  public getName(): string {
    return "Create paid invoices for specific cases";
  }

  public getAuthor(): string {
    return "Arnold Ramos/ Rafael Schuster";
  }

  public getTicket(): string {
    return "CHA-9367";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    await this.logic(context, true);
  }

  
  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async upsertInvoice(invoiceData: InvoiceData, client: PgService, knexClient: Knex): Promise<any> {
    try {
      console.log('Checking for existing invoices for caseId: %s', invoiceData.productReference);
      const existingInvoiceQuery = knexClient
        .queryBuilder()
        .select('*')
        .from("Invoice AS i")
        .where("i.productReference", invoiceData.productReference)
        
      const existingInvoices = await client.query(existingInvoiceQuery.toQuery());
  
      let query;
      if (existingInvoices.length > 0) {
        console.log('Invoice already exists, updating for caseId: %s', invoiceData.productReference);
        query = knexClient
          .queryBuilder()
          .update(invoiceData)
          .from("Invoice AS i")
          .where("i.productReference", invoiceData.productReference)
          .returning('id');
      } else {
        console.log('Invoice does not exist, inserting for caseId: %s', invoiceData.productReference);
        query = knexClient
          .queryBuilder()
          .insert(invoiceData)
          .into("Invoice")
          .returning('id');
      }
  
      const res = await client.query(query.toQuery());
      return res[0];
    } catch (error) {
      console.error('Error upserting invoice', error);
      throw new Error('Error upserting invoice');
    }
  }

  private async updatePaymentCollection(invoiceData: { id: number }, client: PgService, knexClient: Knex): Promise<any> {
    console.log('Checking for existing payment collection for invoiceId: %s', invoiceData.id);
    try{
      const existingPaymentCollectionQuery = knexClient
        .queryBuilder()
        .select('*')
        .from("PaymentCollections AS p")
        .where("p.invoiceId", invoiceData.id)
       
      const existingPaymentCollection = await client.query(existingPaymentCollectionQuery.toQuery());
  
      let query;
      if (existingPaymentCollection.length > 0) {
        console.log('Payment collection already exists, updating for invoiceId: %s', invoiceData.id);
        query = knexClient
          .queryBuilder()
          .update({
            status: 'paid',
            dateUpdated: new Date(),
          })
          .from("PaymentCollections AS p")
          .where("p.invoiceId", invoiceData.id)
          .returning('id');
          
      } else {
        console.log('Payment collection does not exist, inserting for invoiceId: %s', invoiceData.id);
        query = knexClient
          .queryBuilder()
          .insert({
            invoiceId: invoiceData.id,
            status: 'paid',
            dateUpdated: new Date(),
          })
          .into("PaymentCollections")
          .returning('id');
      }
  
      const res = await client.query(query.toQuery());
      return res[0];
    } catch (error) {
      console.error('Error upserting payment collection: %s', JSON.stringify(error));
      throw new Error('Error upserting payment collection');
    }
  }

  private async getCaseIdsFromJsonFile() {
    try {
      const filePath = path.join(__dirname, "providedList.json");
      const data = await fs.readFile(filePath, "utf-8");
      return JSON.parse(data);
    } catch (error) {
      console.error("Error reading case ids from file", error);
      console.error(
        "Make sure the file <providedList.json> exists and is in the same directory as the script"
      );
    }
  }
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    if (chunkSize <= 0) {
      throw new Error("Chunk size must be greater than 0");
    }

    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }

    return chunks;
  }

  private async fetchDisputesData(caseIds: string[], collection: Collection): Promise<DisputeData[]> {
    const disputes = await collection.aggregate([
      {
        $match: { "dispute.id": { $in: caseIds } }
      },
      {
        $lookup: {
          from: "shops",
          localField: "chargeflowId",
          foreignField: "chargeflow_id",
          as: "shopDetails"
        }
      },
      {
        $unwind: "$shopDetails"
      },
      {
        $project: {
          "dispute.amount": 1,
          "dispute.currency": 1,
          "dispute.id": 1,
          chargeflowId: 1,
          accountId: 1,
          "submittedAt": "$chargeflow.submitted.submittedAt",
          shopSuccessRate: "$shopDetails.chargeflow_success_rate",
          accountIdInShop: "$shopDetails.account_id",
        }
      }
    ]).toArray();

    const mappedDisputes = disputes.map((dispute) => ({
      ...dispute,
      chargeflowId: dispute.chargeflowId.toString(),
      accountId: dispute.accountId.toString(),
    }));

    return mappedDisputes as unknown as DisputeData[];
  }

  private toInvoiceData(dispute: DisputeData, accountId: number, paymentMethodId: number, finalAmount: number, description: string, issueDate: Date): InvoiceData {
    return {
      accountId,
      paymentMethodId,
      status: 'paid',
      amount: finalAmount,
      productReference: dispute.dispute.id,
      issueDate,
      currency: 'USD',
      description,
      productType: 'dispute'
    };
  }

  private buildDescription(dispute: DisputeData, calculatedProductAmount: number, issueDate: Date): string {
    function formatDate(issueDate: Date): string {
      const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: '2-digit' };
      return issueDate.toLocaleDateString('en-US', options).replace(',', '');
    }
    const submittedAt = formatDate(issueDate);

    return `An invoice for a recovered dispute, ID: ${dispute.dispute.id}, which was submitted by Chargeflow on ${submittedAt}, for ${calculatedProductAmount} USD and manually processed as collected.`;
  }

  private generateRandomAlphanumeric(length: number): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    const charactersLength = characters.length;

    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }

    return result;
  }

  private async upsertOrFindAccount(chargeflowId: string, client: PgService, knexClient: Knex, mongoAccountId: string): Promise<any> {
    const query = knexClient
      .queryBuilder()
      .select(['a.id'])
      .from("Account AS a")
      .where("a.mongoAccountId", mongoAccountId);

    const result = await client.query(query.toQuery());

    if (!result.length) {
      console.log('Creating new account for mongoAccountId: %s', mongoAccountId);
      const insertQuery = knexClient('Account').insert({
        chargeflowId,
        mongoAccountId,
      }).returning('id');

      const insertResult = await client.query(insertQuery.toQuery());
      return insertResult[0];
    }

    return result[0];
  }

  private async upsertOrFindPaymentMethodId(accountId: number, client: PgService, knexClient: Knex, paymentMethodType: string, mongoAccountId: string, chargeflowId: string): Promise<any> {
    const query = knexClient
      .queryBuilder()
      .select(['p.id'])
      .from("PaymentMethod AS p")
      .where("p.accountId", accountId);

    const result = await client.query(query.toQuery());

    if (!result.length) {
      console.log('Creating payment method for %s accountId', accountId);
      const insertQuery = knexClient('PaymentMethod').insert({
        accountId,
        type: paymentMethodType,
        settings: {},
        mongoAccountId,
        chargeflowId,
      }).returning('id');

      const insertResult = await client.query(insertQuery.toQuery());
      return insertResult[0];
    }

    return result[0];
  }

  async flagManyPendingCharges(caseIds: string[], collection: Collection): Promise<any> {
    if (!caseIds.length) throw new Error('Missing caseIds');

    return collection.updateMany(
      { caseId: { $in: caseIds } },
      { $set: { manuallyProcessedAsPromotionalCharge: true } }
    ).catch((err) => {
      if (err.code === 'ECONNREFUSED') throw new Error(`No internet connection: ${JSON.stringify(err)}`);
      throw err;
    });
  }

  private toCents(amount: number): number | undefined {
    if (amount === undefined || amount === null) {
      console.info('Amount is missing');
      return;
    }
    return Math.round(amount * 100);
  }

  private async logic(context: ScriptContext, isDryRun: boolean): Promise<void> {
    console.log('Retrieving disputes data from JSON file');
    const jsonData = await this.getCaseIdsFromJsonFile() as JsonData[];
    if (jsonData.length === 0) {
      console.error('No case ids found in file <providedList.json>');
      return;
    }
    console.log('Successfully retrieved %s cases from JSON file', jsonData.length);

    const chunkedCaseIds = this.chunkArray(jsonData, CHUNK_SIZE);
    console.log('Chunked cases to %s chunks of %s cases each', chunkedCaseIds.length, CHUNK_SIZE);

    console.log('Generating PG and Mongo clients');
    const client = new PgService();
    const knexClient = knex({ client: "pg" });
    await client.connect();
    const mongoClient = await loadMongoClient();
    const disputesMongoCollection = mongoClient.db("chargeflow_api").collection("disputes");
    const pendingChargesMongoCollection = mongoClient.db("chargeflow_api").collection("pendingCharges");
    console.log('Successfully generated DB clients');

    let totalCasesProcessed = 0;
    for (const chunk of chunkedCaseIds) {
      const caseIds = chunk.map((data) => data.caseId);
      const disputes = await this.fetchDisputesData(caseIds as string[], disputesMongoCollection as Collection);
      console.log('Fetched %s disputes data among %s cases to process', disputes.length, chunk.length);

      const invoiceDataPromises = disputes.map(async (dispute) => {
        console.log('Creating invoice data for caseId: %s', dispute.dispute.id);
        const caseIdData = chunk.find((data) => data.caseId === dispute.dispute.id);

        console.log('Handling account data for caseId: %s', dispute.dispute.id);
        const mongoAccountId: string = dispute.accountId || dispute.accountIdInShop || `manually-added-acccount-${this.generateRandomAlphanumeric(24)}`;
        const account = await this.upsertOrFindAccount(dispute.chargeflowId, client, knexClient, mongoAccountId) as any;
        console.log('Account processed for caseId: %s', dispute.dispute.id);

        console.log('Handling payment method data for caseId: %s', dispute.dispute.id);
        const paymentMethod = await this.upsertOrFindPaymentMethodId(
          account.id, client, knexClient, caseIdData?.PaymentMethodType!, mongoAccountId, dispute.chargeflowId) as any;
        console.log('Payment method processed for caseId: %s', dispute.dispute.id);

        console.log('Calculating product amount for caseId: %s', dispute.dispute.id);
        const calculatedProductAmount = await getAmountInUsd(dispute.dispute.amount, dispute.dispute.currency);
        const finalAmount = Number(caseIdData?.finalAmount);
        const issueDate = new Date(caseIdData?.issueDate!);

        console.log('Mapping invoice data for caseId: %s', dispute.dispute.id);
        const description = this.buildDescription(dispute, calculatedProductAmount, issueDate!);
        return this.toInvoiceData(dispute, account.id, paymentMethod.id, this.toCents(finalAmount!)!, description, issueDate!);
      });

      try {
        const invoiceData = await Promise.all(invoiceDataPromises);
        console.log('Successfully processed %s invoices data from %s case ids in chunk', invoiceData.length, caseIds.length);
        if (!isDryRun) {
          console.log('Not dry run - upserting bulk of invoices');
          for (const invoice of invoiceData) {
            const invoiceRes = await this.upsertInvoice(invoice, client, knexClient);
            const pcRes = await this.updatePaymentCollection(invoiceRes, client, knexClient);
            console.log('Upserted invoice id: %s', invoiceRes.id);
            console.log('Upserted payment collection id: %s', pcRes.id);
          }

          console.log('Not dry run - flagging pending charges as manually processed');
          const resFlagUpdate = await this.flagManyPendingCharges(caseIds, pendingChargesMongoCollection);
          console.log('Successfully flagged %s pending charges from %s invoices processed from chunk', resFlagUpdate.modifiedCount, invoiceData.length);
        }
      } catch (error) {
        console.error('Error occurred while processing invoices: %s', JSON.stringify(error));
      }

      totalCasesProcessed += chunk.length;
      await delay(2000);

    }

    console.log(`Successfully processed ${totalCasesProcessed} from ${jsonData.length} cases from input list`);
    console.log('Disconnecting from Mongo');
    await mongoClient.close();
    console.log('Disconnecting from RDS');
    await client.disconnect();
  }
}

interface IDbService {
  query<T>(queryString: string, params: any[]): Promise<T[]>
}

class PgService implements IDbService {
  private pool: Pool;
  private client: PoolClient | null = null;

  constructor() {
    const connectionString = process.env.PG_READER_CONNECTION_STRING_DEV!
    if (!connectionString) {
      console.error('InternalServerError', 'No connection string provided for Postgres');
    }
    this.pool = new Pool({connectionString, ssl: {rejectUnauthorized: false}, max: 1});
  }

  public async connect() {
    try {
      this.client = await this.pool.connect();
    } catch (error) {
      console.error('InternalServerError', 'Error connecting to Postgres', {error});
      throw new Error('Error connecting to Postgres');
    }
  }

  public async disconnect() {
    if (this.client) {
      await this.client.release();
      this.client = null;
    }
  }

  public async query<T>(queryString: string, params: any[] = []): Promise<T[]> {
    try {
      const result = await this.client?.query(queryString, params);
      return result?.rows || [];
    } catch (error) {
      console.error('InternalServerError', 'Error running query', { error, queryString });
      throw new Error('No data returned from Postgres');
    }
  }
}


const script = new DeleteChargesForPaidInvoices();
script.main();
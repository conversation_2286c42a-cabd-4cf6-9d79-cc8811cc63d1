# Purpose:
Creates paid invoices for specific cases

# Motivation:
This is to fix an issue from the new billing to address the failed charges

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary>`
NOTE: a `.env` file is expected to be present in CWD with 
**PG_READER_CONNECTION_STRING_DEV** , 
**MONGO_URI** ,  
**AWS_CONVERSION_APIG_SERVICE_URL** , 

- Supply the list of caseIds in the providedList.json

### How this script was tested
[x] Wet run against dev & test
 
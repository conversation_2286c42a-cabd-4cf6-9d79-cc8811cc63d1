# Purpose

Process dump items from collection `cha-14673-enriched-investigation` and set correct chargeflowPerformedAction, submittedAt, amountInUSD.

## Motivation

Problem:

We have uncovered a significant issue: over 7K disputes worth $450K were manually submitted and imported without being marked as chargeflowPerformedAction: true, and their submission date is missing. This issue is causing financial reporting errors and uncollected revenue. This needs urgent action to fix past gaps and prevent future losses.


## Running this script

`npx ts-node ./script.ts <dry | wet-primary | wet-secondary>`

### Modes

dry - does not update anything, writes the results to the console
wet-secondary - does not update the source table, writes results to another collection (disputes_test), adding documents as needed
wet-primary - UPDATES THE DISPUTES TABLE. Use with extreme caution!

### How this script was tested

[x] Run locally with prod database in dry-run mode

## Required env variables

MONGO_URI=<mongo_uri_string>
MONGO_DB_NAME=<mongo_db_name>

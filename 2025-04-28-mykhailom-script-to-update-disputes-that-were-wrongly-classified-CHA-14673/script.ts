import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { ObjectId, AnyBulkWriteOperation } from "mongodb";
import rawExchangeRates from "../amount-in-usd-script/year-exchange-rate.json";
const exchangeRates: Record<string, Record<string, number>> = rawExchangeRates;

dotenv.config();

export class ReClassifiedCorruptedDisputes extends ScriptBase {
  public getName(): string {
    return "This script re-classify corrupted disputes from collection cha-14673-enriched-investigation";
  }

  public getAuthor(): string {
    return "<PERSON><PERSON><PERSON>";
  }

  public getTicket(): string {
    return "cha-14673-more-than-7k-imported-disputes-is-not-classified-as-managed";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  async wetRunOnSecondary(): Promise<void> {
    throw new Error("Method not implemented.");
  }

  isEnvVarsOkOrThrow(keys: string[]) {
    const requiredEnvVars = keys;
    if (!requiredEnvVars.every((envVar) => process.env[envVar])) {
      throw new Error(
        `Missing required environment variables: ${requiredEnvVars.join(", ")}`
      );
    }
  }

  async logic(context: ScriptContext, isDryRun: boolean) {
    this.isEnvVarsOkOrThrow(["MONGO_URI", "MONGO_DB_NAME"]);

    const client = await loadMongoClient();

    const DUMP_DISPUTES_COLLECTION = client
      .db("chargeflow_api")
      .collection("cha-14673-enriched-investigation");
    const DISPUTES_COLLECTION = client
      .db("chargeflow_api")
      .collection("disputes");

    const cursor = DUMP_DISPUTES_COLLECTION.find({});

    try {
      let bulkOps: AnyBulkWriteOperation<any>[] = [];

      while (await cursor.hasNext()) {
        const dumpDispute = await cursor.next();
        if (!dumpDispute) continue;

        const dumpDisputeId = dumpDispute.disputes?.[0]?._id;
        const expectedSubmissionDate = new Date(
          dumpDispute?.["Expected Submission Date"]
        );

        const cfDipsute = dumpDispute.disputes?.[0];

        let disputeAmount: number | undefined;
        let disputeCurrency: string | undefined;
        let fallbackDisputeAmountInUSD: number | undefined;

        if (cfDipsute && cfDipsute.dispute) {
          disputeAmount = cfDipsute.dispute.amount;
          disputeCurrency = cfDipsute.dispute.currency;
          fallbackDisputeAmountInUSD = cfDipsute.dispute.amountInUsd;
        }

        const amountInUsd = this.getAmountInUSD(
          expectedSubmissionDate,
          dumpDispute.FallBackAmountUsd,
          disputeAmount,
          disputeCurrency,
          fallbackDisputeAmountInUSD
        );

        if (!dumpDisputeId || !expectedSubmissionDate || !amountInUsd) {
          console.log(
            `Skipping document due to missing data: ${dumpDispute._id}`
          );
          continue;
        }

        bulkOps.push({
          updateOne: {
            filter: { _id: new ObjectId(dumpDisputeId) },
            update: {
              $set: {
                "chargeflow.cfActions.chargeflowPerformedAction": true,
                "chargeflow.submitted.submittedAt": expectedSubmissionDate,
                "chargeflow.submitted.amountInUSD": amountInUsd,
              },
            },
          },
        });

        if (!isDryRun) {
          if (bulkOps.length >= 1000) {
            console.log("Updating disputes in bulk");
            const bulkWriteResult = await DISPUTES_COLLECTION.bulkWrite(
              bulkOps,
              { ordered: false }
            );
            console.log("Bulk write result:", bulkWriteResult);
            bulkOps = [];
          }
        } else {
          console.log(`Saving ${bulkOps.length} items in dryRun mode`);
          bulkOps = [];
        }
      }

      if (!isDryRun) {
        if (bulkOps.length > 0) {
          console.log("Updating disputes in bulk");
          const bulkWriteResult = await DISPUTES_COLLECTION.bulkWrite(bulkOps, {
            ordered: false,
          });
          console.log("Bulk write result:", bulkWriteResult);
        }
      } else {
        console.log(`Saving ${bulkOps.length} items in dryRun mode`);
      }
    } catch (error) {
      console.error("Error processing disputes:", error);
    } finally {
      await cursor.close();
    }
  }

  private getAmountInUSD(
    expectedSubmissionDate: Date,
    fallbackDumpDisputeAmountInUSD: number,
    disputeAmount?: number,
    disputeAmountCurrency?: string,
    fallbackDisputeAmountInUsd?: number
  ) {
    if (expectedSubmissionDate && disputeAmount && disputeAmountCurrency) {
      const exchangeDate = expectedSubmissionDate.toISOString().slice(0, 10);
      if (Object.keys(exchangeRates).includes(exchangeDate)) {
        return Number(this.computeUSDAmount(exchangeDate, disputeAmount, disputeAmountCurrency));
      }
    }

    if (fallbackDisputeAmountInUsd) {
      return fallbackDisputeAmountInUsd;
    }

    return fallbackDumpDisputeAmountInUSD;
  }

  private computeUSDAmount(exchangeDate: string, amount: number, currency: string) {
    const rate = exchangeRates[exchangeDate][currency];
    const amountInUsd = Number(Number(amount) / Number(rate)).toFixed(2);
    return amountInUsd;
  }
}

const script = new ReClassifiedCorruptedDisputes();
script.main();

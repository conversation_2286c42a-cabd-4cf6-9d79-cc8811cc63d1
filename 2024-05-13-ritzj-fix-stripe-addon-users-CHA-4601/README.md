# Purpose:
To update a stripe-registered shops' addon user to have the value for `company?.users?.billing?.stripeCustomer?.paymentMethodId` as well from the main user's (`companies` document).

# Motivation:
Addon users added to stripe-registered shops are getting the billing warning in webapp because the current script to add them do not include copying the `paymentMethodId` from the main user - which is what `webapp` is using to determine whether to show the billing warning modal or not.

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> <db shop name> <addon user email>`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**.

### How this script was tested
[x] Wet run against dev & test
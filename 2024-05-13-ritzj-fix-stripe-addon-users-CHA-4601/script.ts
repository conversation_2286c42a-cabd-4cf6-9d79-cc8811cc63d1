import dotenv from 'dotenv';
import { ObjectId } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';

dotenv.config();


export class <PERSON>ript extends ScriptBase {
    public getName(): string {
        return 'Add stripe payment method id to add user companies doc.';
    }

    public getAuthor(): string {
        return 'Ritz Jumola';
    }

    public getTicket(): string {
        return 'CHA-4601';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {
        console.log('ctx', ctx);

        const shopName = ctx.args[1];
        const addonUserEmail = ctx.args[2];

        if (!shopName) {
            throw new Error('Shop name is required');
        }

        if (!addonUserEmail) {
            throw new Error('Addon user email is required');
        }

        console.log('params', { shopName, addonUserEmail });

        const mongoClient = await loadMongoClient();
        const db = mongoClient.db('chargeflow_api');


        // collections
        const companies = db.collection('companies');
        const shops = db.collection('shops');

        // Find shop 
        const shop = await shops.findOne({ name: shopName });
        if (!shop) {
            throw new Error('Shop not found');
        }

        // Find main user
        const mainUser = {
            companiesDoc: await companies.findOne({
                'users.email': shop.email,
                'users.chargeflowId': shop.chargeflow_id,
            }),
        };
        if (!mainUser?.companiesDoc) {
            throw new Error('Main user not found');
        }

        // ensure main user has set up stripe payment method id
        if (!mainUser.companiesDoc.users?.billing?.stripeCustomer?.paymentMethodId) {
            throw new Error('Main user has no stripe payment method id');
        }

        // Find addon user
        const addonUser = {
            companiesDoc: await companies.findOne({
                'users.email': addonUserEmail
            }),
        };

        if (!addonUser.companiesDoc) {
            throw new Error('Addon user not found');
        }

        // ensure that the company has no stripe payment method id
        if (addonUser.companiesDoc.users?.billing?.stripeCustomer?.paymentMethodId) {
            throw new Error('Company already has stripe payment method id');
        }

        // ensur their from the same shop by users.chargeflowId
        if (String(mainUser.companiesDoc.users.chargeflowId) !== String(addonUser.companiesDoc.users.chargeflowId)) {
            throw new Error(
                `Main user and addon user are not from the same shop main user chargeflowId: ${mainUser.companiesDoc.users.chargeflowId} addon user chargeflowId: ${addonUser.companiesDoc.users.chargeflowId}`
            );
        }

        const handleDryRun = async () => {
            console.log('Running in dry run mode (not changing anything)');

            // log the main user and addon user payment method id
            console.log('main user payment method id', mainUser.companiesDoc?.users.billing.stripeCustomer.paymentMethodId);
            console.log('addon user payment method id', addonUser.companiesDoc?.users.billing.stripeCustomer.paymentMethodId);
        };

        const handleWetRun = async () => {
            console.log('Running in wet run mode (changing data)');

            // update the addOnUsr's paymentMethodId
            const res = await companies.updateOne(
                { _id: addonUser?.companiesDoc?._id },
                {
                    $set: {
                        'users.billing.stripeCustomer.paymentMethodId': mainUser?.companiesDoc?.users.billing.stripeCustomer.paymentMethodId,
                    },
                }
            );

            console.log('updated response', JSON.stringify(res));
        };

        try {
            if (isDryRun) {
                await handleDryRun();
            } else {
                await handleWetRun();
            }
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        } finally {
            mongoClient.close();
        }
    }
}

const script = new Script();
script.main();

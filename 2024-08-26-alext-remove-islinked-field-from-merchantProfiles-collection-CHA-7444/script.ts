import { MongoClient, WithId, Document } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext, ScriptMode } from "../shared/script-base";

export class RemoveIsLinkedFieldFromMerchantProfilesCollection extends ScriptBase {
    public getName(): string {
        return 'Remove isLinked field from merchantProfiles collection';
    }

    public getAuthor(): string {
        return 'Alex Tkach';
    }
    
    public getTicket(): string {
        return 'CHA-7444';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.logic(context);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context);
    }

    async wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method is not implemented.');
    }

    async logic(ctx: ScriptContext) {
        const mode = (ctx.args[0] ?? 'dry') as ScriptMode;
        try {
            const batchSize = process.env.BATCH_SIZE ? parseInt(process.env.BATCH_SIZE) : 100;
            const client: MongoClient = await loadMongoClient();

            let skip = 0;
            let totalDocsToUpdate = 0;
            let totalDocsUpdated = 0;
            let merchantProfilesDocs: WithId<Document>[] = [];
            
            do {
                merchantProfilesDocs = await this.getMerchantProfilesDocs(client, skip, batchSize);
                if (merchantProfilesDocs.length === 0) {
                    break;
                }
                totalDocsToUpdate += merchantProfilesDocs.length;

                if (mode !== 'dry') {
                    const updateMerchantProfilesOps = await this.createUpdateMerchantProfilesOps(merchantProfilesDocs);
                    const updatedMerchantProfilesCount = await this.updateMerchantProfiles(client, updateMerchantProfilesOps);

                    console.log('Updated %s merchantProfiles', updatedMerchantProfilesCount);
                    totalDocsUpdated += updatedMerchantProfilesCount;
                }
                skip += batchSize;
            } while (merchantProfilesDocs.length > 0);

            if (mode === 'dry') {
                console.log('Dry run mode');
                console.log('Total docs to update: %s', totalDocsToUpdate);
            } else {
                console.log('Total disputes updated: %s', totalDocsUpdated);
            }
        } catch (error) {
            console.error('Error: ', error);
        }
    }

    private async getMerchantProfilesDocs(client: MongoClient, skip: number, batchSize: number): Promise<WithId<Document>[]> {
        try {
            return client
            .db('chargeflow_api')
            .collection('merchantProfiles')
            .find({}, { 
                projection: {
                    _id: 1,
                } 
            })
            .skip(skip)
            .limit(batchSize)
            .toArray();
        } catch (error) {
            console.error('Error fetching merchantProfiles: ', error);
            throw error;
        }
    }

    private async createUpdateMerchantProfilesOps(merchantProfilesDocs: any[]): Promise<any[]> {
        return merchantProfilesDocs.map((item) => ({
            updateOne: {
                filter: { '_id': item._id },
                update: {
                    $unset: {
                        'isLinked': true,
                    }
                }
            }
        }));
    }

    private async updateMerchantProfiles(client: MongoClient, updateOps: any[] | []): Promise<number> {
        try {
            return client
                .db('chargeflow_api')
                .collection('merchantProfiles')
                .bulkWrite(updateOps)
                .then((result) => result.modifiedCount);
        } catch (error) {
            console.error('Error updating merchantProfiles: ', error);
            throw error;
        }
    }
}

const script = new RemoveIsLinkedFieldFromMerchantProfilesCollection();
script.main();
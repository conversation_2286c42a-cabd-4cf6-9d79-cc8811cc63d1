# Purpose
There is a need to display to users `Amount In USD` in exported alerts CSV file.
https://linear.app/chargeflow/issue/CHA-10450/convert-alerts-amounts-to-usd-and-add-this-as-a-new-columns-to-the

# Motivation
We need to filter out the alerts which don't have `amountInUSD` set in the `alerts` collection and send them to dedicated SQS which will convert the currency to USD and update alert itself.

# Running this script
`npx ts-node ./script.ts <dry | wet-primary>`

# Required env variables
MONGO_URI=<mongo_uri_string> (`alerts` collection is in use)
QUEUE_URL=<sqs queue url> (`ChargebackHitAlertProcessedQueue` required)

Queue Urls:
* Dev - https://us-east-1.console.aws.amazon.com/sqs/v3/home?region=us-east-1#/queues/https%3A%2F%2Fsqs.us-east-1.amazonaws.com%2F781257341267%2Fdev-chargeback-hit-ChargebackHitAlertProcessedQueue-VHfnw8SMotPl
* Prod - https://us-east-1.console.aws.amazon.com/sqs/v3/home?region=us-east-1#/queues/https%3A%2F%2Fsqs.us-east-1.amazonaws.com%2F781257341267%2Fprod-chargeback-hit-ChargebackHitAlertProcessedQueue-KcGw5gW4pGlR

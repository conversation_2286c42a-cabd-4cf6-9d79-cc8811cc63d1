import { Collection, Document, MongoClient } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { AwsSqsUtil, Logger } from "@chargeflow-team/chargeflow-utils-sdk";

export class AlertAmountUSDConversionScript extends ScriptBase {
  public getName() {
    return "Alert Amount in USD Conversion Script";
  }

  public getAuthor() {
    return "<PERSON><PERSON><PERSON><PERSON>";
  }

  public getTicket(): string {
    return "CHA-10706/write-a-script-to-update-all-historical-alerts";
  }

  public async dryRun(): Promise<void> {
    await this.handle(false);
  }

  public async wetRunOnPrimary(): Promise<void> {
    await this.handle(true);
  }

  public async wetRunOnSecondary(): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async handle(sendMessages = false) {
    if (!process.env.MONGO_URI) {
      throw new Error("MONGO_URI is not set");
    }

    if (!process.env.QUEUE_URL) {
      throw new Error("QUEUE_URL is not set");
    }

    console.log(`QUEUE_URL: ${process.env.QUEUE_URL}`);

    const client: MongoClient = await loadMongoClient();

    const alertsCollection = client.db("chargeflow_api").collection("alerts");

    const alertsWithoutAmountInUSDCount = await alertsCollection.countDocuments(
      { amountInUsd: { $exists: false } }
    );

    if (alertsWithoutAmountInUSDCount === 0) {
      console.log("No alerts without missing amount in USD found. Exiting...");
      return;
    }

    console.log(
      `Found ${alertsWithoutAmountInUSDCount} alerts without amount in USD field set.`
    );

    if (!sendMessages) {
      return;
    }

    const alertsWithoutAmountInUSDCursor = alertsCollection
      .find({ amountInUsd: { $exists: false } })
      .batchSize(100);

    let numberOfAlertsProcessed = 0;

    while (await alertsWithoutAmountInUSDCursor.hasNext()) {
      const alert = await alertsWithoutAmountInUSDCursor.next();

      if (!alert) {
        continue;
      }

      await this.sendAlertProcessedMessageToQueue(alert.alertId);

      numberOfAlertsProcessed++;
    }

    console.log(
      `All alerts without amount in USD field set have been processed. Total processed: ${numberOfAlertsProcessed}`
    );
  }

  private async sendAlertProcessedMessageToQueue(alertId: string) {
    const response = await AwsSqsUtil.send({
      MessageBody: JSON.stringify({ alertId }),
      QueueUrl: process.env.QUEUE_URL,
    });

    console.log(`Alert processed message sent to queue: ${response.MessageId}`);
  }
}

const script = new AlertAmountUSDConversionScript();
script.main();

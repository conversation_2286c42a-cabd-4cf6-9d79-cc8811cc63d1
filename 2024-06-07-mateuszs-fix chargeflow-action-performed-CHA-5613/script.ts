import dotenv from "dotenv";
import { MongoClient } from "mongodb";
import { ScriptBase } from "../shared/script-base";
import {AssignProcessorsToShops} from "./logic";

dotenv.config();

export class Script extends ScriptBase {
    public getName(): string {
        return 'Change chargeflowPerformedAction to true for submitted disputes which were not classified as chargeflowPerformedAction: true';
    }

    public getAuthor(): string {
        return 'mateuszs';
    }

    public getTicket(): string {
        return 'CHA-5613';
    }

    async dryRun(): Promise<void> {
        const dryRun = true;

        await this.execute(dryRun);
    }

    async wetRunOnPrimary(): Promise<void> {
        const dryRun = false;

        await this.execute(dryRun);
    }

    wetRunOnSecondary(): Promise<void> {
        throw new Error("Method not implemented.");
    }

    private async execute(dryRun: boolean): Promise<void> {
        const mongoClient = await MongoClient.connect(process.env.MONGO_URI!);
        const logger = {
            log: (message: string) => {
                console.log(message);
            },
        }

        const logic = new AssignProcessorsToShops(
            mongoClient,
            logger,
        );

        await logic.execute(dryRun);
    }

}

const script = new Script();
script.main();

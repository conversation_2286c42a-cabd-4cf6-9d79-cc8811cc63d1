import {MongoClient, Db, ObjectId, AnyBulkWriteOperation, Document, UpdateFilter, Filter} from 'mongodb';

export interface Logger {
    log: (message: string) => void;
}

export class AssignProcessorsToShops {
    private readonly DB_NAME = 'chargeflow_api';
    private readonly DISPUTES_COLLECTION_NAME = 'disputes';

    private db: Db

    constructor(
        mongoClient: MongoClient,
        private readonly logger: Logger,
    ) {
        this.db = mongoClient.db(this.DB_NAME);
    }

    async execute(dryRun: boolean): Promise<void> {
        try {
            this.logger.log('Setting chargeflowPerformedAction for disputes started, please wait ...');
            const aggregationFilter =
                [
                    {
                        $match: {
                            "chargeflow.cfActions.chargeflowPerformedAction": false,
                            "chargeflow.submitted.submittedAt": { $exists: true, $ne: null }
                        }
                    },
                    {
                        $lookup: {
                            from: "actions",
                            localField: "_id",
                            foreignField: "disputeId",
                            as: "actions"
                        }
                    },
                    {
                        $addFields: {
                            hasManualAction: {
                                $anyElementTrue: {
                                    $map: {
                                        input: "$actions",
                                        as: "action",
                                        in: { $eq: ["$$action.actionType", "manual_action"] }
                                    }
                                }
                            },
                            hasHandeByChargeflowOffAction: {
                                $anyElementTrue: {
                                    $map: {
                                        input: "$actions",
                                        as: "action",
                                        in: { $eq: ["$$action.action.actionType", "handle_by_chargeflow_off"] }
                                    }
                                }
                            },
                            hasEvidenceOrMessage: {
                                $anyElementTrue: {
                                    $map: {
                                        input: "$actions",
                                        as: "action",
                                        in: {
                                            $or: [
                                                { $eq: ["$$action.actionType", "evidence_submitted"] },
                                                { $eq: ["$$action.actionType", "message_sent"] }
                                            ]
                                        }
                                    }
                                }
                            }
                        }
                    },
                    {
                        $match: {
                            hasManualAction: false,
                            hasHandeByChargeflowOffAction: false,
                            hasEvidenceOrMessage: true
                        }
                    },
                    {
                        $project: {
                            _id: 1,
                        }
                    }
                ] ;
            const totalItems = await this.db
                .collection(this.DISPUTES_COLLECTION_NAME)
                .aggregate(aggregationFilter)
                .toArray()
                .then(disputes => disputes.map(dispute => dispute._id));
            this.logger.log(`Total documents to process: ${totalItems.length}`);
            this.logger.log(`Total documents to process: ${JSON.stringify(totalItems)}`);
            if (dryRun) {
                this.logger.log('Dry run mode enabled, no changes made to the database');
                return;
            }
            const updateDisputeFieldOps = await this.createUpdateDisputeFieldOps(totalItems);
            const result = await this.db
                .collection(this.DISPUTES_COLLECTION_NAME)
                .bulkWrite(updateDisputeFieldOps);
            this.logger.log(`Updated ${result.modifiedCount} disputes.`);
            this.logger.log('Setting chargeflowPerformedAction for disputes finished successfully');
        } catch (error) {
            if (error instanceof Error) {
                this.logger.log(`Setting chargeflowPerformedAction for disputes failed, reason: ${error.message}`);
            }
            throw error;
        }
    }

    private async createUpdateDisputeFieldOps(disputeIds: string[]): Promise<AnyBulkWriteOperation[]> {
        const updateDisputeFieldOps: AnyBulkWriteOperation[] = [];
        for (const id of disputeIds) {
            updateDisputeFieldOps.push({
                updateOne: {
                    filter: { '_id': new ObjectId(id)},
                    update: {
                        $set: {
                            'chargeflow.cfActions.chargeflowPerformedAction': true,
                        },
                    }
                }
            });
        }
        return updateDisputeFieldOps;
    }
}

import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Db } from 'mongodb';

dotenv.config();

export class ScriptMigrateUserOnboarding extends ScriptBase {
    db: Db | undefined = undefined;

    public getName(): string {
        return 'Migrate unfinished onboarding';
    }

    public getAuthor(): string {
        return '<PERSON><PERSON> Jumola';
    }

    public getTicket(): string {
        return 'CHA-7259';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(context: ScriptContext, isDryRun: boolean) {
        const client = await loadMongoClient();
        const db = client.db('chargeflow_api');
        const collection = db.collection('companies');
        const filter = {
            'users.status': 'registered',
            'users.onboardingStageV1': {
                $in: ['Step 2', 'Step 3', 'Integrations', 'Schedule Call']
            },
        };
        const companies = await collection.find(
            filter,
            { projection: { _id: 1, 'users.email': 1, 'users.onboardingStageV1': 1 }},
        ).toArray();
        console.log('Found', companies.length, 'companies');
        console.log(companies);
        if (!isDryRun) {
            const response = await collection.updateMany(
                filter,
                {
                    $set: {
                        'users.onboardingStageV1': 'Step 1',
                        'users.dateUpdated': new Date()
                    },
                }
            );
            console.log(response);
        }
    }
}

const script = new ScriptMigrateUserOnboarding();
script.main();

# Purpose
This script converts the `processorId` field from string to MongoDB ObjectId in the `processor-meta-data` collection in the `chargeflow_api` database.

## Motivation
The `processorId` field in the `processor-meta-data` collection is currently stored as a string, but it should be stored as a MongoDB ObjectId for consistency with other collections and to enable proper referencing and indexing.

## Running this script
`npx ts-node ./convert-processorId-to-objectId.ts <dry or wet-secondary or wet-primary> | tee log.txt`

NOTE: a `.env` file is expected to be present in CWD with the `MONGO_URI` environment variable set.

When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
- **dry** - does not update anything, writes the results to the console and shows how many documents would be affected
- **wet-secondary** - does not update the source table, writes results to another collection (`processor-meta-data-test`), adding documents as needed
- **wet-primary** - **UPDATES THE PROCESSOR-META-DATA TABLE**. Use with extreme caution!

### How this script works
1. Finds all documents in `processor-meta-data` collection where `processorId` is a string
2. Processes documents in batches of 1000 for optimal performance
3. Validates that each string can be converted to a valid ObjectId
4. Uses bulk operations to efficiently update multiple documents
5. Converts the string to ObjectId and updates the document
6. Handles any documents with invalid ObjectId strings by logging them as errors

### Performance considerations
- Processes documents in batches of 1000 to avoid memory issues
- Uses MongoDB bulk operations for efficient updates
- Provides progress tracking with batch-by-batch reporting
- Optimized for large collections

### Safety considerations
- The script validates each `processorId` string before conversion
- Invalid ObjectId strings are logged but not processed
- Uses MongoDB transactions for data integrity
- Dry run mode allows testing without making changes
- Batch processing prevents memory overflow on large datasets

### Example output
```
Running in dry run mode (not changing anything)
Found 2500 documents with string processorId

Processing batch 1/3 (1000 documents)
✓ Document 507f1f77bcf86cd799439011: processorId "507f1f77bcf86cd799439012" is valid ObjectId
...

--- Summary ---
Total documents found: 2500
Documents processed: 2500
Valid ObjectId strings: 2450
Invalid ObjectId strings: 50
Documents that would be updated: 2450
```

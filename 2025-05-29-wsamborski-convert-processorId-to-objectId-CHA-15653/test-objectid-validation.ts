import { ObjectId } from "mongodb";

/**
 * Test script to validate ObjectId conversion logic
 * Run with: npx ts-node test-objectid-validation.ts
 */

function isValidObjectId(id: string): boolean {
  try {
    return ObjectId.isValid(id) && new ObjectId(id).toString() === id;
  } catch {
    return false;
  }
}

// Test cases
const testCases = [
  // Valid ObjectId strings
  "507f1f77bcf86cd799439011",
  "507f191e810c19729de860ea",
  "6123456789abcdef12345678",
  
  // Invalid ObjectId strings
  "invalid-id",
  "507f1f77bcf86cd79943901", // too short
  "507f1f77bcf86cd799439011x", // too long
  "507f1f77bcf86cd799439g11", // invalid character 'g'
  "",
  "null",
  "undefined",
  "123",
  "507F1F77BCF86CD799439011", // uppercase (should be invalid due to strict comparison)
];

console.log("Testing ObjectId validation logic:\n");

testCases.forEach((testCase, index) => {
  const isValid = isValidObjectId(testCase);
  const status = isValid ? "✓ VALID" : "✗ INVALID";
  console.log(`${index + 1}. "${testCase}" -> ${status}`);
  
  if (isValid) {
    const objectId = new ObjectId(testCase);
    console.log(`   Converted to: ObjectId("${objectId.toString()}")`);
  }
  console.log();
});

console.log("Test completed. Verify that the validation logic works as expected.");

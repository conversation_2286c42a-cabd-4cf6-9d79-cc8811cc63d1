import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { AnyBulkWriteOperation, Db, MongoClient, ObjectId } from 'mongodb';

dotenv.config();

export enum NotificationPreferenceSettingsFields {
    WonDispute = 'wonDispute',
    SubmittedEvidence = 'evidenceSubmission',
    DisputeEvidenceUploadRequired = 'evidenceRequired',
    DisputeWeeklySummary = 'disputesWeeklySummary',
    AlertReceived = 'alertReceived',
    AlertPrevented = 'alertPrevented',
    AlertWeeklySummary = 'alertsWeeklySummary',
}

const defaultSettings = {
    [NotificationPreferenceSettingsFields.WonDispute]: true,
    [NotificationPreferenceSettingsFields.SubmittedEvidence]: true,
    [NotificationPreferenceSettingsFields.DisputeEvidenceUploadRequired]: true,
    [NotificationPreferenceSettingsFields.DisputeWeeklySummary]: true,
    [NotificationPreferenceSettingsFields.AlertReceived]: true,
    [NotificationPreferenceSettingsFields.AlertPrevented]: true,
    [NotificationPreferenceSettingsFields.AlertWeeklySummary]: true,
};

interface NotificationSettings {
    notifications: Record<string, any>;
    accountId: string,
    accountMappings: [
        {
            email: string,
        }
    ],
    shop: {
        email: string,
        accountId: ObjectId,
    },
    businessUnit: {
        _id: ObjectId,
    },
    chargeflow_id: ObjectId,
}

export class ScriptDeleteUser extends ScriptBase {
    db: Db | undefined = undefined;

    public getName(): string {
        return 'Migrate notification preferences';
    }

    public getAuthor(): string {
        return 'Riza Jumola';
    }

    public getTicket(): string {
        return 'CHA-7963';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async getCurrentNotificationSettings(mongoClient: MongoClient, page: number, pageSize: number): Promise<Record<string, any>[]> {
        const db = mongoClient.db('chargeflow_api');
        const collection = db.collection('settings');
        return collection
            .aggregate(
                [
                    {
                      $lookup: {
                        from: "accountMappings",
                        localField: "chargeflow_id",
                        foreignField: "chargeflowId",
                        as: "accountMappings",
                      },
                    },
                    {
                      $lookup: {
                        from: "shops",
                        localField: "chargeflow_id",
                        foreignField: "chargeflow_id",
                        as: "shops",
                      },
                    },
                    {
                      $addFields: {
                        shop: {
                          $first: "$shops",
                        },
                      },
                    },
                    {
                      $lookup: {
                        from: "businessUnits",
                        let: {
                          chargeflowId: "$chargeflow_id",
                          accountId: "$shop.accountId",
                        },
                        pipeline: [
                          {
                            $match: {
                              $expr: {
                                $and: [
                                  {
                                    $eq: [
                                      "$chargeflowId",
                                      "$$chargeflowId",
                                    ],
                                  },
                                  {
                                    $eq: [
                                      "$accountId",
                                      "$$accountId",
                                    ],
                                  },
                                ],
                              },
                            },
                          },
                        ],
                        as: "businessUnits",
                      },
                    },
                    {
                      $project: {
                        'shop': 1,
                        businessUnit: {
                          $first: "$businessUnits",
                        },
                        'accountMappings.email': 1,
                        notifications: 1,
                        chargeflow_id: 1,
                      },
                    },
                ]
            )
            .skip((pageSize - 1) * page)
            .limit(pageSize)
            .toArray();
    }

    private getUpsertOperations(notificationSettings: NotificationSettings): AnyBulkWriteOperation[] {
        const ops: AnyBulkWriteOperation[] = [];
        if (!notificationSettings.businessUnit?._id) {
            console.log('No businessUnit id for', notificationSettings['chargeflow_id']);
            return [];
        }
        const command = {
            updateOne: {
                filter: {
                    businessUnitId: notificationSettings.businessUnit._id,
                    accountId: notificationSettings.shop.accountId,
                    email: '',
                },
                update: {
                    $set: {
                        dateUpdated: new Date(),
                        settings: {
                            ...defaultSettings,
                            [NotificationPreferenceSettingsFields.WonDispute]:
                                notificationSettings.notifications['won_disputes'] ??
                                    defaultSettings[NotificationPreferenceSettingsFields.WonDispute],
                            [NotificationPreferenceSettingsFields.SubmittedEvidence]:
                                notificationSettings.notifications['evidence_collection'] ??
                                    defaultSettings[NotificationPreferenceSettingsFields.SubmittedEvidence],
                            [NotificationPreferenceSettingsFields.AlertWeeklySummary]:
                                notificationSettings.notifications['weekly_summary'] ??
                                    defaultSettings[NotificationPreferenceSettingsFields.AlertWeeklySummary],
                            [NotificationPreferenceSettingsFields.DisputeWeeklySummary]:
                                notificationSettings.notifications['weekly_summary'] ??
                                    defaultSettings[NotificationPreferenceSettingsFields.DisputeWeeklySummary],
                        }
                    },
                    $setOnInsert: {
                        dateCreated: new Date(),
                    }
                },
                upsert : true
            }
        };
        if (notificationSettings.accountMappings.length) {
            for (let i=0; i<notificationSettings.accountMappings.length; i++) {
                command.updateOne.filter.email = notificationSettings.accountMappings[i].email;
                ops.push({...command});
            }
        } else if(notificationSettings.shop?.email) {
            command.updateOne.filter.email = notificationSettings.shop.email;
            ops.push({ ...command });
        } else {
            console.log('No email found for', notificationSettings.chargeflow_id);
        }
        return ops;
    }

    private async upsertNotificationPreferences(mongoClient: MongoClient, settings: NotificationSettings[]): Promise<void> {
        const collection = mongoClient.db('chargeflow_api').collection('notificationPreferences');
        const ops = settings.map(
            i => this.getUpsertOperations(i as NotificationSettings)
        ).flat();
        if (ops.length) {
            const res = await collection.bulkWrite(ops);
            console.log(res);
        }
    }

    private async deleteMissingAccountId(mongoClient: MongoClient): Promise<void> {
        console.log('Deleting null accountId');
        const collection = mongoClient.db('chargeflow_api').collection('notificationPreferences');
        const res = await collection.deleteMany({ accountId: null });
        console.log(res);
    }


    private async logic(context: ScriptContext, isDryRun: boolean) {
        const client = await loadMongoClient();
        let settings: Record<string, any>[];
        let page = 1;
        const pageSize = 1000;
        if (!isDryRun) {
            await this.deleteMissingAccountId(client);
        }
        do {
            settings = await this.getCurrentNotificationSettings(client, page++, pageSize);
            console.log('Read', settings.length);
            if (!isDryRun && settings.length) {
                await this.upsertNotificationPreferences(
                    client,
                    settings as NotificationSettings[]
                );
            }
        } while (settings.length);
    }
}

const script = new ScriptDeleteUser();
script.main();

import dotenv from 'dotenv';
import { loadMongoClientWithAuthParameters} from '../shared/mongo-config';
import { WithId, Collection, Filter, MongoClient } from 'mongodb';
import { Document, ObjectId } from 'bson';
import { ScriptBase, ScriptContext } from '../shared/script-base';

dotenv.config();

interface Dispute extends WithId<Document>{
    _id: ObjectId;
    dispute: {
        id: string;
    };
}

const dbName = 'chargeflow_api';
const disputesCollection = 'disputes';

const updateDispute = async (disputes: Collection<Document>, dispute: Dispute) => {
    console.log(`dispute: ${JSON.stringify(dispute)}`)
    const fixedDisputeId = dispute.dispute.id.replace(/^\*/, '');
    const result = await disputes.updateOne({ _id: dispute._id }, { $set: { "dispute.id": fixedDisputeId } });
    if (!result.acknowledged) {
        console.error(`Failed to update dispute with _id: ${dispute._id}`);
    }
    console.log(`Updated dispute with _id: ${dispute._id} with new dispute.id: ${fixedDisputeId}`);
};

const getAsteriskDisputes = async (disputes: Collection<Document>): Promise<Dispute[]> => {
    try {
        const asteriskquery: Filter<Document> = { "dispute.id": { $regex: /^\*/ } }
        const projectionObject = { "dispute.id": 1 };
        const result = await disputes.find(asteriskquery, { projection: projectionObject }).limit(100).toArray();
        return result as Dispute[];
    } catch (error) {
        console.error(error);
        return []
    }
};

export class ScriptRemoveAsteriskFromDisputeID extends ScriptBase {
    public getName(): string {
        return 'Remove asterisk from disputeId';
    }

    public getAuthor(): string {
        return 'Asaf Gamliel';
    }

    public getTicket(): string {
        return 'CHA-7849';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {

        const handleDryRun = async (disputes: Collection<Document>) => {       
            const asteriskDisputes = await getAsteriskDisputes(disputes);
            console.log(asteriskDisputes);
        };

        const handleWetRun = async (disputes: Collection<Document>) => {
            const asteriskDisputes = await getAsteriskDisputes(disputes);
            console.log(asteriskDisputes);
            console.log(`Found ${asteriskDisputes.length} disputes with asterisk in dispute.id`);
            const updateOprations = asteriskDisputes.map(async (dispute) => {
                await updateDispute(disputes, dispute);
            });
            await Promise.all(updateOprations);
        };

        try {
            const mongoClient: MongoClient = await loadMongoClientWithAuthParameters();
            const disputes = mongoClient.db(dbName).collection(disputesCollection);
            if (isDryRun) {
                await handleDryRun(disputes);
            } else {
                await handleWetRun(disputes);
            }
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        }
    }
}

const script = new ScriptRemoveAsteriskFromDisputeID();
script.main();

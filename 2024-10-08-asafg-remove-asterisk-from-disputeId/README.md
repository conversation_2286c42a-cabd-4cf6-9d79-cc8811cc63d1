# Purpose:
Fix incorrect dispute IDs in the production DB - remove asterisk

# Motivation:
Due to the long number format, Excel (which the analysts use) automatically converted the IDs into scientific notation (240702212529088903373 becomes 2.40702E+20). As a workaround, they added an asterisk to the case number so it would be treated as a string, but they didn't realize that the asterisk would be included in the case number.

# Running this script
`npx ts-node ./script.ts <dry or wet-primary>`

NOTE: a `.env` file is expected to be present in CWD with **PROD_MONGO_URI**, **AWS_ACCESS_KEY_ID**, **AWS_SECRET_ACCESS_KEY** and **AWS_SESSION_TOKEN**. 

Examples:
`~ npx ts-node script.ts dry`
```
Running in dry run mode (not changing anything)
Connecting to mongodb+srv://clusterprod.jpwxq.mongodb.net/
(node:77306) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
Connected to mongodb+srv://clusterprod.jpwxq.mongodb.net/
[
  {
    _id: new ObjectId("66a1fff1dd658b56581ce6c6"),
    dispute: { id: '*240508005623784903368' }
  },
  {
    _id: new ObjectId("66a1fff1dd658b56581ce6c3"),
    dispute: { id: '*240516163815231903314' }
  },
  .....
{
    _id: new ObjectId("66a1fff1dd658b56581ce6c1"),
    dispute: { id: '*240714022537811903383' }
  }
]
``` 

`~ npx ts-node script.ts wet-primary`
(Test example - change dispute.id from `1` to `2`)
```
Connecting to mongodb+srv://server-dev:<EMAIL>/
(node:78339) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
Connected to mongodb+srv://server-dev:<EMAIL>/
[
  {
    _id: new ObjectId("65e8245b1955637cc459387d"),
    dispute: { id: '1' }
  }
]
Found 1 disputes with asterisk in dispute.id
dispute: {"_id":"65e8245b1955637cc459387d","dispute":{"id":"1"}}
Updated dispute with _id: 65e8245b1955637cc459387d with new dispute.id: 2
```

### How this script was tested
[x] Using test values on dev DB

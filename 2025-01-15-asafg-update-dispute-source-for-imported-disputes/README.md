# Purpose:
Update `dispute.source` to be `import` for all of the imported disputes

# Motivation:
The data in the "data-integrity-report" is after filtering by `dispute.source: import`
In the new disputes, our code automatically assigns the value import into `dispute.source`. In the old imported disputes, the source is the PSP, so they don't appear in the report.

# Running this script
`npx ts-node ./script.ts <dry or wet-primary>`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**

Examples:
```
npx ts-node ./script.ts dry
Running in dry mode
        Execution ID: 05db6df8-32d2-47c9-9b4e-da5dc9c3b5e6
        Name: Update dispute.source to be import for all of the imported disputes
        Author: Asaf Gamliel
        Ticket: CHA-11265
        Run date: 2025-01-15T07:55:49.944Z
        Args: [ 'dry' ]
----------------------------------------
Running in dry run mode (not changing anything)
Dry run mode. Found 1129 documents to update.
        End date: 2025-01-15T08:00:34.996Z
```


### How this script was tested
Ran with dry mode.
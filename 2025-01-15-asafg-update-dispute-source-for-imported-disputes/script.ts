import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';

import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Collection, Filter, MongoClient, Document, UpdateFilter, WithId } from 'mongodb';

dotenv.config();

const defaultDbName = 'chargeflow_api';


async function *getDisputesGenertaor(collection: Collection, filter: Filter<Document>): AsyncGenerator<WithId<Document>[]> {
    let hasMore = true;
    let offset = 0;
    const limit = 100;
    while (hasMore) {
        console.log(`Fetching disputes from offset ${offset} with limit ${limit}`);
        const disputes = await collection.find(filter).skip(offset).limit(limit).toArray();
        if (disputes.length === 0) {
            hasMore = false;
            continue;
        }
        yield disputes as WithId<Document>[];
        offset += limit;
    }
}

export class UpdateImportedDisputesSource extends ScriptBase {
    public getName(): string {
        return 'Update dispute.source to be import for all of the imported disputes';
    }

    public getAuthor(): string {
        return 'Asaf Gamliel';
    }

    public getTicket(): string {
        return 'CHA-11265';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {

        const handleDryRun = async (disputes: Document[]) => {
            console.log(`Dry run mode. Found ${disputes.length} documents to update.`);
            if (disputes.length < 10) {
                console.log('Documents to update: ', JSON.stringify(disputes, null, 2));
            }
        };

        const handleWetRun = async (collection: Collection, disputes: Document[]) => {
            const updateStatements = disputes.map((dispute: Document) => {
                const filter: Filter<Document> = { _id: dispute._id };
                const update: UpdateFilter<Document> = { $set: { "dispute.source": "import" } };
                return { updateOne: { filter, update } };
            });
            const result = await collection.bulkWrite(updateStatements);
            console.log(`Updated ${result.modifiedCount} documents.`);
        };

        try {
            const sourceCollection = 'disputes';
            const mongoClient: MongoClient = await loadMongoClient();
            const collection = mongoClient.db(defaultDbName).collection(sourceCollection);
            const filter: Filter<Document> = { $or: [{ "chargeflow.imported": true }, { "chargeflow.importTaskId": { $ne: null } }], "dispute.source": { $nin: ["import", "asana"] } };
            for await (const disputes of getDisputesGenertaor(collection, filter)) {
                if (isDryRun) {
                    await handleDryRun(disputes);
                } else {
                    await handleWetRun(collection, disputes);
                }
            }
            
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        }
    }
}

const script = new UpdateImportedDisputesSource();
script.main();

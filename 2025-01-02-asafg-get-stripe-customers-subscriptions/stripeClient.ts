import { Stripe } from "stripe";

let stripeClient: Stripe | undefined = undefined;

const getStripeClient = (): Stripe => {
    if (!process.env.STRIPE_SECRET_KEY) {
        throw new Error("STRIPE_SECRET_KEY is not set");
    }
    const stripeSecret = process.env.STRIPE_SECRET_KEY;
    if (!stripeClient) {
        stripeClient = new Stripe(stripeSecret!, { telemetry: false });
    }
    return stripeClient;
}

export const getCustomerSubscriptionsLength = async (stripeUserId: string): Promise<number> => {
    const subscriptionsMaxLimit = 100;
    let subscriptionsLength = 0;
    try {
        const stripe = getStripeClient();
        const subscriptions = await stripe.subscriptions.list({ limit: subscriptionsMaxLimit }, { stripeAccount: stripeUserId });
        subscriptionsLength = subscriptions.data.length;
        console.log(`Found ${subscriptionsLength} subscriptions for stripe user ${stripeUserId}`);
    }
    catch (error) {
        console.log(`Error getting subscriptions for stripe user ${stripeUserId}: ${error}`);
        console.error(`Error getting subscriptions for stripe user ${stripeUserId}: ${error}`);
    }
    return subscriptionsLength;
}
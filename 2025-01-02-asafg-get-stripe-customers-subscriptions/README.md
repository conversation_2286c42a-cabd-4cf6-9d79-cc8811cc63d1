# Purpose:
Identify and flag merchants who manage subscriptions through Stripe


# Motivation:
Check how namy of our customers are managing their subscriptions through Stripe

# Running this script
`npx ts-node ./script.ts <dry or wet-primary>`

NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI** and **STRIPE_SECRET_KEY** (sk for chargeflow)

Examples:
`npx ts-node ./script.ts wet-primary`

### How this script was tested
Tested by running in wet-primary mode on prod, and dry mode on dev

import dotenv from 'dotenv';
import { getCustomerSubscriptionsLength } from './stripeClient'
import { loadMongoClient } from '../shared/mongo-config';
import { Collection, Filter, MongoClient } from 'mongodb';
import { Document, ObjectId } from 'bson';
import { ScriptBase, ScriptContext } from '../shared/script-base';

dotenv.config();

const DEFAULT_DB_NAME = 'chargeflow_api';
const BATCH_SIZE = 100;

export class GetStripeCustomersSubscriptions extends ScriptBase {
    public getName(): string {
        return 'Get subscriptions for stripe customers';
    }

    public getAuthor(): string {
        return 'Asaf Gamliel';
    }

    public getTicket(): string {
        return 'CHA-11318';
    }

    async getStripeProcessors(collection: Collection<Document>): Promise<Document[]> {
        const query = { processor_name: "stripe", isKeyValid: { $ne: false } };
        const projection = { stripeUserId: "$connect.stripe_user_id" };
        const processors = await collection.find(query, { projection: projection }).toArray();
        console.log(`Found ${processors.length} stripe processors`);
        return processors;
    }

    batchGenerator = function* (objectsArray: any[], batchSize: number): Generator<any[], void, unknown> {
        for (let i = 0; i < objectsArray.length; i += batchSize) {
            console.log(`current index: ${i}`);
            yield objectsArray.slice(i, i + batchSize);
        }
    };

    async checkSubscriptionsPerAccount(processors: Document[]): Promise<{ [id: string]: boolean }> {
        const promises: Promise<[string, number]>[] = processors.map(async (processor) => {
            const subscriptionsLength = await getCustomerSubscriptionsLength(processor.stripeUserId);
            return [processor._id, subscriptionsLength] as [string, number];
        });
        const results = await Promise.all(promises);
        const mergedResults = Object.fromEntries(results);
        console.log(mergedResults);
        const hasSubscriptionsData = Object.entries(mergedResults).map(([processorId, subscriptionsLength]) => {
            const hasSubscription = subscriptionsLength > 0;
            return [processorId, hasSubscription];
        });
        return Object.fromEntries(hasSubscriptionsData);
    }

    async updateProcessor(collection: Collection<Document>, processorId: string, hasSubscription: boolean) {
        const query: Filter<Document> = { _id: new ObjectId(processorId) };
        const update = { $set: { hasSubscription: hasSubscription } };
        return await collection.updateOne(query, update);
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {

        const handleDryRun = async (finalResults: { [id: string]: boolean }) => {
                const hasSubscriptions = Object.values(finalResults).filter(value => value).length;
                console.log(`Found ${hasSubscriptions} stripe customers with subscriptions`);
            }

        const handleWetRun = async (sourceCollection: Collection<Document>, finalResults: { [id: string]: boolean }) => {
                const updateProcessorPromises = Object.entries(finalResults).map(([processorId, hasSubscription]) => {
                    return this.updateProcessor(sourceCollection, processorId, hasSubscription)
                });
                const updateResults = await Promise.all(updateProcessorPromises);
                console.log(`Updated ${updateResults.length} processors`);
                console.log(JSON.stringify(updateResults));
            }

        try {
            const processorsCollection = 'processors';
            const mongoClient: MongoClient = await loadMongoClient();
            const collection = mongoClient.db(DEFAULT_DB_NAME).collection(processorsCollection);
            const processors = await this.getStripeProcessors(collection);
            let batchCounter = 0;
            for (const batch of this.batchGenerator(processors, BATCH_SIZE)) {
                console.log(`Batch ${batchCounter}`);
                const finalResults = await this.checkSubscriptionsPerAccount(batch);
                console.log(finalResults);
                if (isDryRun) {
                    await handleDryRun(finalResults);
                } else {
                    await handleWetRun(collection, finalResults);
                }
                batchCounter += 1;
            }
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        }
    }

}

const script = new GetStripeCustomersSubscriptions();
script.main();

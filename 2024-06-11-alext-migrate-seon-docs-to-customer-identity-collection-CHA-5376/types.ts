export type AccountDetails = Record<string, Record<string, any>>;

export type SeonRawData = {
    id?: string;
    state?: string;
    fraud_score?: number;
    version?: string;
    ip_details?: {
        ip?: string;
        score?: number;
        country?: string;
        state_prov?: string;
        city?: string;
        isp_name?: string;
        type?: string;
        latitude?: number;
        longitude?: number;
        tor?: boolean;
        harmful?: boolean;
        vpn?: boolean;
        web_proxy?: boolean;
        public_proxy?: boolean;
        spam_number?: number;
        spam_urls?: string[];
        history?: {
            hits?: number;
            customer_hits?: number;
            first_seen?: string;
            last_seen?: string;
        };
        flags?: string[];
    };
    email_details?: {
        email?: string;
        score?: number;
        deliverable?: boolean;
        domain_details?: {
            tld?: string;
            name?: string;
            created?: string;
            updated?: string;
            registered?: boolean;
            registrar_name?: string;
            disposable?: boolean;
            free?: boolean;
            custom?: boolean;
            dmarc_enforced?: boolean;
            spf_strict?: boolean;
            valid_mx?: boolean;
            accept_all?: boolean;
            suspicious_tld?: boolean;
            website_exists?: boolean;
        };
        account_details?: AccountDetails,
        breach_details?: {
            haveibeenpwned_listed?: boolean;
            breaches?: string[]; 
        };
        history?: {
            hits?: number;
            customer_hits?: number;
            first_seen?: string;
            last_seen?: string;
        };
        flags?: string[];
    };
    bin_details?: {
        card_bin?: string;
        bin_bank?: string;
        bin_card?: string;
        bin_type?: string;
        bin_level?: string;
        bin_country?: string;
        bin_valid?: boolean;
        card_issuer?: string;
    };
    phone_details?: {
        number?: string;
        valid?: boolean;
        disposable?: boolean;
        type?: string;
        country?: string;
        carrier?: string;
        score?: number;
        history?: {
            hits?: number;
            customer_hits?: number;
            first_seen?: string;
            last_seen?: string;
        };
        account_details?: AccountDetails,
        flags?: string[];
    };
    applied_rules?: string[];
} | undefined;

export type ICustomerIdentityData = {
    dateReceived: Date;
    id?: string;
    status?: string;
    score?: number;
    apiVersion?: string;
    ipDetails?: {
        ip?: string;
        score?: number;
        country?: string;
        state?: string;
        city?: string;
        isp?: {
            name?: string;
            type?: string;
            description?: string;
        };
        lat?: number;
        lon?: number;
        tor?: boolean;
        harmful?: boolean;
        vpn?: boolean;
        webProxy?: boolean;
        publicProxy?: boolean;
        spamList?: {
            count?: number;
            urls?: string[];
        };
        history?: {
            searches?: number;
            uniqueSearches?: number;
            firstSearch?: string | null;
            recentSearch?: string | null;
        };
        flags?: string[];
    };
    emailDetails?: {
        email?: string;
        score?: number;
        deliverable?: boolean;
        domain?: {
            tld?: string;
            name?: string;
            dateCreated?: string;
            dateUpdated?: string;
            registered?: boolean;
            registrarName?: string;
            disposable?: boolean;
            free?: boolean;
            custom?: boolean;
            dmarcEnforced?: boolean;
            spfStrict?: boolean;
            validMx?: boolean;
            acceptAll?: boolean;
            suspiciousTld?: boolean;
            websiteExists?: boolean;
            registeredAccounts?: string[];
            registeredAccountsDetails?: Record<string, object>[];
        };
        breachDetails?: {
            emailCompromised?: boolean;
            breaches?: string[];
        };
        history?: {
            searches?: number;
            uniqueSearches?: number;
            firstSearch?: string | null;
            recentSearch?: string | null;
        };
        flags?: string[];
    };
    binDetails?: {
        bin?: string;
        issuanceBank?: string;
        cardNetwork?: string;
        cardType?: string;
        cardLevel?: string;
        cardCountry?: string;
        binValid?: boolean;
        cardIssuer?: string;
    };
    phoneDetails?: {
        number?: string;
        valid?: boolean;
        disposable?: boolean;
        type?: string;
        country?: string;
        carrier?: string;
        score?: number;
        registeredAccounts?: string[];
        registeredAccountsDetails?: Record<string, object>[];
        history?: {
            searches?: number;
            uniqueSearches?: number;
            firstSearch?: string | null;
            recentSearch?: string | null;
        };
        flags?: string[];
    };
    appliedRules?: string[];
}

export type CustomerIdentityAttributes = CoreCustomerIdentityAttributes & AttributesIndexSignature;

export type CoreCustomerIdentityAttributes = {
    ip?: string;
    email?: string;
    user_firstname?: string;
    user_lastname?: string;
    card_fullname?: string;
    card_bin?: string;
    card_expire?: string;
    card_last?: string;
    avs_result?: string;
    phone_number?: string;
    transaction_amount?: string;
    transaction_currency?: string;
    shipping_country?: string;
    shipping_city?: string;
    shipping_region?: string;
    shipping_zip?: string;
    shipping_street?: string;
    shipping_street2?: string;
    shipping_phone?: string;
    shipping_fullname?: string;
    billing_country?: string;
    billing_city?: string;
    billing_region?: string;
    billing_zip?: string;
    billing_street?: string;
    billing_street2?: string;
    billing_phone?: string;
};

export type AttributesIndexSignature = {
    [key: string]: string | undefined;
};
import {
    ICustomerIdentityData,
    AccountDetails,
    SeonRawData
} from './types';

const seonKeysToRename: Record<string, string> = {
    'image': 'photo',
    'profile_url': 'url',
    'uid': 'maritalStatus',
};

export const seonGenderProperty = 'gender';
export const maleGender = 'Male';
export const femaleGender = 'Female';
export const seonPropertiesToRename = [ 'profile_url', 'image', 'uid' ];

const IspTypeDictonary: Record<string, string> = {
    COM: 'Commercial',
    ORG: 'Organization',
    EDU: 'School/College/University',
    LIB: 'Library',
    ISP: 'Fixed Line ISP',
    MOB: 'Mobile ISP',
    DCH: 'Data Center/Web Hosting',
    RCV: 'Reserved',
    MIL: 'Military',
    CDN: 'Content Delivery Network',
    GOV: 'Government',
    SES: 'Search Engine Spider',
};

const emailAccountsProperties = [
    'url',
    'profile_url',
    'photo',
    'image',
    'location',
    'name',
    'company',
    'title',
    'website',
    'twitter',
    'company',
    'city',
    'gender',
    'id',
    'handle',
    'bio',
    'age',
    'language',
    'state',
    'about',
    'first_name',
    'identity_verified',
    'haveibeenpwned_listed',
];

const phoneAccountsProperties = [
    'last_seen',
    'photo',
    'image',
    'name',
    'country',
    'city',
    'gender',
    'id',
    'handle',
    'bio',
    'about',
    'age',
    'language',
    'state',
    'uid',
    'full_name',
    'date_of_birth',
];

const registeredAccountsTypeEmail = 'email';
const registeredAccountsTypePhone = 'phone';

export function mapSeonToCustomerIdentityData(seon: SeonRawData): ICustomerIdentityData{
    const { emailRegisteredAccountsNames, emailRegisteredAccountsDetails } =
        mapRegisteredAccounts(seon, registeredAccountsTypeEmail);
    const { phoneRegisteredAccountsNames, phoneRegisteredAccountsDetails } =
        mapRegisteredAccounts(seon, registeredAccountsTypePhone);

    return {
        dateReceived: new Date(),
        id: seon?.id,
        status: seon?.state,
        score: seon?.fraud_score,
        apiVersion: seon?.version,
        ipDetails: {
            ip: seon?.ip_details?.ip,
            score: seon?.ip_details?.score,
            country: seon?.ip_details?.country,
            state: seon?.ip_details?.state_prov,
            city: seon?.ip_details?.city,
            isp: {
                name: seon?.ip_details?.isp_name,
                type: seon?.ip_details?.type,
                description: IspTypeDictonary[seon?.ip_details?.type as string],
            },
            lat: seon?.ip_details?.latitude,
            lon: seon?.ip_details?.longitude,
            tor: seon?.ip_details?.tor,
            harmful: seon?.ip_details?.harmful,
            vpn: seon?.ip_details?.vpn,
            webProxy: seon?.ip_details?.web_proxy,
            publicProxy: seon?.ip_details?.public_proxy,
            spamList: {
                count: seon?.ip_details?.spam_number,
                urls: seon?.ip_details?.spam_urls,
            },
            history: {
                searches: seon?.ip_details?.history?.hits,
                uniqueSearches: seon?.ip_details?.history?.customer_hits,
                firstSearch: seon?.ip_details?.history?.first_seen,
                recentSearch: seon?.ip_details?.history?.last_seen,
            },
            flags: seon?.ip_details?.flags || [],
        },
        emailDetails: {
            email: seon?.email_details?.email,
            score: seon?.email_details?.score,
            deliverable: seon?.email_details?.deliverable,
            domain: {
                tld: seon?.email_details?.domain_details?.tld,
                name: seon?.email_details?.domain_details?.name,
                dateCreated: seon?.email_details?.domain_details?.created,
                dateUpdated: seon?.email_details?.domain_details?.updated,
                registered: seon?.email_details?.domain_details?.registered,
                registrarName: seon?.email_details?.domain_details?.registrar_name,
                disposable: seon?.email_details?.domain_details?.disposable,
                free: seon?.email_details?.domain_details?.free,
                custom: seon?.email_details?.domain_details?.custom,
                dmarcEnforced: seon?.email_details?.domain_details?.dmarc_enforced,
                spfStrict: seon?.email_details?.domain_details?.spf_strict,
                validMx: seon?.email_details?.domain_details?.valid_mx,
                acceptAll: seon?.email_details?.domain_details?.accept_all,
                suspiciousTld: seon?.email_details?.domain_details?.suspicious_tld,
                websiteExists: seon?.email_details?.domain_details?.website_exists,
                registeredAccounts: emailRegisteredAccountsNames,
                registeredAccountsDetails: emailRegisteredAccountsDetails,
            },
            breachDetails: {
                emailCompromised: seon?.email_details?.breach_details?.haveibeenpwned_listed,
                breaches: seon?.email_details?.breach_details?.breaches || [],
            },
            history: {
                searches: seon?.email_details?.history?.hits,
                uniqueSearches: seon?.email_details?.history?.customer_hits,
                firstSearch: seon?.email_details?.history?.first_seen
                    ? new Date(seon?.email_details?.history?.first_seen)?.toISOString()
                    : null,
                recentSearch: seon?.email_details?.history?.last_seen
                    ? new Date(seon?.email_details?.history?.last_seen)?.toISOString()
                    : null,
            },
            flags: seon?.email_details?.flags || [],
        },
        binDetails: {
            bin: seon?.bin_details?.card_bin,
            issuanceBank: seon?.bin_details?.bin_bank,
            cardNetwork: seon?.bin_details?.bin_card,
            cardType: seon?.bin_details?.bin_type,
            cardLevel: seon?.bin_details?.bin_level,
            cardCountry: seon?.bin_details?.bin_country,
            binValid: seon?.bin_details?.bin_valid,
            cardIssuer: seon?.bin_details?.card_issuer,
        },
        phoneDetails: {
            number: seon?.phone_details?.number,
            valid: seon?.phone_details?.valid,
            disposable: seon?.phone_details?.disposable,
            type: seon?.phone_details?.type,
            country: seon?.phone_details?.country,
            carrier: seon?.phone_details?.carrier,
            score: seon?.phone_details?.score,
            registeredAccounts: phoneRegisteredAccountsNames,
            registeredAccountsDetails: phoneRegisteredAccountsDetails,
            history: {
                searches: seon?.phone_details?.history?.hits,
                uniqueSearches: seon?.phone_details?.history?.customer_hits,
                firstSearch: seon?.phone_details?.history?.first_seen
                    ? new Date(seon?.phone_details?.history?.first_seen)?.toISOString()
                    : null,
                recentSearch: seon?.phone_details?.history?.last_seen
                    ? new Date(seon?.phone_details?.history?.last_seen)?.toISOString()
                    : null,
            },
            flags: seon?.phone_details?.flags || [],
        },
        appliedRules: seon?.applied_rules || [],
    };
}

function mapRegisteredAccounts(seon: SeonRawData, type: "email" | "phone") {
    const rawAccountsDetails = type === registeredAccountsTypeEmail
        ? seon?.email_details?.account_details
        : seon?.phone_details?.account_details;

    if (!rawAccountsDetails) {
        // console.warn(`Missing ${type} account details`);
        return {};
    }

    const registeredAccounts = filterByRegistered(rawAccountsDetails);
    const registeredAccountsNames = registeredAccounts?.map(account => account?.[0]);
    const registeredAccountsDetails = getRegisteredAccountsDetails(rawAccountsDetails, type);

    return type === registeredAccountsTypeEmail
        ? {
            emailRegisteredAccountsNames: registeredAccountsNames,
            emailRegisteredAccountsDetails: registeredAccountsDetails,
        }
        : {
            phoneRegisteredAccountsNames: registeredAccountsNames,
            phoneRegisteredAccountsDetails: registeredAccountsDetails,
        };
}

function filterByRegistered(rawAccountsDetails: AccountDetails) {
    return Object.entries(rawAccountsDetails)
        ?.filter(a => a?.[1]?.registered);
}

function getRegisteredAccountsDetails(rawAccountsDetails: AccountDetails, type: "email" | "phone") {
    const rawAccountDetailsCopy = JSON.parse(JSON.stringify(rawAccountsDetails));
    let notRegistered;
    let properties;

    for (const account in rawAccountDetailsCopy) {
        notRegistered = !rawAccountDetailsCopy?.[account]?.registered;
        if (notRegistered) {
            delete rawAccountDetailsCopy[account];
            continue;
        }

        properties = Object.keys(rawAccountDetailsCopy[account]);
        properties?.forEach(property => {
            checkIfPropertyRequired(type, rawAccountDetailsCopy, account, property);
            checkIfShouldRenameProperty(rawAccountDetailsCopy, account, property);
            mapGenderProperty(rawAccountDetailsCopy, account, property);
        });

        checkIfRegisteredAccountHasProperties(rawAccountDetailsCopy, account);
    }

    return rawAccountDetailsCopy;
}

function checkIfRegisteredAccountHasProperties(rawAccountDetailsCopy: AccountDetails, account: string) {
    const hasProperties = Object.keys(rawAccountDetailsCopy?.[account]).length;

    if (!hasProperties) {
        delete rawAccountDetailsCopy[account];
    }
}

function checkIfPropertyRequired(
    type: "email" | "phone",
    rawAccountDetailsCopy: AccountDetails, 
    account: string, 
    property: string
) {
    const requiredProperty =
        type === registeredAccountsTypeEmail
            ? emailAccountsProperties.includes(property)
            : phoneAccountsProperties.includes(property);

    if (!requiredProperty) {
        delete rawAccountDetailsCopy[account][property];
    }

    const ifEmptyPropertyValue = !rawAccountDetailsCopy[account][property];
    if (ifEmptyPropertyValue) {
        delete rawAccountDetailsCopy[account][property];
    }
}

function mapGenderProperty(
    rawAccountDetailsCopy: AccountDetails, 
    account: string, 
    property: string
) {
    if (property === seonGenderProperty) {
        rawAccountDetailsCopy[account][property] =
            getGender(rawAccountDetailsCopy[account][property] as number);
    }
}

function checkIfShouldRenameProperty(
    rawAccountDetailsCopy: AccountDetails, 
    account: string, 
    property: string
) {
    if (seonPropertiesToRename.includes(property)) {
        const renamedProperty = {
            [seonKeysToRename[property]]: rawAccountDetailsCopy[account][property],
        };

        Object.assign(rawAccountDetailsCopy[account], renamedProperty);
        delete rawAccountDetailsCopy[account][property];
    }
}

function getGender(seonValue: number) {
    if (!seonValue) {
        return;
    }

    return seonValue === 1 ? maleGender : femaleGender;
}
# Purpose
This script migrates all documents from "seon" collection to "customer-identity" + addes field extensions.customerIdentity.requestHash to UDO

https://linear.app/chargeflow/issue/CHA-5376/remap-seon-collection-docs-to-customer-identity-collection-docs

## Motivation
This script is one of the 'seon' > 'customer-identity' domain name migration. 

## Running this script
`npx ts-node ./script.ts <dry or wet-primary>`

### Modes
dry: does not update anything, writes the total amount of docs for update to the console 
wet-primary: 
    - remaps seon rawData taken from response.rawData field of a document to normalized customer-identity data, 
    - saves new document in customer-identity collection with the following data:
        {
            requestHash,
            request
            response,
            rawData,
            dateCreated,
        }

### How this script was tested
Btoth modes were tested on dev mongo DB cluster
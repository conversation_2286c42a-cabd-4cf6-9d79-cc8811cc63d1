import { MongoClient } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext, ScriptMode } from "../shared/script-base";
import { mapSeonToCustomerIdentityData } from "./customer-identity-mapper";
import { webcrypto } from 'crypto';
import { CustomerIdentityAttributes } from "./types";

export class MigrateSeonDocsToCustomerIdentityCollection extends ScriptBase {
    public getName(): string {
        return 'Migrate SEON docs to customer identity collection';
    }
    public getAuthor(): string {
        return '<PERSON> Tkach';
    }
    public getTicket(): string {
        return 'CHA-5376';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.logic(context);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context);
    }

    async wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method is not implemented.');
    }

    async logic(ctx: ScriptContext) {
        const mode = (ctx.args[0] ?? 'dry') as ScriptMode;
        const client: MongoClient = await loadMongoClient();
        
        const batchSize = process.env.BATCH_SIZE ? parseInt(process.env.BATCH_SIZE) : 10;
        let skip = 0;
        let totalDocsToUpdate = 0;
        let totalCIDocsUpdated = 0;
        let totalDisputeDocsUpdated = 0;
        
        while(true) {
            const seonDocs = await client
                .db('chargeflow_api')
                .collection('seon')
                .find(
                    {
                        'disputeId': {$exists: true},
                    }, 
                    {
                        projection: {
                            'response.rawData.seon': 1,
                            'requestObject': 1,
                            'disputeId': 1,
                        }
                    })
                .skip(skip)
                .limit(batchSize)
                .toArray();
            
            if (seonDocs.length === 0) {
                break;
            }
            
            let CIDocs: any[] = [];
            for (const doc of seonDocs) {
                CIDocs.push(await this.mapSeonToCIDocs(doc));
            }
            
            const updateCIDocsOps = await this.createUpdateCIDocsOps(CIDocs);
            const updateDisputeOps = await this.createUpdateDisputesOps(CIDocs);
            totalDocsToUpdate += CIDocs.length;
            
            if (mode !== 'dry') {
                const updatedCIDocsCount = await this.updateCIDocs(client, updateCIDocsOps);
                const updatedDisputesCount = await this.updateDisputes(client, updateDisputeOps);

                console.log('Updated %s customer identity docs', updatedCIDocsCount);
                totalCIDocsUpdated += updatedCIDocsCount;

                console.log('Updated %s disputes', updatedDisputesCount);
                totalDisputeDocsUpdated += updatedDisputesCount;

                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            skip += batchSize;
        }

        if (mode === 'dry') {
            console.log('Dry run mode');
            console.log('Total docs to update: %s', totalDocsToUpdate);
        } else {
            console.log('Total customer identity docs updated: %s', totalCIDocsUpdated);
            console.log('Total disputes updated: %s', totalDisputeDocsUpdated);
        }
    }

    private async mapSeonToCIDocs(doc: any) {
        const response = mapSeonToCustomerIdentityData(doc.response.rawData.seon);
        
        const customerIdentityAttributes = doc.requestObject;
        delete customerIdentityAttributes.config;
        const requestHash = await this.createRequestHash(customerIdentityAttributes);
        return {
            update: {
                requestHash,
                request: customerIdentityAttributes,
                response,
                rawData: doc.response.rawData.seon,
                dateCreated: response.dateReceived,
            },
            disputeId: doc.disputeId,
        }
    }

    private async createRequestHash(
        customerIdentityAttributes: CustomerIdentityAttributes
    ): Promise<string> {
        for (const key in customerIdentityAttributes) {
            if (!customerIdentityAttributes[key]) {
                delete customerIdentityAttributes[key];
            }
        }

        const sorted = Object.keys(customerIdentityAttributes)
            .sort()
            .reduce((acc, key) => {
                acc[key] = customerIdentityAttributes[key];
                return acc;
            }, {} as CustomerIdentityAttributes);

        const jsonStr = JSON.stringify(sorted);
        const encodedData = new TextEncoder().encode(jsonStr);
        const hash = await webcrypto.subtle.digest("SHA-256", encodedData);

        const hashArray = Array.from(new Uint8Array(hash));
        const hashHex = hashArray
            .map((b) => b.toString(16).padStart(2, "0"))
            .join("");


        return hashHex;
    }

    private async createUpdateCIDocsOps(data: any[]): Promise<any[]> {
        const updateCIDocsOps: any[] = [];

        for (const item of data) {
            updateCIDocsOps.push({
                updateOne: {
                    filter: { 'requestHash': item.update.requestHash },
                    update: {
                        $set: item.update,
                    },
                    upsert: true,
                }
            });
        }

        return updateCIDocsOps;
    }

    private async createUpdateDisputesOps(data: any[]): Promise<any[]> {
        const updateDisputesOps: any[] = [];

        for (const item of data) {
            updateDisputesOps.push({
                updateOne: {
                    filter: { '_id': item.disputeId },
                    update: {
                        $set: {
                            'extensions.customerIdentity.requestHash': item.update.requestHash,
                        }
                    }
                }
            });
        }

        return updateDisputesOps;
    }

    private async updateCIDocs(client: MongoClient, updateOps: any[] | []): Promise<number> {
        try {
            return client
                .db('chargeflow_api')
                .collection('customer-identity')
                .bulkWrite(updateOps)
                .then((result) => result.modifiedCount);
        } catch (error) {
            console.error('Error updating customer identity docs: ', error);
            throw error;
        }
    }

    private async updateDisputes(client: MongoClient, updateOps: any[] | []): Promise<number> {
        try {
            return client
                .db('chargeflow_api')
                .collection('disputes')
                .bulkWrite(updateOps)
                .then((result) => result.modifiedCount);
        } catch (error) {
            console.error('Error updating disputes: ', error);
            throw error;
        }
    }
}

const script = new MigrateSeonDocsToCustomerIdentityCollection();
script.main();
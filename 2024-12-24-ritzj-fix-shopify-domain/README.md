# Purpose:
To correct the `shop.domain` value, taken from the `domain` prop from Shopify Shop API response (instead of taking it from `myshopify_domain`).

- Get list of shops where `shop.domain` is not equal to either `shop.rawData.domain` OR `shop.rawData.dataShop.rawData.domain`.
- Run a `bulkWrite` to update all these shop's domain value

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with

**MONGO_URI**
**MONGO_DB_NAME**

See `.env.example` for an example.

### How this script was tested
[x] Wet run against dev & prod with data diff validation afterwards

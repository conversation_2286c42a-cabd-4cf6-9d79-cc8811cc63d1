import dotenv from "dotenv";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { Db, ObjectId } from "mongodb";
import AWS from "aws-sdk";
import { DataAccessUnitOfWork } from "@chargeflow-team/data-access";
import { MongoConfig } from "@chargeflow-team/chargeflow-utils-sdk";
dotenv.config();
import fs from 'fs';

export class Script extends ScriptBase {

  public db!: Db;
  public getName(): string {
    return "Fix shpify shop domain";
  }

  public getAuthor(): string {
    return "Ritz Jumola";
  }

  public getTicket(): string {
    return "CHA-11172";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  async getProblemShops(offset = 0, limit = 1000) {
    const query = {
      // date_updated: { $gte: new Date('2024-10-23T10:50:20.835+00:00')},
      platform: 'shopify',
      rawData: { $ne: null },
      $or: [
        {
          'rawData.domain': { $exists: true },
          $expr: {
            $and: [
              { $ne: ['$domain', '$rawData.domain'] },
            ]
          }
        },
        {
          'rawData.dataShop.rawData.domain': { $exists: true },
          $expr: {
            $and: [
              { $ne: ['$domain', '$rawData.dataShop.rawData.domain'] },
            ]
          }
        }
      ],
    }

    const COL = this.db.collection('shops');
    return await COL.find(query).skip(offset).limit(limit).toArray();
  }

  prepareUpdateQueryForBulkWrite(shop: {
    _id: ObjectId,
    domain: string;
    rawData: {
      domain?: string;
      dataShop?: {
        rawData?: {
          domain?: string;
        }
      }
    }
  }) {
    return {
        filter: { _id: shop._id },
        set: { $set: {
          'domain': shop.rawData.domain || shop.dataShop?.rawData?.domain,
        } },
        old: {
          domain: shop.domain,
        }
    };
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow(["MONGO_URI", "MONGO_DB_NAME"]);

      const mongo = await loadMongoClient();
      console.log("Mongo client loaded");

      this.db = mongo.db(process.env.MONGO_DB_NAME);

      let OFFSET = 0;
      const LIMIT = 1000;

      const QUERIES = [];

      while (true) {
        console.log("Fetching shops...", 'OFFSET:', OFFSET);
        const shops = await this.getProblemShops(OFFSET, LIMIT);
        console.log("Shops found:", shops.length);
        if (shops.length === 0) {
          console.log("No shops found");
          break;
        }

        const queries = shops.map(shop => this.prepareUpdateQueryForBulkWrite(shop));

        QUERIES.push(...queries);

        OFFSET += shops.length;
      }

      await fs.writeFileSync(`queries.json`, JSON.stringify(QUERIES, null, 2));

      const bulkWriteOps = QUERIES.map(query => ({
        updateOne: {
            filter: query.filter,
            update: query.set,
        },
      }));

      console.log('Total bulk ops', bulkWriteOps.length);

      await fs.writeFileSync(`bulkOps.json`, JSON.stringify(bulkWriteOps, null, 2));

      // chunk by 1000
      const CHUNK_SIZE = 1000;
      const chunks = Array.from({ length: Math.ceil(bulkWriteOps.length / CHUNK_SIZE) }, (_, i) => bulkWriteOps.slice(i * CHUNK_SIZE, i * CHUNK_SIZE + CHUNK_SIZE));
      console.log('Total chunks', chunks.length);

      if (isDryRun) {
        console.log("Dry run mode enabled, no changes made to the database");
        return;
      }

      for (const chunk of chunks) {
        const res = await this.db.collection('shops').bulkWrite(chunk);
        console.log('Bulk write result', res);
      }
    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }
}

const script = new Script();
script.main();
import dotenv from 'dotenv';
import { ObjectId } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';

dotenv.config();

export class ScriptUpdateStripeUserCompanies extends ScriptBase {
    public getName(): string {
        return 'Set stripe onboarded shops onboardingStageV1 to Payment';
    }

    public getAuthor(): string {
        return '<PERSON><PERSON> Jumola';
    }

    public getTicket(): string {
        return 'CHA-3739';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    getUpdateCompanyQuery(companyId: string) {
        const filter = { _id: new ObjectId(companyId) };
        const update = {
            $set: {
                'users.dateUpdated': new Date(),
                'users.status': 'active',
                'users.onboardingStageV1': 'Payment',
                'users.platform': 'stripe'
            }
        }
        return { updateOne: { filter, update } };
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {
        const client = await loadMongoClient();
        const shopsCollection = client
            .db('chargeflow_api')
            .collection('shops');
        const companiesCollection = client
            .db('chargeflow_api')
            .collection('companies');
        const aggQuery = [
            { $match: { registrationType: 'stripe-app' } },
            {
                $lookup: {
                    from: 'companies',
                    pipeline: [
                        { $match: { 'users.onboardingStageV1': 'Step 1' } }
                    ],
                    localField: 'chargeflow_id',
                    foreignField: 'users.chargeflowId',
                    as: 'companies'
                }

            },
            {
                $project: { 
                    companyId: { $first: '$companies._id' },
                    chargeflowId: '$chargeflow_id',
                    shopName: '$name'
                }
            },
            { $match: { companyId: { $ne: null } } }
        ];
        const handleDryRun = async () => {
            const toUpdate = await shopsCollection.aggregate(aggQuery).toArray();
            console.log(`Found ${toUpdate.length} to update.`);
            console.log('Will update the following shops...');
            console.log(JSON.stringify(toUpdate, null, 2));
            console.log('Update queries...');
            const updateQueries = toUpdate.map(
                i => this.getUpdateCompanyQuery(i.companyId)
            );
            console.log(JSON.stringify(updateQueries, null, 2));
        };

        const handleWetRun = async () => {
            const toUpdate = await shopsCollection.aggregate(aggQuery).toArray();
            console.log(`Found ${toUpdate.length} to update.`);
            const updateQueries = toUpdate.map(
                i => this.getUpdateCompanyQuery(i.companyId)
            );
            const res = await companiesCollection.bulkWrite(updateQueries);
            console.log('Execution result...');
            console.log(JSON.stringify(res, null, 2));
        };

        try {
            if (isDryRun) {
                await handleDryRun();
            } else {
                await handleWetRun();
            }
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        } finally {
            client.close();
        }
    }
}

const script = new ScriptUpdateStripeUserCompanies();
script.main();

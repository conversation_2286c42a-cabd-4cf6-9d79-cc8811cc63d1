# Purpose:
To update stripe onboarded shops with onboardingStageV1='Step 1' to 'Payment'

# Motivation:
Assume role only accepts 'Payment' and 'Completed' onboarding stage status. Some stripe users initially has 'Step 1' for companies.users.onboardingStageV1.

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**

### How this script was tested
[x] Wet run against dev & test
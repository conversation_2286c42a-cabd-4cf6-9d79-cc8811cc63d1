# Purpose
 Some customers have some specific requirements for their refunds, such as thresholds. Until those are implemented as features of our auto-refund system, we'll have to exclude those from the auto-refund mechanism. 
https://linear.app/chargeflow/issue/CHA-10104/write-a-script-to-exclude-some-customers-from-auto-refund

# Motivation
We need to disable autorefund for specific customers

# Running this script
`npx ts-node ./script.ts <dry | wet-primary>`

# Required env variables
MONGO_URI=<mongo_uri_string> (`alertRegistry` collection is in use)
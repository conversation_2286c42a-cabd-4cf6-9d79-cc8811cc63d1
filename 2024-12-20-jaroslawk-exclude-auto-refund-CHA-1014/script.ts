import { MongoClient, ObjectId } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase } from "../shared/script-base";
import chargeflowIds from './prodChargeflowIds.json';

export class AutoRefundExclusionScript extends ScriptBase {
  public getName() {
    return "Auto Refund Exclusion Script";
  }

  public getAuthor() {
    return "Jaroslaw Kyc";
  }

  public getTicket(): string {
    return "CHA-10104/write-a-script-to-exclude-some-customers-from-auto-refund";
  }

  public async dryRun(): Promise<void> {
    await this.handle(true);
  }

  public async wetRunOnPrimary(): Promise<void> {
    await this.handle(false);
  }

  public async wetRunOnSecondary(): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async handle(isDryRun: boolean) {
    if (!process.env.MONGO_URI) {
      throw new Error("MONGO_URI is not set");
    }
    const client: MongoClient = await loadMongoClient();
    const db = client.db("chargeflow_api");
    const alertsRegistryCollection = db.collection("alertsRegistry");
    const parsedIds = chargeflowIds.map((id) => new ObjectId(id));

    if (isDryRun) {
      const documents = await alertsRegistryCollection.find({ chargeflowId: { $in: parsedIds } }).toArray();
      console.log(`Dry Run: Found ${documents.length} documents to update.`);
      documents.forEach(doc => {
        console.log(`Would update document with _id: ${doc._id}, setting disableAutoRefund to true and pushing to history.`);
      });
    } else {
      const res = await alertsRegistryCollection.updateMany(
        { chargeflowId: { $in: parsedIds } },
        [
          {
            $set: {
              disableAutoRefund: true,
              history: {
                $concatArrays: [
                  { $ifNull: ["$history", []] },
                  [
                    {
                      date: new Date(),
                      user: "script",
                      field: "disableAutoRefund",
                      oldValue: "$disableAutoRefund",
                      newValue: true,
                    },
                  ],
                ],
              },
            },
          },
        ]
      );
      console.log(`Wet Run: Updated ${res.modifiedCount} documents.`);
    }

    await client.close();
  }
}

const script = new AutoRefundExclusionScript();
script.main();

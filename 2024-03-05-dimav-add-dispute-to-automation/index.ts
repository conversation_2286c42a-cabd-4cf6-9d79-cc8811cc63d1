import { SQS } from '@aws-sdk/client-sqs';
import dotenv from 'dotenv';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import disputesIds from './tmp_disputes.json';
import PromisePool from '@supercharge/promise-pool';
import { SFN } from '@aws-sdk/client-sfn';

dotenv.config();

class Script extends ScriptBase {
    getName() {
        return 'Add disputeId to automation sqs';
    }
    getAuthor() {
        return 'Di<PERSON> Vinogradov';
    }
    getTicket() {
        return 'CHA-3044';
    }

    constructor(private sfn: SFN) {
        super();
    }

    private async SendMessage(disputeId: string) {
        return this.sfn.startExecution({
            stateMachineArn: process.env.AUTOMATION_ELIGIBILITY_SFN_ARN,
            input: JSON.stringify({ detail: { disputeId } }),
        });
    }

    private async processInBatches(disputeIds: string[], batchSize: number, waitTime: number) {
        for (let i = 0; i < disputeIds.length; i += batchSize) {
            console.log(`Processing batch ${i / batchSize + 1} of ${Math.ceil(disputeIds.length / batchSize)}`);
            const firstIndex = i;
            const lastIndex = Math.min(i + batchSize, disputeIds.length) - 1;
            const percentage = ((lastIndex + 1) / disputeIds.length) * 100;
            console.log(`First dispute index:${firstIndex}\nLast dispute index: ${lastIndex}\nPercentage: ${percentage.toFixed(2)}%`);
            const batch = disputeIds.slice(i, i + batchSize);
            const { results, errors } = await PromisePool.withConcurrency(10)
                .for(batch)
                .process(async (disputeId) => {
                    return await this.SendMessage(disputeId);
                });

            if (errors.length > 0) {
                console.log(`Batch failed for ${errors.length} disputes`);
                const errorIds = errors.map((err) => err.item).join(', ');
                console.log('##################\nIDS:\n##################');
                console.log(errorIds);
            }
            console.log(`Batch succeeded for ${results.length} ids`);

            if (i + batchSize < disputeIds.length) {
                console.log(`Waiting for ${waitTime / 1000} seconds before processing next batch...`);
                await new Promise((resolve) => setTimeout(resolve, waitTime));
            }
        }
    }

    async dryRun(context: ScriptContext): Promise<void> {
        throw new Error('Not implemented');
    }
    async wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Not implemented');
    }
    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        const batchSize = 10;
        const waitTime = 60000;
        await this.processInBatches(disputesIds, batchSize, waitTime);
    }
}

const script = new Script(new SFN());
script.main();

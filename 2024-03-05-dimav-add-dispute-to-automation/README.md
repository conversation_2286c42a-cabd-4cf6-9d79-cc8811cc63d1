# Purpose
To assign an experiment and autosubmit disputes that are already open.

## Motivation
To be able to add disputes that were opened before the date of the experiment's creation to the current SQS handling process.
Gives us an option to analyze which variables would be missing or what would stop disputes from being auto submitted and also to send out
disputes that are **open** but unprocessed by experts.

## Warning
Currently we go over 10 disputes a minute due to a limitation from OpenAI API for Custom assistants. This script is not optimized for speed and should be run with caution.

## Running this script
`npx ts-node ./index.ts wet-primary | tee log.txt`

NOTE: a `.env` file is expected to be present in CWD
`tmp_disputes.json` also expected = array of strings which represent the disputeIds.
The following env variables are required:

**MONGO_URI**
**AUTOMATION_ELIGIBILITY_SFN_ARN** - ARN of the Enrichment SFN we're trying to send messages to

### How this script was tested
[x] Wet run against development checking the message gets added and processed by the appropriate pooling function.
import { DataAccessUnitOfWork } from '@chargeflow-team/data-access';

export interface Logger {
    log: (message: string) => void;
}

export class CreateAccountsFromChargeflowIdsMigrationLogic {
    constructor(
        private readonly dataAccess: DataAccessUnitOfWork,
        private readonly logger: Logger,
    ) {}

    async execute(batchSize: number, dryRun: boolean): Promise<void> {
        try {
            this.logger.log('Migrating accounts from chargeflow ids started, please wait ...');

            const pageSizeForInitialFind = 1;
            const limitForInitialFind = 1;

            const { pagination } = await this.dataAccess.chargeflowId.findWithoutAccountId(
                pageSizeForInitialFind,
                limitForInitialFind,
            );

            const totalBatches = Math.ceil(pagination.totalItems / batchSize);

            this.logger.log(`Total accounts to process: ${pagination.totalItems}`);
            this.logger.log(`Total batches to process: ${totalBatches}`);

            if (dryRun) {
                this.logger.log('Dry run mode enabled, no changes made to the database');

                return;
            }

            for (let currentBatch = 1; currentBatch <= totalBatches; currentBatch++) {
                try {
                    const session = await this.dataAccess.startTransaction();
                    const page = 1;

                    const { items } = await this.dataAccess.chargeflowId.findWithoutAccountId(
                        page,
                        batchSize,
                        session,
                    );

                    await Promise.all(
                        items.map(async chargeflowId => {
                            const account = await this.dataAccess.accountRepository.add({
                                email: chargeflowId.account_email,
                                legacy: {
                                    chargeflowId: chargeflowId._id,
                                    shopId: chargeflowId.shop_id,
                                    customerId: chargeflowId.customer_id,
                                },
                            }, session);

                            return this.dataAccess.chargeflowId.assignAccountId(
                                chargeflowId._id,
                                account._id,
                                session,
                            );
                        }),
                    );
                } catch (error) {
                    await this.dataAccess.abortTransaction();

                    throw error;
                }

                await this.dataAccess.commitTransaction();
            }

            this.logger.log('Migrating accounts from chargeflow ids finished successfully');

        } catch (error) {
            if (error instanceof Error) {
                this.logger.log(`Migrating accounts from chargeflow ids failed, reason: ${error.message}`);
            }

            throw error;
        }
    }
}

import { mock } from 'jest-mock-extended';
import { CreateAccountsFromChargeflowIdsMigrationLogic, Logger } from './logic';
import { DataAccessUnitOfWork, paginationMocks, chargeflowIdMocks, accountMocks } from '@chargeflow-team/data-access';
import { ClientSession } from 'mongodb';

describe(CreateAccountsFromChargeflowIdsMigrationLogic.name, () => {
    let createAccountsFromChargeflowIdsMigration: CreateAccountsFromChargeflowIdsMigrationLogic;
    const dataAccess = mock<DataAccessUnitOfWork>();
    const logger = mock<Logger>();
    const session = mock<ClientSession>();

    beforeEach(() => {
        createAccountsFromChargeflowIdsMigration = new CreateAccountsFromChargeflowIdsMigrationLogic(dataAccess, logger);
    });

    afterEach(jest.clearAllMocks);

    it('performs the migration successfully', async () => {
        expect.assertions(23);

        // GIVEN
        const batchSize = 2;
        const dryRun = false;
        const totalItems = 5;

        const pagination = paginationMocks.getPaginationResult({
            totalItems,
        });

        const chargeflowIdsItems = Array.from(
            { length: totalItems },
            () => chargeflowIdMocks.getChargeflowId(),
        );

        const accountItems = Array.from(
            { length: totalItems },
            () => accountMocks.getAccount(),
        );

        dataAccess.startTransaction
            .mockResolvedValue(session);

        dataAccess.commitTransaction
            .mockResolvedValue();

        dataAccess.chargeflowId.findWithoutAccountId = jest.fn()
            .mockResolvedValueOnce({ items: chargeflowIdsItems.slice(0, 1), pagination })
            .mockResolvedValueOnce({ items: chargeflowIdsItems.slice(0, 2), pagination })
            .mockResolvedValueOnce({ items: chargeflowIdsItems.slice(2, 4), pagination })
            .mockResolvedValueOnce({ items: chargeflowIdsItems.slice(4, 5), pagination });

        dataAccess.chargeflowId.assignAccountId = jest.fn();

        dataAccess.accountRepository.add = jest.fn()
            .mockResolvedValueOnce(accountItems[0])
            .mockResolvedValueOnce(accountItems[1])
            .mockResolvedValueOnce(accountItems[2])
            .mockResolvedValueOnce(accountItems[3])
            .mockResolvedValueOnce(accountItems[4]);

        // WHEN
        await createAccountsFromChargeflowIdsMigration.execute(batchSize, dryRun);

        // THEN
        expect(logger.log)
            .toHaveBeenCalledTimes(4);

        expect(logger.log)
            .toHaveBeenNthCalledWith(1, 'Migrating accounts from chargeflow ids started, please wait ...');

        expect(logger.log)
            .toHaveBeenNthCalledWith(2, 'Total accounts to process: 5');

        expect(logger.log)
            .toHaveBeenNthCalledWith(3, 'Total batches to process: 3');

        expect(logger.log)
            .toHaveBeenNthCalledWith(4, 'Migrating accounts from chargeflow ids finished successfully');

        expect(dataAccess.chargeflowId.findWithoutAccountId)
            .toHaveBeenCalledTimes(4);

        expect(dataAccess.chargeflowId.findWithoutAccountId)
            .toHaveBeenNthCalledWith(1, 1, 1);

        expect(dataAccess.chargeflowId.findWithoutAccountId)
            .toHaveBeenNthCalledWith(2, 1, 2, session);

        expect(dataAccess.chargeflowId.findWithoutAccountId)
            .toHaveBeenNthCalledWith(3, 1, 2, session);

        expect(dataAccess.chargeflowId.findWithoutAccountId)
            .toHaveBeenNthCalledWith(4, 1, 2, session);

        expect(dataAccess.accountRepository.add)
            .toHaveBeenCalledTimes(5);

        for (let i = 0; i < totalItems; i++) {
            expect(dataAccess.accountRepository.add)
                .toHaveBeenNthCalledWith(
                    i + 1,
                    {
                        email: chargeflowIdsItems[i].account_email,
                        legacy: {
                            chargeflowId: chargeflowIdsItems[i]._id,
                            shopId: chargeflowIdsItems[i].shop_id,
                            customerId: chargeflowIdsItems[i].customer_id,
                        },
                    },
                    session,
                );

            expect(dataAccess.chargeflowId.assignAccountId)
                .toHaveBeenNthCalledWith(
                    i + 1,
                    chargeflowIdsItems[i]._id,
                    accountItems[i]._id, session,
                );
        }

        expect(dataAccess.startTransaction)
            .toHaveBeenCalledTimes(3);

        expect(dataAccess.commitTransaction)
            .toHaveBeenCalledTimes(3);
    });

    it('performs the migration successfully in dry run mode', async () => {
        expect.assertions(7);

        // GIVEN
        const batchSize = 2;
        const dryRun = true;
        const totalItems = 5;

        const pagination = paginationMocks.getPaginationResult({
            totalItems,
        });

        dataAccess.chargeflowId.findWithoutAccountId = jest.fn()
            .mockResolvedValueOnce({ pagination });

        // WHEN
        await createAccountsFromChargeflowIdsMigration.execute(batchSize, dryRun);

        // THEN
        expect(logger.log)
            .toHaveBeenCalledTimes(4);

        expect(logger.log)
            .toHaveBeenNthCalledWith(1, 'Migrating accounts from chargeflow ids started, please wait ...');

        expect(logger.log)
            .toHaveBeenNthCalledWith(2, 'Total accounts to process: 5');

        expect(logger.log)
            .toHaveBeenNthCalledWith(3, 'Total batches to process: 3');

        expect(logger.log)
            .toHaveBeenNthCalledWith(4, 'Dry run mode enabled, no changes made to the database');

        expect(dataAccess.chargeflowId.findWithoutAccountId)
            .toHaveBeenCalledTimes(1);

        expect(dataAccess.chargeflowId.findWithoutAccountId)
            .toHaveBeenCalledWith(1, 1);
    });

    describe('throws an error', () => {
        it('when the migration failed outside of a specific batch processing', async () => {
            expect.assertions(4);

            // GIVEN
            const batchSize = 2;
            const dryRun = false;

            const error = new Error('Something went wrong');

            dataAccess.chargeflowId.findWithoutAccountId = jest.fn()
                .mockRejectedValueOnce(error);

            // WHEN
            await expect(createAccountsFromChargeflowIdsMigration.execute(batchSize, dryRun))
                // THEN
                .rejects.toThrow(error);

            expect(logger.log)
                .toHaveBeenCalledTimes(2);

            expect(logger.log)
                .toHaveBeenNthCalledWith(1, 'Migrating accounts from chargeflow ids started, please wait ...');

            expect(logger.log)
                .toHaveBeenNthCalledWith(2, `Migrating accounts from chargeflow ids failed, reason: ${error.message}`);
        });

        it('when the migration failed inside a specific batch processing', async () => {
            expect.assertions(8);

            // GIVEN
            const batchSize = 2;
            const dryRun = false;
            const totalItems = 5;

            const pagination = paginationMocks.getPaginationResult({
                totalItems,
            });

            const chargeflowIdsItems = Array.from(
                { length: totalItems },
                () => chargeflowIdMocks.getChargeflowId(),
            );

            const error = new Error('Something went wrong');

            dataAccess.startTransaction
                .mockResolvedValue(session);

            dataAccess.chargeflowId.findWithoutAccountId = jest.fn()
                .mockResolvedValueOnce({ pagination });

            dataAccess.startTransaction
                .mockResolvedValue(session);

            dataAccess.abortTransaction
                .mockResolvedValue();

            dataAccess.chargeflowId.findWithoutAccountId = jest.fn()
                .mockResolvedValueOnce({ items: chargeflowIdsItems.slice(0, 1), pagination })
                .mockResolvedValueOnce({ items: chargeflowIdsItems.slice(0, 2), pagination });

            dataAccess.accountRepository.add = jest.fn()
                .mockRejectedValueOnce(error);

            // WHEN
            await expect(createAccountsFromChargeflowIdsMigration.execute(batchSize, dryRun))
                // THEN
                .rejects.toThrow(error);

            expect(logger.log)
                .toHaveBeenCalledTimes(4);

            expect(logger.log)
                .toHaveBeenNthCalledWith(1, 'Migrating accounts from chargeflow ids started, please wait ...');

            expect(logger.log)
                .toHaveBeenNthCalledWith(2, 'Total accounts to process: 5');

            expect(logger.log)
                .toHaveBeenNthCalledWith(3, 'Total batches to process: 3');

            expect(logger.log)
                .toHaveBeenNthCalledWith(4, `Migrating accounts from chargeflow ids failed, reason: ${error.message}`);

            expect(dataAccess.startTransaction)
                .toHaveBeenCalledTimes(1);

            expect(dataAccess.abortTransaction)
                .toHaveBeenCalledTimes(1);
        });
    });
});
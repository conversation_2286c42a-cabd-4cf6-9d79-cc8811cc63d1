import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Db, MongoClient, ObjectId } from 'mongodb';
import * as fs from 'fs';

dotenv.config();

export class ScriptDeleteUser extends ScriptBase {
    db: Db | undefined = undefined;

    public getName(): string {
        return 'Delete a user';
    }

    public getAuthor(): string {
        return '<PERSON><PERSON> Ju<PERSON>la';
    }

    public getTicket(): string {
        return 'CHA-9365';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async getCrmsForChargeflowAccounts(mongoClient: MongoClient): Promise<Record<string, any>[]> {
        const db = mongoClient.db('chargeflow_api');
        const collection = db.collection('crms');
        const aggFilter = [
            {
                $addFields: {
                    chargeflowId: {
                        $toObjectId: "$Chargeflow ID",
                    },
                },
            },
            {
                $lookup: {
                    from: "shops",
                    localField: "chargeflowId",
                    foreignField: "chargeflow_id",
                    as: "shops",
                },
            },
            {
                $match: {
                    "shops.email": {
                        $regex: ".*@chargeflow.io"
                    }
                },
            },
        ];
        return collection.aggregate(aggFilter).toArray();
    }

    private async getCrmsForDeletedAccounts(mongoClient: MongoClient): Promise<Record<string, any>[]> {
        const db = mongoClient.db('chargeflow_api');
        const collection = db.collection('crms');
        const aggFilter = [
            {
                $addFields: {
                    chargeflowId: {
                        $toObjectId: "$Chargeflow ID",
                    },
                },
            },
            {
                $lookup: {
                    from: "shops",
                    localField: "chargeflowId",
                    foreignField: "chargeflow_id",
                    as: "shops",
                },
            },
            {
                $match: {
                    "shops._id": {
                        $exists: false,
                    }
                },
            },
        ];
        return collection.aggregate(aggFilter).toArray();
    }

    private async deleteCrms(mongoClient: MongoClient, crmsIds: ObjectId[]): Promise<void> {
        const db = mongoClient.db('chargeflow_api');
        const collection = db.collection('crms');
        console.log(`Deleting these from crms ${crmsIds}`);
        const res = await collection.deleteMany({
            _id: { $in: crmsIds },
        });
        console.log('Deletion result:', res);
    }

    private async logic(context: ScriptContext, isDryRun: boolean) {
        const client = await loadMongoClient();
        const crmsForCFAccounts = await this.getCrmsForChargeflowAccounts(client);
        console.log(`Found ${crmsForCFAccounts.length} with chargeflow accounts.`);
        fs.writeFileSync('crms-cf-accounts.json', JSON.stringify(crmsForCFAccounts, null, 2));

        const crmsForDeletedAccounts = await this.getCrmsForDeletedAccounts(client);
        console.log(`Found ${crmsForDeletedAccounts.length} for deleted accounts.`);
        fs.writeFileSync('crms-deleted-accounts.json', JSON.stringify(crmsForDeletedAccounts, null, 2));

        const ids = [
            ...crmsForCFAccounts.map(i => i._id),
            ...crmsForDeletedAccounts.map(i => i._id)
        ];
        if (!isDryRun) {
            await this.deleteCrms(client, ids);
        }
    }
}

const script = new ScriptDeleteUser();
script.main();

# Purpose:
Running mongoDB pipeline from a script. This script is for running pipelines that will output to another collection (and create it, if not exist).

# Motivation:
Adding new collections to the DB with documented actions.

# Running this script
`npx ts-node ./script.ts <dry or wet-primary> <base-collection> <output-collection> <pipeline-file-path>`

NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**

Examples:
`~ npx ts-node ./script.ts wet-primary disputes trashedDisputes31 ./trashedDisputes.json`
`~ npx ts-node ./script.ts wet-primary asanaRawData1 asanaRawDataQualified2 ./asanaRawDataQualified.json`
`~ npx ts-node ./script.ts wet-primary asanaMappedData disputes ./mergeAsanaMappedIntoDisputes.json`
There is also another script for deleting "trashed disputes" from the main collection
`~ npx ts-node ./removeTrashedDisputes.ts wet-primary disputes_copy`

### Current pipelines

The current pipelines are for the Asana migration process:
`trashedDisputes` - Should remove corrupted data from `disputes` collection
`asanaRawDataQualified` - Should take only `asana` dipsutes that are NOT exist in `disputes`
`asanaMappedApprovedData` - After filtering only the not exist disputes, and mapping them into the UDO model, this pipeline should filter some disputes that were not mapped correctly, and filter duplications.
Regarding `dispute.id`, this pipeline will filter every dispute with `dispute.id` with one of these issues:
 - Has length < 2
 - Contains `#`
 - Contains `+`
 - Contains `*`
 - Contains `.`

`mergeAsanaMappedIntoDisputes` - Should marge the mapped disputes according the `dispute.id` field. For this pipeline to run, we should have a unique index on `dispute.id` for both source and target collection. (This was tested against a copy of `disputes` and worked as expected. BUT, in the main `disputes` collection, the creation of such unique index was failed)
`mergeAsanaMappedIntoDisputesWithoutIndex` - Should merge the mapped disputes into the main `disputes` collection. 

### How this script was tested
Tested on dev
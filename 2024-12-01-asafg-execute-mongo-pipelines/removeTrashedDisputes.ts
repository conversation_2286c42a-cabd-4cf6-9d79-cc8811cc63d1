import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { WithId, Collection, Filter, MongoClient, UnorderedBulkOperation } from 'mongodb';
import { Document, ObjectId } from 'bson';
import { ScriptBase, ScriptContext } from '../shared/script-base';

dotenv.config();

const defaultDbName = 'chargeflow_api';

export class RemoveTrashedDisputes extends ScriptBase {
    public getName(): string {
        return 'Remove disputes from main disputes collection';
    }

    public getAuthor(): string {
        return 'Asaf Gamliel';
    }

    public getTicket(): string {
        return 'CHA-10248';
    }

    async removeTrashedDisputes(collection: Collection<Document>) {
        const filter: Filter<Document> = {
            $and: [
                {
                    "chargeflow.importTaskId": {
                        "$exists": true
                    }
                }
                , {
                    "chargeflow.importTaskId": {
                        "$ne": null
                    }
                }
                , {
                    "dispute.dateReceived": {
                        "$lte": new Date('2024-06-30T23:59:59.999Z')
                    }
                }]
            };
        const result = await collection.deleteMany(filter);
        console.log(`Deleted ${result.deletedCount} documents`);
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {

        const handleDryRun = async (sourceCollection: Collection<Document>) => {
            console.log('Dry run mode. sourceCollection: ', sourceCollection.namespace);
        };

        const handleWetRun = async (sourceCollection: Collection<Document>) => {
            await this.removeTrashedDisputes(sourceCollection);
        };

        try {
            const sourceCollection = ctx.args[1];

            const mongoClient: MongoClient = await loadMongoClient();
            const collection = mongoClient.db(defaultDbName).collection(sourceCollection);
            if (isDryRun) {
                await handleDryRun(collection);
            } else {
                await handleWetRun(collection);
            }
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        }
    }
}

const script = new RemoveTrashedDisputes();
script.main();

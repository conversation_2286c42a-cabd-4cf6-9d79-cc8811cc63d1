import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { WithId, Collection, Filter, MongoClient, UnorderedBulkOperation } from 'mongodb';
import { Document, ObjectId } from 'bson';
import { ScriptBase, ScriptContext } from '../shared/script-base';

dotenv.config();

const defaultDbName = 'chargeflow_api';
const timeout = 1000 * 60 * 60 * 3; // 2 hours
const bulkSize = 500;

export class ExecuteMongoPipelines extends ScriptBase {
    public getName(): string {
        return 'Move disputes to trashedDisputes collection';
    }

    public getAuthor(): string {
        return 'Asaf Gamliel';
    }

    public getTicket(): string {
        return 'CHA-10248';
    }

    private dateParser = (key: string, value: any) => {
        if (typeof value === "string" && value.startsWith("$date")) { 
            return new Date(value.replace("$date:", ""));
        } else { 
            return value; 
        } 
    }

    async sendPaginatedAggergationCommandToMongo(sourceCollection: Collection<Document>, runnablePipeline: any, skipIndex: number, mergeCommand: any): Promise<void> {
        let offset = 0;
        while (offset < 640000) {
            if (offset > 0) {
                const lastIndex = runnablePipeline.length - 1;
                runnablePipeline[lastIndex] = mergeCommand;
            }
            runnablePipeline[skipIndex]["$skip"] = offset;
            console.log(`Aggregating documents. Offset: ${offset}. Pipeline: ${JSON.stringify(runnablePipeline)}`);
            const result = await sourceCollection.aggregate(runnablePipeline).maxTimeMS(timeout).toArray();
            console.log('Aggregation command sent. Result: ', result);
            offset += bulkSize;
        }
    }

    getPipeline(targetCollection: string, piplineFilePath: string, dbName: string = defaultDbName): any {
        const pipelineSource = require(piplineFilePath);
        const parameterizedPipeline = JSON.stringify(pipelineSource).replace("dbName", dbName).replace("collectionName", targetCollection);
        const runnablePipeline = JSON.parse(parameterizedPipeline, this.dateParser);
        return runnablePipeline;
    }
    
    async sendAggergationCommandToMongo(sourceCollection: Collection<Document>, targetCollection: string, piplineFilePath: string, dbName: string = defaultDbName): Promise<void> {
        const runnablePipeline = this.getPipeline(targetCollection, piplineFilePath, dbName);
        console.log(`Aggregating documents. Pipeline: ${JSON.stringify(runnablePipeline)}, dbName: ${dbName}, collectionName: ${targetCollection}`);
        const pipeLineCommands = runnablePipeline.map((t: any) => Object.keys(t).pop());
        console.log('pipeLineCommands: ', pipeLineCommands);
        if (pipeLineCommands.includes("$skip") && pipeLineCommands.includes("$limit")) {
            const skipIndex = pipeLineCommands.indexOf("$skip");
            const limitIndex = pipeLineCommands.indexOf("$limit");
            runnablePipeline[limitIndex]["$limit"] = bulkSize;
            const mergeCommand = { "$merge": { "into": { "db": dbName, "coll": targetCollection } ,"on": "dispute.id", "whenMatched": "replace", "whenNotMatched": "insert" } };
            await this.sendPaginatedAggergationCommandToMongo(sourceCollection, runnablePipeline, skipIndex, mergeCommand);
            return;
        }
        const result = await sourceCollection.aggregate(runnablePipeline).maxTimeMS(timeout).toArray();
        console.log('Aggregation command sent. Result: ', result);
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {

        const handleDryRun = async (sourceCollection: Collection<Document>, targetCollection: string, pipelineFilePath: string) => {
            console.log('Dry run mode. sourceCollection: ', sourceCollection.namespace, 'targetCollection: ', targetCollection, 'pipelineFilePath: ', pipelineFilePath);
        };

        const handleWetRun = async (sourceCollection: Collection<Document>, targetCollection: string, pipelineFilePath: string) => {
            await this.sendAggergationCommandToMongo(sourceCollection, targetCollection, pipelineFilePath);
        };

        try {
            const sourceCollection = ctx.args[1];
            const targetCollection = ctx.args[2];
            const pipelineFilePath = ctx.args[3];
            const mongoClient: MongoClient = await loadMongoClient();
            const collection = mongoClient.db(defaultDbName).collection(sourceCollection);
            if (isDryRun) {
                await handleDryRun(collection, targetCollection, pipelineFilePath);
            } else {
                await handleWetRun(collection, targetCollection, pipelineFilePath);
            }
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        }
    }
}

const script = new ExecuteMongoPipelines();
script.main();

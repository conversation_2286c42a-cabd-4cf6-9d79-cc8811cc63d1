[{"$match": {"dispute.stage": {"$ne": null}, "dispute.amount": {"$ne": 0}, "dispute.reason": {"$ne": null}, "dispute.processor": {"$ne": "asana"}, "shopId": {"$ne": null}, "chargeflowId": {"$ne": null}, "shopName": {"$ne": ""}, "accountId": {"$ne": null}, "dispute.id": {"$not": {"$regex": "^.{0,1}$|[#+\\*\\.]"}}}}, {"$sort": {"dispute.id": 1, "dispute.dateCreated": -1}}, {"$group": {"_id": "$dispute.id", "latestDocument": {"$first": "$$ROOT"}}}, {"$replaceRoot": {"newRoot": "$latestDocument"}}, {"$out": {"db": "dbN<PERSON>", "coll": "collectionName"}}]
# Purpose:
To delete records in automation_response and experimentLinks based on disputeId

# Motivation:
In case we want to rerun a batch of disputes, we need to delete the records in automation_response and experimentLinks based on disputeId otherwise the process stops as the disputes were already processed.

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**
`tmp_disputes.json` in the script folder also expected = array of strings which represent the disputeIds.



### How this script was tested
[x] Wet run against dev & test
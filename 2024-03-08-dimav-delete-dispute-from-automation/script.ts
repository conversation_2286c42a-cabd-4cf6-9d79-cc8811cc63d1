import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import disputes from './tmp_disputes.json';
import { ObjectId } from 'mongodb';

dotenv.config();

const BATCH_SIZE = 1000;

export class ScriptAddPickUpDateToShippingObject extends ScriptBase {
    public getName(): string {
        return 'Delete disputes from experimentLink & automation_response';
    }

    public getAuthor(): string {
        return 'Dima Vinogradov';
    }

    public getTicket(): string {
        return 'CHA-3106';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {
        const client = await loadMongoClient();
        const responsesCollection = client
            .db('chargeflow_api')
            .collection('automation_responses');
        const linksCollection = client
            .db('chargeflow_api')
            .collection('experimentLinks');
        const query = {
            disputeId: { $in: disputes.map((d) => new ObjectId(d)) },
        };
        const handleDryRun = async () => {
            const linksP = linksCollection.find(query).toArray();
            const responsesP = responsesCollection.find(query).toArray();
            const [links, responses] = await Promise.all([
                linksP,
                responsesP,
            ]);
            console.log('#'.repeat(40));
            console.log(
                'THESE RECORDS WILL BE PERMANENTLY DELETED FROM THE TWO COLLECTIONS',
            );
            console.log('#'.repeat(40));
            console.log(`Links for disputeIds (count: ${links.length}):`);
            console.log(links.map((l) => l.disputeId).join('\n'));
            console.log('#'.repeat(40));
            console.log(
                `Responses for disputeIds (count: ${responses.length}):`,
            );
            console.log(responses.map((r) => r.disputeId).join('\n'));
            console.log('#'.repeat(40));
            console.log(
                'THESE RECORDS WILL BE PERMANENTLY DELETED FROM THE TWO COLLECTIONS',
            );
            console.log('#'.repeat(40));
        };

        const handleWetRun = async () => {
            const linksP = linksCollection.deleteMany(query);
            const responsesP = responsesCollection.deleteMany(query);
            const [links,responses] = await Promise.all([linksP, responsesP]);
            console.log(`Deleted ${links.deletedCount} links`);
            console.log(`Deleted ${responses.deletedCount} responses`);
        };

        try {
            if (isDryRun) {
                await handleDryRun();
            } else {
                await handleWetRun();
            }
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        } finally {
            client.close();
        }
    }
}

const script = new ScriptAddPickUpDateToShippingObject();
script.main();

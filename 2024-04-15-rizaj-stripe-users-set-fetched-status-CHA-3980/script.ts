import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';

dotenv.config();

export class ScriptFixFetchingStripeAccounts extends ScriptBase {
    public getName(): string {
        return 'Set stripe shops with no disputes disputes_status from fetching to fetched';
    }

    public getAuthor(): string {
        return 'Riza Jumola';
    }

    public getTicket(): string {
        return 'CHA-3980';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        console.log('Running in dry run mode (not changing anything)');
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async logic(ctx: ScriptContext, isDryRun: boolean) {
        const client = await loadMongoClient();
        const shopsCollection = client
            .db('chargeflow_api')
            .collection('shops');
        const yesterday = new Date().setDate(new Date().getDate() - 1);
        const query = { 
            disputes_status: 'fetching',
            date_created: { $lt: new Date(yesterday) }
        };
        const handleDryRun = async () => {
            const toUpdate = await shopsCollection.find(query).toArray();
            console.log(`Found ${toUpdate.length} to update.`);
            console.log('Will update the following shops...');
            console.log(toUpdate.map(shop => shop.name));
        };

        const handleWetRun = async () => {
            const updateResponse = await shopsCollection.updateMany(
                query,
                {
                    $set: { disputes_status: 'fetched' }
                }
            );
            console.log('Update result: ', updateResponse);
        };

        try {
            if (isDryRun) {
                await handleDryRun();
            } else {
                await handleWetRun();
            }
        } catch (err) {
            console.error(err);
            if (err instanceof Error) {
                throw err;
            }
        } finally {
            client.close();
        }
    }
}

const script = new ScriptFixFetchingStripeAccounts();
script.main();

# Purpose
This script adds a new property named chargeflow.evidenceUploadCategories on disputes. It should be executed after rolling out changes to the system that will continue populating it when new evidences are uploaded.

https://linear.app/chargeflow/issue/CHA-2836/add-disputeschargeflowevidenceuploadcategories-to-speed-up-admin-seach

## Motivation
Dispute search, implemented in `disputes-api` (https://github.com/chargeflow-team/disputes-api/blob/a0ab520cf4ae6f3c97fab2f307284e937cc09aff/src/service/mapper/queryMapper.js) makes a VERY complicated mongodb query that peeks into `chargeflow.evidences.evidenceUploadCategory`, where `chargeflow.evidences` is an array of objects that have `evidenceUploadCategory`. This query has extremely poor performance and at some point it started timing out completely, leading to https://linear.app/chargeflow/issue/CHA-2417/admin-cannot-filter-the-disputes-table

## Running this script
`npx ts-node ./add-evidence-upload-categories.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console
wet-secondary - does not update the source table, writes results to another collection (disputes_test), adding documents as needed
wet-primary - UPDATES THE DISPUTES TABLE. Use with extreme caution!

### How this script was tested
[x] An object without chargeflow.evidences.evidenceUploadCategory set - see that it's being created successfully 
[x] An object with chargeflow.evidences.evidenceUploadCategory set to {dummy:true}, to verify existing data (dummy) remains after the update
[x] An object without chargeflow.evidences at all -  chargeflow.evidences.evidenceUploadCategory should not be created or should be created with no properties
[x] An object without chargeflow - same
[x] A chargeflow.evidences with no evidenceUploadCategory
[x] A chargeflow.evidences.evidenceUploadCategory with empty string

[ ] Verify update query with {$set:{
            [`chargeflow.uploadedEvidenceCategories.abc`]: true,
        }} that creates chargeflow.uploadedEvidenceCategories if it does not exist
[ ] Verify update query with {$set:{
            [`chargeflow.uploadedEvidenceCategories.abc`]: true,
        }} that only updates uploadedEvidenceCategories and adds 'abc'. An existing property 'def':true should be unaffected
       
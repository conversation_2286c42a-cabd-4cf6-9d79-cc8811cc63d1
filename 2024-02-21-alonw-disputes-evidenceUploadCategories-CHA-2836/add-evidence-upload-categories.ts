import { ObjectId } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";
const mongodbAggregationQuery: any[] = [
  {
    $match: {
      // "_id": new ObjectId("61d74b547646142fb0329363"),
      //"chargeflow.evidences": { $exists: true, $ne: [], $type: "array"},
      //"chargeflow.evidences.evidenceUploadCategory": { $ne: "", $type: "string"},
      //"chargeflow.evidenceUploadCategoriesMigrated": {$ne: true}
      "chargeflow.uploadedEvidenceCategories": {$exists: true}      
    },
  },
  // {
  //     $limit: 20000 //Update only few documents for testing in wet mode
  // },
  {
      $project: {
        chargeflow: 1,
        original_chargeflow: "$chargeflow",
      }
  },
  {
    $unwind: "$chargeflow.evidences",
  },
  {
    $match: {
      "chargeflow.evidences.evidenceUploadCategory": { $ne: "", $type: "string"},
    },
  },
  {
    $group: {
      _id: "$_id",
      original_chargeflow: { $first: "$original_chargeflow" },
      newCategories: {
        $addToSet: "$chargeflow.evidences.evidenceUploadCategory",
      },
    },
  },
  {
    $project: {
      _id: 1, // Include _id field for proper matching
      chargeflow: {
        $mergeObjects: [
          "$original_chargeflow",
          {
            evidenceUploadCategoriesMigrated: true,
            evidenceUploadCategories: {
              $mergeObjects: [
                "$original_chargeflow.evidenceUploadCategories",
                {
                  $arrayToObject: {
                    $map: {
                      input: "$newCategories",
                      as: "category",
                      in: {
                        k: "$$category",
                        v: true,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
  },
];

const mergeStage: any = {
  $merge: {
    into: "disputes",
    on: "_id",
    whenMatched: "merge",
    whenNotMatched: "discard",
  },
};

const mergeStage_anotherCollection: any = {
  $merge: {
    into: "disputes_test",
    on: "_id",
    whenMatched: "merge",
    whenNotMatched: "insert",
  },
};

class Script extends ScriptBase {
  public getName(): string {
    return "Add chargeflow.evidenceUploadCategories to disputes";
  }
  public getAuthor(): string {
    return "Alon Weiss"
  }
  public getTicket(): string {
    return "CHA-2836";
  }
  async dryRun(context: ScriptContext) {
    console.log("Running in dry run (not changing anything)");
    const client = await loadMongoClient();
    const results = await client
      .db("chargeflow_api")
      .collection("disputes")
      .aggregate(mongodbAggregationQuery)
      .toArray();
      for (const result of results) {
        console.log('dispute id: ' + result._id.toString());
        console.log('chargeflow.evidences: ');
        console.dir(result.chargeflow.evidences);

        console.log('chargeflow.evidences types: ');
        console.log(result.chargeflow.evidences.map((evd:{evidenceUploadCategory:string})=>evd.evidenceUploadCategory).filter(Boolean).join(', '));

        console.log('chargeflow.evidenceUploadCategories: ');
        console.dir(result.chargeflow.evidenceUploadCategories);

        console.log('-'.repeat(50));
      }
    //console.log(JSON.stringify(results, null, "\t"));
  }
  async wetRunOnSecondary(context: ScriptContext) {
    await this.doWetRun(mergeStage_anotherCollection);
  }
  async wetRunOnPrimary(context: ScriptContext) {
    await this.doWetRun(mergeStage);
  }

  private async doWetRun(mergeStage: any) {
    const client = await loadMongoClient();
    const session = client.startSession();
    try {
      session.startTransaction();
      const results = await client
        .db("chargeflow_api")
        .collection("disputes")
        .aggregate([...mongodbAggregationQuery, mergeStage], {allowDiskUse: true}).toArray();
      console.log(JSON.stringify(results, null, "\t"));
      console.log("Commiting transaction");
      await session.commitTransaction();
      client.close();
    } catch (ex) {
      console.warn('Aborting transaction');
      await session.abortTransaction();
      console.error(ex);
      throw ex;
    } finally {
      session.endSession();
    }
  }

}

const script = new Script();
script.main();
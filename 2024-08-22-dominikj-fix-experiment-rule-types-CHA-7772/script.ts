import dotenv from "dotenv";
import { AnyBulkWriteOperation, Document, ObjectId } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";

dotenv.config();

const BATCH_SIZE = 150;

export class ScriptRemapExperimentRuleComparisonTypes extends ScriptBase {
  public getName(): string {
    return "Map Shipment pick update from raw data to shippingData";
  }

  public getAuthor(): string {
    return "Dominik Jancik";
  }

  public getTicket(): string {
    return "";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private fixRules = (doc: Document) => {
    // console.log("Fixing rules for experiment: ", JSON.stringify(doc));
    const originalRules = doc.rules;
    const updatedRules = originalRules.map((rule: any) => {
      const comparison = { ...rule.comparison };
      //   console.log("Comparison: ", JSON.stringify(comparison, null, 2));
      if (comparison.type === "string" && comparison.value === true) {
        console.log("    Detected boolean value in string comparison");
        comparison.type = "boolean";
      }

      let key = rule.key;
      if (comparison.type === "logical" && rule.key === undefined) {
        console.log("    Detected missing key in logical comparison");
        key = "";
      }

      return {
        ...rule,
        key,
        comparison,
      };
    });

    return {
      ...doc,
      rules: updatedRules,
    };
  };

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    let lastId: ObjectId | undefined;
    let updatedCount = 0;
    const client = await loadMongoClient();
    const collection = client.db("chargeflow_api").collection("experiments");

    const handleDryRun = (doc: Document) => {
      console.log(`Experiment ID: ${doc._id}`);
        console.log("Before:");
        console.log(doc.rules);
      const fixedDocument = this.fixRules(doc).rules;
        console.log("After:");
        console.log(fixedDocument);
        console.log("-".repeat(50));
    };

    const createBulkOperation = (doc: Document) => {
      const fixedDoc = this.fixRules(doc);
      return {
        updateOne: {
          filter: { _id: doc._id },
          update: {
            $set: {
              rules: fixedDoc.rules,
            },
          },
        },
      };
    };

    while (true) {
      const query = lastId
        ? {
            _id: { $gt: lastId },
          }
        : {};

      const documents = await collection
        .find(query, {})
        .sort({ _id: 1 })
        .limit(BATCH_SIZE)
        .toArray();

      if (documents.length === 0) {
        console.log("No more documents to process.");
        break;
      }

      let bulkOps: AnyBulkWriteOperation<Document>[] = [];

      if (!isDryRun) {
        bulkOps = documents.map(createBulkOperation);
      } else {
        documents.forEach(handleDryRun);
      }

      if (!isDryRun && bulkOps.length > 0) {
        const result = await collection.bulkWrite(bulkOps);
        updatedCount += result.modifiedCount;
        console.log(
          `${new Date().toISOString()}: Processed ${
            documents.length
          } documents. Total updated so far: ${updatedCount}`
        );
      }

      lastId = documents[documents.length - 1]._id;
    }

    if (!isDryRun) {
      console.log(`Total documents updated: ${updatedCount}`);
    }
  }
}

const script = new ScriptRemapExperimentRuleComparisonTypes();
script.main();

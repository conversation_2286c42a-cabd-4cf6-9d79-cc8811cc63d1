# Purpose:
The purpose of this script is to fix data inconsistencies in `experiments` collection.

- Replace `string` type comparisons against boolean value with `boolean`
- Ensure that all rules with `logical` type comparison have key set to empty string `""`

# Motivation:
Failed adherence to types and schemas of experiments data. Surfaced via `automation-management` integration for the Automation Studio project.

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**


### How this script was tested
[x] Wet run against dev & prod with data diff validation afterwards
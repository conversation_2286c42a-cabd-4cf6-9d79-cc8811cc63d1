import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Collection, Db, ObjectId } from 'mongodb';

dotenv.config();

const DISABLED_STATUSES = ['inactive', 'disabled'];
const NON_STRIPE_PLATFORMS = ['shopify', 'sas'];

export class ReconcileShopsStatuses extends ScriptBase {
  db: Db | undefined = undefined;

  public getName(): string {
    return "Create script to reconcile shops statuses";
  }

  public getAuthor(): string {
    return "Rafael Schuster";
  }

  public getTicket(): string {
    return "CHA-10322";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async logic(context: ScriptContext, isDryRun: boolean): Promise<void> {
    console.log('Reconciling shops statuses..');

    const mongoClient = await loadMongoClient();
    const shopsMongoCollection = mongoClient.db("chargeflow_api").collection("shops");
    console.log('Successfully generated DB client');

    try {
      const shopsCompanyAndBilling: any[] = await this.findShopsCompanyAndBillingData(shopsMongoCollection);
      console.log('Processing data for %s shops', shopsCompanyAndBilling.length);
      if (!shopsCompanyAndBilling.length) {
        console.warn('No shops to reconcile data. Ending process...');
        return;
      }

      const chargeflowIdsToUpdate = shopsCompanyAndBilling
        .reduce((ids, data) => {
          if (data.chargeflowId) {
            ids.push(new ObjectId(data.chargeflowId));
          }
          return ids;
        }, []);
      console.log('%s shops chargeflow ids to update', chargeflowIdsToUpdate.length);

      if (isDryRun) {
        console.log('Dry run - Would have processed %s chargeflow ids to update. Ending process...', chargeflowIdsToUpdate.length);
        return;
      }

      console.log('Updating many shops statuses...');
      const res = await this.updateManyShopsAsStatusActive(chargeflowIdsToUpdate, shopsMongoCollection);
      console.log('Update many response: %j', res);
    } catch (error) {
      console.error('Error in main processing loop:', error);
      throw error;
    } finally {
      console.log('Cleaning up connections');
      await mongoClient.close();
    }
  }

  async updateManyShopsAsStatusActive(chargeflowIdsToUpdate, shopsMongoCollection: Collection) {
    if (!chargeflowIdsToUpdate.length) {
      return;
    }
    return shopsMongoCollection
      .updateMany(
        { 'chargeflow_id' : { $in: chargeflowIdsToUpdate }}, 
        { $set: { 'chargeflow_status': 'active' }})
      .catch(err => {
        console.log(err);
        throw err;
      });
  }

  async findShopsCompanyAndBillingData(shopsCollection: Collection) {
    const agg = [
      {
        $match: {
          chargeflow_status: { $in: DISABLED_STATUSES },
          platform: { $nin: NON_STRIPE_PLATFORMS }
        }
      },
      {
        $lookup: {
          from: 'companies',
          localField: 'chargeflow_id',
          foreignField: 'users.chargeflowId',
          as: 'company',
          pipeline: [
            {
              $project: {
                _id: 0,
                'users.billing.stripeCustomer.paymentMethodId': 1,
              },
            },
            {
              $match: {
                'users.billing.stripeCustomer.paymentMethodId': { $type: 'string', $ne: '' }
              }
            }
          ],
        },
      },
      {
        $unwind: '$company'
      },
      {
        $project: {
          _id: 0,
          chargeflowId: '$chargeflow_id',
          paymentMethodId: '$company.users.billing.stripeCustomer.paymentMethodId',
        }
      },
    ];

    try {
      const res = await shopsCollection.aggregate(agg).toArray();
      return res;
    } catch (err) {
      console.error(err);
      return [];
    }
  }
}

const script = new ReconcileShopsStatuses();
script.main();
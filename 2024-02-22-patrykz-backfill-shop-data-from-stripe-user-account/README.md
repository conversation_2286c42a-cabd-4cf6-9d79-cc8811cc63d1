# Backfill Shop Data From Stripe User Account

Linear task can be found [here](https://linear.app/chargeflow/issue/CHA-2845/discrepancies-within-the-shop-collection-for-users-from-the-stripe-app)

This migration takes every Shop that was created from Stripe App onboarding flow and enrich the shop's data with `registrationType` set to `stripe-app` and `domain` set to corresponded Stripe User Account's business url. The requirement was specified by Product. After initial investigation plan of the migration was described and agreed with Product.

Before running the migration make sure that you installed the project dependencies:
```
npm i
```

As the next step build the migration:
```
npm run build
```

## Running the migration

1. Set your `.env` file:
```
    MONGO_URI=<connection_string>
    MONGO_AUTH={"username":"<username>","password":"<password>"}
    DISPUTES_EVENT_BUS="disputes_event_bus"
    STACK_NAME="stack_name"
    DRY_RUN=true
    BATCH_SIZE=<number>
```

2. Run the migration in "dry run"
```
node ./out/migrations/2024-02-22-backfill-shop-data-from-stripe-user-account/execute.js
```

3. Analyse the logs and adjust the `BATCH_SIZE` value in your `.env` file if needed
```
...
BATCH_SIZE=<number>
```

4. Disable the "dry run" mode
```
...
DRY_RUN=false
```

5. Run the migration without the "dry run" mode:
```
node ./out/migrations/2024-02-22-backfill-shop-data-from-stripe-user-account/execute.js
```

6. Save the logs from the migration if needed and exit the process in the terminal.

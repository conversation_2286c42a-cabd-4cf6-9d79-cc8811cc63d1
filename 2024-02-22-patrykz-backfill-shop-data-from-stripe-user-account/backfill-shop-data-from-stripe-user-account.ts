import { Logger } from "@chargeflow-team/chargeflow-utils-sdk";
import { DataAccessUnitOfWork } from "@chargeflow-team/data-access";
import { ObjectId } from "mongodb";

export class BackfillShopDataFromStripeUserAccountMigration {
    constructor(
        private readonly dataAccess: DataAccessUnitOfWork,
        private readonly logger: typeof Logger,
    ) {}

    async execute(batchSize: number, dryRun: boolean): Promise<void> {
        try {
            this.logger.info('Backfilling shop data from stripe user account data');

            const pageSizeForInitialFind = 1;
            const limitForInitialFind = 1;

            const { pagination } = await this.dataAccess.stripeUserAccount.findWithLegacyChargeflowIdSet(
                pageSizeForInitialFind,
                limitForInitialFind,
            );

            const totalBatches = Math.ceil(pagination.totalItems / batchSize);

            this.logger.info(`Total shops to process: ${pagination.totalItems}`);
            this.logger.info(`Total batches to process: ${totalBatches}, batch size: ${batchSize}`);

            if (dryRun) {
                this.logger.info('Dry run mode enabled, no changes made to the database');

                return;
            }

            for (let currentBatch = 1; currentBatch <= totalBatches; currentBatch++) {
                try {
                    const session = await this.dataAccess.startTransaction();
                    
                    const { items } = await this.dataAccess.stripeUserAccount.findWithLegacyChargeflowIdSet(
                        currentBatch,
                        batchSize,
                        session,
                    );

                    await Promise.all(
                        items.map(async (item) => {
                            if (!item.chargeflowId) {
                                return;
                            }
                        
                            let parsedId = item.chargeflowId;

                            if (parsedId instanceof ObjectId) {
                                parsedId = item.chargeflowId;
                            } else {
                                parsedId = new ObjectId(item.chargeflowId);
                            }

                            const shop = await this.dataAccess.shop
                                .findOneByLegacyChargeflowId(parsedId, session);

                            if (shop) {
                                await this.dataAccess.shop.update(shop._id, { domain: item.business.url, registrationType: 'stripe-app' }, session);
                            } else {
                                this.logger.info(`Shop with legacy chargeflow id ${parsedId} not found`)
                            }

                            return Promise.resolve();
                        })
                    )
                } catch (error) {
                    if (error instanceof Error) {
                        this.logger.info(`Batch ${currentBatch} of ${totalBatches} failed, reason: ${error.message}`);
                    }

                    await this.dataAccess.abortTransaction();

                    throw error;
                }

                await this.dataAccess.commitTransaction();
            }

            this.logger.info('Backfilling shop data from stripe user account data finished successfully');
        } catch (error) {
            if (error instanceof Error) {
                this.logger.info(`Backfilling shop data from stripe user account data, reason: ${error.message}`);
            }

            throw error;
        }
    }
}

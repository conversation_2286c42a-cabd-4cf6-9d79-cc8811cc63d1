import 'dotenv/config'
import { MongoClient } from 'mongodb';
import { DataAccess } from "@chargeflow-team/data-access";
import { Logger } from "@chargeflow-team/chargeflow-utils-sdk";
import { BackfillShopDataFromStripeUserAccountMigration } from "./backfill-shop-data-from-stripe-user-account";


(async () => {
    const mongoClient = await MongoClient.connect(process.env.MONGO_URI!);
    const dataAccess = await DataAccess.initialize(mongoClient);

    const migration = new BackfillShopDataFromStripeUserAccountMigration(
        dataAccess,
        Logger,
    );

    const batchSize = parseInt(process.env.BATCH_SIZE!, 10);
    const dryRun = process.env.DRY_RUN === 'true';

    await migration.execute(batchSize, dryRun);
})();

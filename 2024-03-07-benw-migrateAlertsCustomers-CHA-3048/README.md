# Purpose
This script migrates current Alerts customers to the new data structure. 

https://linear.app/chargeflow/issue/CHA-2905/data-structure-changes

## Motivation
Alerts is a subscription-based service which we are rolling out to our clients. We had a prelimenary data structure which was initiated as POC. Now that we are moving forward with designing the system from the start, we need to migrate the previously enrolled customers to the new data structure: https://app.eraser.io/workspace/********************

## Running this script
`npx ts-node ./migrate-alerts-customers.ts <dry or wet-secondary or wet-primary> 2>&1 | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD  
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console  
wet-secondary - not utilized in this script
wet-primary - UPDATES THE ALERTSREGISTRY & MERCHANTPROFILES COLLECTIONS. Use with extreme caution!  

### How this script was tested
[x] An object was created in alertsRegistry successfully detailing the customers alertRegistry   
[x] Object in alertsRegistry contains:
  [x] `chargeflowId`
  [x] `accountId`
  [x] `dateCreated`
  [x] `dateUpdated`
  [x] `authorizedAt`  
[x] An object was created in merchantProfiles successfully   
[x] Object in merchantProfiles contains:
  [x] `chargeflowId`
  [x] `accountId`
  [x] `dateCreated`
  [x] `dateUpdated`
  [x] `isLinked`
  [x] `isActive`
  [x] `processor`
  [x] `statementDescriptor`
import { ObjectId } from 'mongodb';

export type updatedAlertsRegistry = {
  accountId: ObjectId | null;
  dateUpdated: Date | null;
  authorizedAt: Date | null;
};

export type MerchantProfile = {
  chargeflowId: ObjectId;
  accountId?: ObjectId | null;
  dateCreated: Date;
  dateUpdated?: Date | null;
  isLinked: boolean;
  isActive: boolean;
  processor: string;
  statementDescriptor: string;
};

export type ProcessorDescriptor = {
  statementDescriptor: string;
  processor: string;
};

export const mapAlertRegistry = (
  accountId: string | null,
  dateAuthorized: Date | null
): updatedAlertsRegistry => {
  return {
    accountId: accountId ? new ObjectId(accountId) : null,
    dateUpdated: dateAuthorized ? new Date(dateAuthorized) : null,
    authorizedAt: dateAuthorized ? new Date(dateAuthorized) : null,
  };
};

const getProcessorName = (processor: string): string => {
  return processor === 'shopify_payments'
    ? 'shopify payments'
    : processor.toLowerCase();
};

export const createMerchantProfiles = (
  chargeflowId: string,
  accountId: string | null,
  dateCreated: Date,
  processorAndDescriptors: ProcessorDescriptor[]
): MerchantProfile[] =>
  processorAndDescriptors.map(entry => ({
    chargeflowId: new ObjectId(chargeflowId),
    accountId: accountId ? new ObjectId(accountId) : null,
    dateCreated: dateCreated,
    isLinked: false,
    isActive: false,
    processor: getProcessorName(entry.processor),
    statementDescriptor: entry.statementDescriptor,
  }));
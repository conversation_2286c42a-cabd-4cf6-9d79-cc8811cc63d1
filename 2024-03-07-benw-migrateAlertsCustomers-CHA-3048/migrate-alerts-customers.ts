import { AnyBulkWriteOperation } from 'mongodb';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { ScriptDataAccess } from './data-access';
import { mapAlertRegistry, createMerchantProfiles } from './shared';

const findQuery = {
  merchantAlertProfile: { $exists: true },
};

class Script extends ScriptBase {
  public getName(): string {
    return 'Migrate current alerts customers to new data structure';
  }

  public getAuthor(): string {
    return 'Ben Weber';
  }

  public getTicket(): string {
    return 'CHA-3048';
  }

  async dryRun(context: ScriptContext) {
    await this.logic(context, true);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error('Method not implemented.');
  }

  async wetRunOnPrimary(context: ScriptContext) {
    await this.logic(context, false);
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    let updatedAlertRegistries = 0;
    let createdMerchantProfiles = 0;
    const client = await loadMongoClient();
    const session = client.startSession();
    const db = client.db('chargeflow_api');
    const alertsRegistryCollection = db.collection('alertsRegistry');
    const merchantProfilesCollection = db.collection('merchantProfiles');

    const alertRegistries = await alertsRegistryCollection
      .find(findQuery)
      .toArray();

    try {
      session.startTransaction();
      const alertsRegistryBulkOps: AnyBulkWriteOperation<Document>[] = [];
      let merchantProfilesBulkOps: AnyBulkWriteOperation<Document>[] = [];

      console.log(`Processing ${alertRegistries.length} Alert Registries...`)
      for (const registry of alertRegistries) {
        console.log(`Processing registry: ${registry.chargeflowId.toString()}`);
        const {
          chargeflowId,
          dateCreated,
          merchantAlertProfile,
          dateAuthorized,
        } = registry;

        const dataAccess = new ScriptDataAccess(client as any);
        const accountId = await dataAccess.findAccountIdByChargeflowId(chargeflowId);
        const alertRegistryUpdatedFields = mapAlertRegistry(accountId, dateAuthorized);


        alertsRegistryBulkOps.push({
          updateOne: {
            filter: { chargeflowId: registry.chargeflowId },
            update: { $set: alertRegistryUpdatedFields },
          },
        });

        const merchantProfiles = createMerchantProfiles(
          chargeflowId,
          accountId,
          dateCreated,
          merchantAlertProfile
        );

        merchantProfiles.forEach(profile => {
          merchantProfilesBulkOps.push({
            updateOne: {
              filter: {
                chargeflowId: profile.chargeflowId,
                statementDescriptor: profile.statementDescriptor,
                processor: profile.processor,
              },
              update: { $setOnInsert: profile },
              upsert: true,
            },
          });
        });
      }

      if (isDryRun) {
        console.log('Dry run mode - no changes are being made.');

        console.log('Alerts Registry Bulk Operations: %j', alertsRegistryBulkOps);
        console.log('Merchant Profiles Bulk Operations: %j',merchantProfilesBulkOps);

        console.log('Total Alert Registries To Update:', alertsRegistryBulkOps.length);
        console.log('Total Merchant Profiles To Create:', merchantProfilesBulkOps.length);
      } else {
        if (alertsRegistryBulkOps.length > 0) {
          const alertRegistryResult = await alertsRegistryCollection.bulkWrite(
            alertsRegistryBulkOps as any,
            { session }
          );
          updatedAlertRegistries += alertRegistryResult.matchedCount;
        }
        if (merchantProfilesBulkOps.length > 0) {
          const merchantProfileResult =
            await merchantProfilesCollection.bulkWrite(
              merchantProfilesBulkOps as any,
              { session, ordered: false }
            );
          createdMerchantProfiles += merchantProfileResult.upsertedCount;
        }
        await session.commitTransaction();
      }
    } catch (error) {
      await session.abortTransaction();
      console.error('An error occurred during the update process:', error);
    } finally {
      session.endSession();
    }
    console.log('Total Alert Registries Updated:', updatedAlertRegistries);
    console.log('Total Merchant Profiles Created:', createdMerchantProfiles);
  }
}

const script = new Script();
script.main();

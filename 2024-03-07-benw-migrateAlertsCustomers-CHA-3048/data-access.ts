import { MongoClient, ObjectId } from 'mongodb';
import { DataAccess, DataAccessUnitOfWork } from '@chargeflow-team/data-access';

export class ScriptDataAccess {
  public constructor(private mongoClient: MongoClient) {
  }
  private dataAccess: DataAccessUnitOfWork | undefined;
  public async getDataAccess(): Promise<DataAccessUnitOfWork> {
    if (!this.dataAccess) {
      this.dataAccess = await DataAccess.initialize(this.mongoClient);
    }
  
    return this.dataAccess;
  }
  
  public async findAccountIdByChargeflowId (
    chargeflowId: string
  ): Promise<string | null> {
    const dataAccess = await this.getDataAccess();
  
    const chargeflowIdAsObjectId = new ObjectId(chargeflowId);
  
    try {
      const account =
        await dataAccess.accountRepository.findOneByLegacyChargeflowId(
          chargeflowIdAsObjectId
        );
      if (account) {
        return account._id;
      }
      return null;
    } catch (err) {
      throw new Error(
        `Failed to find accountId for chargeflowId: ${chargeflowId}: ${String(
          err
        )}`
      );
    }
  };
  
}


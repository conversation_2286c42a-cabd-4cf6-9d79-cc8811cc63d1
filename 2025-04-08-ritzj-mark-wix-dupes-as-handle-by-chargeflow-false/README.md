# Purpose

Goal of this script is to delete duplicate WIX disputes. This has been pre-analzyed with our analysts via the Mongo view `disputes-stripe-us-comparison`.

See notes https://linear.app/chargeflow/issue/CHA-14273/deactivate-wixs-duplicate-stripe-processor-for-wix-premium-us


## Running this script

`npx ts-node ./script.ts <dry | wet-primary | wet-secondary>`

## Modes

- dry - only logs the number of duplicates, checks if we marked the disputes as duplicates
- wet-secondary - marks the disputes as isDuplicate = true. This is used to easily download a backup of the data.
- wet-primary - deletes the disputes from disputes collection
## Required env variables

MONGO_URI=<mongo_uri_string>

import { ObjectId } from "mongodb";
import {loadMongoClient} from "../shared/mongo-config";
import {ScriptBase} from "../shared/script-base";
export class UpdateAdyenDisputesStatuses extends ScriptBase {

    public getName(): string {
        return "Emit processor deactivating event to proceed with deactivation";
    }

    public getAuthor(): string {
        return "Mateusz Smagiel";
    }

    public getTicket(): string {
        return "cha-14210-fix-processors-statuses-for-which-deactivation-request-stuck";
    }

    async dryRun(): Promise<void> {
        await this.logic();
    }

    async wetRunOnPrimary(): Promise<void> {
        await this.logic();
    }

    async wetRunOnSecondary(): Promise<void> {
        await this.logic();
    }

    isEnvVarsOkOrThrow(keys: string[]) {
        const requiredEnvVars = keys;
        if (!requiredEnvVars.every((envVar) => process.env[envVar])) {
          throw new Error(
            `Missing required environment variables: ${requiredEnvVars.join(", ")}`
          );
        }
    }

    async logic() {
        const client = await loadMongoClient();

        this.isEnvVarsOkOrThrow([
            'MONGO_URI',
            'MONGO_DB_NAME',
        ]);

        const CHARGEFLOW_ID = '67b72c33719d262c00af6992';
        console.log('WIX CHARGEFLOW_ID', CHARGEFLOW_ID);

        const DISPUTES = client.db('chargeflow_api').collection('disputes');
        const COMPARISON_TABLE = client.db('chargeflow_api').collection('disputes-stripe-us-comparison');

        console.log('Counting duplicates...');

        const DUPE_COUNT: number = await COMPARISON_TABLE.countDocuments({
            isDuplicate: true,
        });

        console.log('DUPE_COUNT', DUPE_COUNT);

        if (!DUPE_COUNT) {
            console.log('No duplicates found');
            return;
        }

        console.log('getting mongo ids from comparison table')

        const DUPE_DISPUTE_MONGO_IDS: string[] = (await COMPARISON_TABLE.find({
            isDuplicate: true,
        }, { projection: { _id: 1 } }).toArray()).map((d) => d._id.toString());

        console.log('DUPE_DISPUTE_MONGO_IDS total:', DUPE_DISPUTE_MONGO_IDS.length);
        console.log('DUPE_DISPUTE_MONGO_IDS sample: %j', DUPE_DISPUTE_MONGO_IDS.slice(0, 10));

        const UNMARKED_DUPLICATES = await DISPUTES.countDocuments({
            _id: {
                $in: DUPE_DISPUTE_MONGO_IDS.map((id) => new ObjectId(id)),
            },
            chargeflowId: new ObjectId(CHARGEFLOW_ID),
            isDuplicate: { $ne: true },
        });

        console.log('UNMARKED_DUPLICATES', UNMARKED_DUPLICATES);

        if (this.mode === 'dry') {
            console.log('Dry run ended. No changes made.');
            return;
        }

        if (this.mode === 'wet-secondary') {
            console.log('Running wet-secondary. Marking disputes as isDuplicate: true');

            if (!UNMARKED_DUPLICATES) {
                console.log('No unmarked duplicates found');
                return;
            }

            const filter = {
                _id: {
                    $in: DUPE_DISPUTE_MONGO_IDS.map((id) => new ObjectId(id)),
                },
                $or: [
                    { isDuplicate: { $exists: false }},
                    { isDuplicate: false },
                ],
                chargeflowId: new ObjectId(CHARGEFLOW_ID),
            };

            const result = await DISPUTES.updateMany(filter, {
                $set: {
                    'isDuplicate': true,
                },
            });

            console.log('wet-secondary ended. Marked disputes as isDuplicate: true', result);
            return;
        }

        if (this.mode === 'wet-primary') {
            console.log('Running wet-primary. Deleting duplicates');
            if (UNMARKED_DUPLICATES > 0) {
                console.log('Found unmarked duplicates. Run wet-secondary to mark ');
                return;
            }

            const result = await DISPUTES.deleteMany({
                _id: {
                    $in: DUPE_DISPUTE_MONGO_IDS.map((id) => new ObjectId(id)),
                },
                chargeflowId: new ObjectId(CHARGEFLOW_ID),
                isDuplicate: true,
            });

            console.log('wet-primary ended. Deleted %d duplicates', result.deletedCount);
            return;
        }

    }
}

const script = new UpdateAdyenDisputesStatuses();
script.main();

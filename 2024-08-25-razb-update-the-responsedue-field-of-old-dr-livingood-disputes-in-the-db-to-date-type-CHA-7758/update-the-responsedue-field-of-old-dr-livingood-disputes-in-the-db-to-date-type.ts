import { MongoClient } from 'mongodb';
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import fs from "fs";
import { parse } from "csv-parse";
import dotenv from 'dotenv';

dotenv.config();

class Script extends ScriptBase {
  public getName(): string {
    return "Update dispute responseDue field to Date type";
  }
  public getAuthor(): string {
    return "<PERSON>z Berman"; 
  }
  public getTicket(): string {
    return "CHA-7758"; 
  }

  async dryRun(context: ScriptContext) {
    console.log("Running in dry run (not changing anything)");
    const client = await loadMongoClient();
    const disputesCollection = client.db("chargeflow_api").collection("disputes");
  
    const filePath = process.env.CSV_FILE_NAME;
    const parser = fs.createReadStream(filePath!).pipe(parse({ columns: true }));
    for await (const record of parser) {
      const disputeId = record['Dispute ID'];
      const dispute = await disputesCollection.findOne({ "dispute.id": disputeId });
      if (dispute && !(dispute.dispute.responseDue instanceof Date)) {
        console.log(`Dispute ID: ${disputeId}, responseDue is not of type Date`);
      }
    }
  }

  async wetRunOnPrimary(context: ScriptContext) {
    const client = await loadMongoClient();
    const disputesCollection = client.db("chargeflow_api").collection("disputes");
    const session = client.startSession();
    session.startTransaction();
  
    try {
      const filePath = process.env.CSV_FILE_NAME;
      const parser = fs.createReadStream(filePath!).pipe(parse({ columns: true }));
      for await (const record of parser) {
        const disputeId = record['Dispute ID'];
        const dispute = await disputesCollection.findOne({ "dispute.id": disputeId }, { session });
        if (dispute && !(dispute.dispute.responseDue instanceof Date)) {
          console.log(`Updating dispute with ID: ${disputeId}`);
          const result = await disputesCollection.updateOne(
              { _id: dispute._id },
              { $set: { "dispute.responseDue": new Date(dispute.dispute.responseDue) } },
              { session }
          );
            
          if (result.modifiedCount > 0) {
          console.log(`Dispute with ID ${dispute._id} was updated.`);
          } else {
          console.log(`No changes were made to the dispute with ID ${dispute._id}.`);
          }
        }
      }
      await session.commitTransaction();
    } catch (error) {
      console.error('Error processing disputes, rolling back transaction', error);
      session.abortTransaction();
    } finally {
      session.endSession();
    }
  }

  async wetRunOnSecondary(context: ScriptContext) {
     throw new Error("wetRunOnSecondary is not implemented");
  }
  
}

const script = new Script();
script.main();
{"scripts": {"test": "jest"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.574.0", "@aws-sdk/client-scheduler": "^3.614.0", "@aws-sdk/client-sqs": "^3.525.0", "@rockset/client": "^0.9.1", "@supercharge/promise-pool": "^3.1.1", "@types/lodash": "^4.17.4", "aws4": "^1.12.0", "dotenv": "^16.4.5", "knex": "^3.1.0", "mongodb": "^5.6.0", "ts-node": "^10.9.2", "typescript": "^5.3.3", "csv-parse": "^5.5.6"}, "devDependencies": {"@types/jest": "^29.5.12", "jest": "^29.7.0", "jest-mock-extended": "^3.0.5", "ts-jest": "^29.1.2"}}
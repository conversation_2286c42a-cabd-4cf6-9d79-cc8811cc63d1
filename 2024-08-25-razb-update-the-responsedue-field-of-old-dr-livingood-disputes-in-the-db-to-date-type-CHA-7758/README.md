# Purpose
This script updates the responseDue field for dr.livingood disputes from type string to Date (these are old disputes that were imported months ago, before the addition of the unified csv mapper).

https://linear.app/chargeflow/issue/CHA-7758/update-the-responsedue-field-of-old-dr-livingood-disputes-in-the-db-to

## Motivation
Becuase for these disputes the responseDue field is of type string, the data-unification fails to update the disputes when a csv with their updated data is attempted to be uploaded via the data-import.
Please refer to - https://linear.app/chargeflow/issue/CHA-7657/investigate-error-while-importing-disputes-for-customer-dr-livinghood.

## Running this script
`npx ts-node ./update-the-responsedue-field-of-old-dr-livingood-disputes-in-the-db-to-date-type.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD, and a csv file with the imported disputes should also be present in CWD.
The `.env` file should  contain the following varibles: MONGO_URI and CSV_FILE_NAME
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

### Modes
dry - does not update anything, writes the results to the console
wet-secondary - not implemented
wet-primary - UPDATES THE DISPUTES TABLE. Use with extreme caution!

### How this script was tested
[x] The disputes were imported into Dev DB (using the same csv file used by the analysts in order to import\update the disputes). 
[x] For some of those disputes the responseDue field was changed to be of type string.
[x] The script run on Dev DB and update only the fields of those disputes with a wrong type for responseDue.
# Purpose:

Fetching non Shopify pending charges from MongoDB, checking against a list of pending charges that were mamnually collected, and sending those pending charges not yet collected to the billing system for collection.

# Motivation:

Leverage new billing system for auto collection of yet uncollected charges.

# Running this script

`npx tsx ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with:
**MONGO_URI** 
**AWS_BILLING_ROUTER_QUEUE_URL**: https://sqs.us-east-1.amazonaws.com/781257341267/prod-chargeflow-billing-system-BillingRouterFifoQueue.fifo


### How this script was tested
[x] Dry run against dev
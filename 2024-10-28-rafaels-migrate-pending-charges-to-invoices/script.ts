import dotenv from "dotenv";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, ScriptContext } from "../shared/script-base";
import neatCsv from "neat-csv";
import fs from "fs";
import { AwsSqsUtil } from "@chargeflow-team/chargeflow-utils-sdk";
import { Collection, MongoClient } from "mongodb";

dotenv.config();

let offset = 0;
const PAGE_SIZE = 100;
const WON_STATUS = "won";
const DISPUTE_ITEM = "dispute";
const MANUAL_PROCESS_SOURCE = "manual-process";
enum EventType {
  DISPUTE_SUBMITTED = "DISPUTE_SUBMITTED",
  DISPUTE_UPDATED = "DISPUTE_UPDATED"
}
type EVENT_TYPE = EventType.DISPUTE_SUBMITTED | EventType.DISPUTE_UPDATED;

type CollectedCaseIdItem = {
  caseId: string,
};

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export class ScriptMigratePendingChargesToInvoices extends ScriptBase {
  public getName(): string {
    return "Migrate Pending Charges to Invoices";
  }

  public getAuthor(): string {
    return "Rafael Schuster";
  }

  public getTicket(): string {
    return "CHA-9687";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  sendEventsForBillingReconciliation = async (disputeId: string, detailType: EVENT_TYPE) => {
    console.log('Sending event %s for dispute %s', detailType, disputeId);
    const eventDetail = {
      disputeId,
      update: {
        status: WON_STATUS
      }
    };

    const event = {
      "detail-type": detailType,
      source: DISPUTE_ITEM,
      detail: eventDetail,
      eventSource: MANUAL_PROCESS_SOURCE,
      productType: DISPUTE_ITEM,
      eventType: detailType,
      eventDetail: JSON.stringify(eventDetail)
    };

    const messageBody = JSON.stringify(event);

    const params = {
      QueueUrl: process.env.AWS_BILLING_ROUTER_QUEUE_URL as string,
      MessageBody: messageBody,
      MessageGroupId: "default",
      MessageAttributes: {
        SentTimestamp: {
          DataType: "String",
          StringValue: new Date().toISOString()
        }
      }
    };
    await AwsSqsUtil.send(params);
  }

  handleSendEventsForBillingReconciliation = async (disputeId: string) => {
    await this.sendEventsForBillingReconciliation(disputeId, EventType.DISPUTE_SUBMITTED);
    await this.sendEventsForBillingReconciliation(disputeId, EventType.DISPUTE_UPDATED);
  }

  async fetchPendingCharges(offset: number, collection: Collection): Promise<any[]> {
    const pendingCharges = await collection.aggregate(
      [
        {
          $match: {
            manuallySentForBilling: { $ne: true }
          }
        },
        {
          $lookup: {
            from: "shops",
            localField: "chargeflowId",
            foreignField: "chargeflow_id",
            as: "shop",
            pipeline: [
              {
                $project: {
                  _id: 0,
                  platform: 1
                }
              },
              {
                $match: {
                  platform: { $ne: 'shopify' }
                }
              }
            ]
          }
        },
        {
          $match: {
            'shop.0': { $exists: true }
          }
        },
        {
          $project: {
            _id: 1,
            caseId: 1,
            disputeId: 1,
          },
        },
        {
          $skip: offset
        },
        {
          $limit: PAGE_SIZE
        }
      ]
    ).toArray();

    return pendingCharges;
  }

  async flagPendingChargeAsManuallySentForBilling(caseId: string, collection: Collection): Promise<any> {
    console.log('Flagging pending charge as manually sent for billing. caseId: %s', caseId);
    const result = await collection.updateOne({
      caseId,
    }, {
      $set: {
        manuallySentForBilling: true
      }
    })

    return result;
  }


  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    
    let sentToBillingCount = 0;
    let pendingChargesToProcessCount = 0;
    
    console.log('Creating connection to MongoDB...');
    const client: MongoClient = await loadMongoClient();
    const collection = client
      .db("chargeflow_api")
      .collection("pendingCharges");

    try {
      const disputesCollectedCsv = fs.readFileSync("case-ids-collected.csv");
      const parsedCaseIdsCollectedData: CollectedCaseIdItem[] = await neatCsv<CollectedCaseIdItem>(
        disputesCollectedCsv
      );
      console.log('Received list of %s collected disputes', parsedCaseIdsCollectedData.length);
      
      let hasMore = true;
      while (hasMore) {
        console.log('Fetching page of pending charges from MongoDB with offset: %s', offset);
        const pendingCharges = await this.fetchPendingCharges(offset, collection);
        if (pendingCharges.length === 0) {
          console.warn('No pending charges found');
          hasMore = false;
          return;
        }
        
        const pendingChargesNotOnTheManuallyCollectedList = pendingCharges.filter(pendingCharge => {
          return !parsedCaseIdsCollectedData.find((d: CollectedCaseIdItem) => d.caseId === pendingCharge.caseId);
        });
        pendingChargesToProcessCount += pendingChargesNotOnTheManuallyCollectedList.length;

        if (!isDryRun) {
          console.log('Wet run mode. Processing %s pending charges to send events to billing system',
            pendingChargesNotOnTheManuallyCollectedList.length
          );
          for (const pendingChargeNotCollected of pendingChargesNotOnTheManuallyCollectedList) {
            try {
              await this.handleSendEventsForBillingReconciliation(pendingChargeNotCollected.disputeId);
              await this.flagPendingChargeAsManuallySentForBilling(pendingChargeNotCollected.caseId, collection)
              sentToBillingCount++;
            } catch (error) {
              console.error(`Error sending event to billing reconciliation. error: ${JSON.stringify(error)}`);
            }
          }
      } else {
          console.log('Dry run mode. Would have processed %s pending charges to send events to billing system',
            pendingChargesToProcessCount
          );
      }
        offset += PAGE_SIZE;
        hasMore = pendingCharges.length === PAGE_SIZE;
        console.log('Has more pending charges: %s', hasMore);

        await delay(2000);
      } 

      if (!isDryRun) { 
        console.log(
          `${new Date().toISOString()}: Processed pending charges. Total sent to billing collection: ${sentToBillingCount}`);
      } else {
        console.log(`${new Date().toISOString()}: Would have sent ${pendingChargesToProcessCount} cases to billing collection`);
      } 

    } catch (error) {
      console.error(`${new Date().toISOString()}: Error processing pending charges. error: ${JSON.stringify(error)}`);
      console.log('Closing MongoDB connection');
      await client.close();
      process.exit(1);

    } finally {
      console.log('Successfully processed pending charges');
      console.log('Closing MongoDB connection');
      await client.close();
    }
  }
}

const script = new ScriptMigratePendingChargesToInvoices();
script.main();
# Purpose
This script assigns processorId to existing disputes.

https://linear.app/chargeflow/issue/CHA-4866/prepare-migration-script-to-add-to-existing-disputes-processorid

## Motivation
Every dispute should have processor id. Which indicates processor from which it comes.

## Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

1. Set the following environment variables in CWD:
```
MONGO_URI=<db connection string>
BATCH_SIZE=<batch size>
PAGE=<on which migration stops if not provided starting from page 1>
PROCESSOR_TYPE=<processor type e.g. paypal, klarna etc...>
```

2. Install deps from CWD: `npm i`

3. Run the `dry` mode first. See the number of projected items to migrate and adjust the `BATCH_SIZE` env var.

4. Run the migration in `wet-primary` mode once everything is prepared.


### Modes
dry - does not update anything, writes the results to the console
wet-secondary - not implemented
wet-primary - executes the actual migration

### How this script was tested
- [x] test migration against `dev` db
- [x] test migration against `test` db

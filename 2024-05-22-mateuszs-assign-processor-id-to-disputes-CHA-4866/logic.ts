import {MongoClient, Db, ObjectId, AnyBulkWriteOperation, Document, UpdateFilter} from 'mongodb';

export interface Logger {
    log: (message: string) => void;
}

export class AssignProcessorsToShops {
    private readonly DB_NAME = 'chargeflow_api';
    private readonly DISPUTES_COLLECTION_NAME = 'disputes';
    private readonly PROCESSORS_COLLECTION_NAME = 'processors';

    private db: Db

    constructor(
        mongoClient: MongoClient,
        private readonly logger: Logger,
    ) {
        this.db = mongoClient.db(this.DB_NAME);
    }

    async execute(batchSize: number, page: number, processorType: string, dryRun: boolean): Promise<void> {
        try {
            this.logger.log('Setting processor id for disputes started, please wait ...');

            const disputesItemFilter = {
                $and: [
                    { dispute: { $exists: true } },
                    { 'dispute.processorId': { $exists: false } },
                    { 'dispute.processor': { $eq: processorType } },
                ]};
            const totalItems = await this.db.collection(this.DISPUTES_COLLECTION_NAME).countDocuments(disputesItemFilter);
            const totalBatches = Math.ceil(totalItems / batchSize);

            this.logger.log(`Total documents to process: ${totalItems}`);
            this.logger.log(`Total batches to process: ${totalBatches}`);

            if (dryRun) {
                this.logger.log('Dry run mode enabled, no changes made to the database');
                return;
            }
            for (let currentBatch = page; currentBatch <= totalBatches; currentBatch++) {
                try {
                    this.logger.log(`Processing batch ${currentBatch} of ${totalBatches}`);
                    const currentPage = (currentBatch - 1) * batchSize;
                    const disputes = await this.db.collection(this.DISPUTES_COLLECTION_NAME)
                        .find(disputesItemFilter)
                        .skip(currentPage)
                        .limit(batchSize)
                        .toArray();
                    const chargeflowIds = disputes.map(value => new ObjectId(value.chargeflowId));
                    const processorsItemFilter = {
                        'chargeflow_id': { $in: chargeflowIds }};
                    let processors = await this.db.collection(this.PROCESSORS_COLLECTION_NAME)
                        .find(processorsItemFilter)
                        .toArray();
                    const processorsByChargeflowId = processors.reduce((map, processor) => {
                        if (!map.has(processor.chargeflow_id.toString())) {
                            map.set(processor.chargeflow_id.toString(), []);
                        }
                        map.get(processor.chargeflow_id.toString())!.push(processor);
                        return map;
                    }, new Map<string, any[]>());
                    this.logger.log(`ChargeflowIds: ${chargeflowIds}`);
                    this.logger.log(`Processors size: ${processorsByChargeflowId.size}`);

                    const bulkOperations = disputes.map(dispute => {
                        this.logger.log(`Trying to find processor by: ${dispute.chargeflowId}`);
                        const matchingProcessors = processorsByChargeflowId.get(dispute.chargeflowId?.toString()) || [];
                        this.logger.log(`Matching processors size: ${matchingProcessors.length}`);
                        const matchingProcessorsByType: Map<string,any> = matchingProcessors.reduce((map, processor) => {
                            map.set(processor.processor_name, processor);
                            return map;
                        }, new Map<string, any>());
                        const processor = matchingProcessorsByType.get(dispute.dispute.processor);
                        return {
                            updateOne: {
                                filter: { _id: new ObjectId(dispute._id) },
                                update: {
                                    $set: {
                                        'dispute.processorId': processor?._id.toString()
                                    }
                                },
                                upsert: false
                            }
                        };
                    });

                    if (bulkOperations.length > 0) {
                        const result = await this.db.collection(this.DISPUTES_COLLECTION_NAME).bulkWrite(bulkOperations);
                        this.logger.log(`Updated ${result.modifiedCount} disputes with processorIds.`);
                    } else {
                        this.logger.log('No disputes to update.');
                    }
                    this.logger.log(`Batch ${currentBatch} of ${totalBatches} processed successfully`);
                } catch (error: unknown) {
                    if (error instanceof Error) {
                        this.logger.log(`Batch ${currentBatch} of ${totalBatches} failed, reason: ${error.message}`);
                    }
                    throw error;
                }
            }
            this.logger.log('Setting processor id for disputes finished successfully');
        } catch (error) {
            if (error instanceof Error) {
                this.logger.log(`Setting processor id for disputes failed, reason: ${error.message}`);
            }
            throw error;
        }
    }
}

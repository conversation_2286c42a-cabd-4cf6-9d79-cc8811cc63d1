export interface PaypalConnectionData {
  scope: string;
  access_token: string;
  token_type: string;
  expires_in: string;
  refresh_token: string;
  nonce: string;
}
export class PaypalClient {
  constructor(
    private connect: PaypalConnectionData,
    private paypalBaseUrl: string,
    private paypalClient: string,
    private paypalSecret: string
  ) {}

  public async getAllDisputes(): Promise<string[]> {
    const token = await this.getToken();
    if (!token || token.error) {
        console.error("Failed to get PayPal token", token);
        return [];
    }
    const dt = new Date(new Date().valueOf() - 120 * 24 * 60 * 60 * 1000); //120 days back
    const disputeIds: string[] = [];
    let disputesPageUrl: string | undefined = `/customer/disputes?start_time=${dt.toISOString()}&page_size=50`;

    let pageNumber = 0;
    while (disputesPageUrl) {
      pageNumber++;
      console.log(`Getting disputes from ${disputesPageUrl} (page ${pageNumber})`);
      const res = await this.fetchPayPal(disputesPageUrl, token);
      if (!res.ok) {
        const body = await res.text();
        throw new Error(
          "Failed to get dispute ids from PayPal: " +
            `${res.status} ${disputesPageUrl} ${body}`
        );
      }
      const disputesIdsPage: IPaypalDisputesResponse = await res.json();
      if (disputesIdsPage?.items) {
        disputesIdsPage.items.forEach(
          (dispute) => {
            disputeIds.push(dispute.dispute_id);
          }
        );
      }

      disputesPageUrl = disputesIdsPage.links.find(
        (link: { rel: string }) => link.rel === "next"
      )?.href;
    }

    return disputeIds;
  }

  private async getToken(): Promise<IPaypalTokenResponse> {
    const tokenAuth = Buffer.from(
      `${this.paypalClient}:${this.paypalSecret}`
    ).toString("base64");

    const baseUrl = this.paypalBaseUrl;//
    const authUrl = `${baseUrl}/identity/openidconnect/tokenservice`;
    const authOptions = {
      method: "POST",
      headers: {
        Authorization: `Basic ${tokenAuth}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: `grant_type=refresh_token&refresh_token=${this.connect.refresh_token}`,
    };

    const accessToken: IPaypalTokenResponse = await fetch(
      authUrl,
      authOptions
    ).then((res) => res.json());
    return accessToken;
  }

  private async fetchPayPal(urlPath: string, accessToken: IPaypalTokenResponse) {
    const options = {
      headers: {
        Authorization: `${accessToken.token_type} ${accessToken.access_token}`,
        "Content-Type": "application/json",
      },
    };

    const url = urlPath.includes(this.paypalBaseUrl) ? urlPath : this.paypalBaseUrl + urlPath;
    const res = await fetch(url, options);
    return res;
  }
}

interface IPaypalTokenResponse {
  access_token: string;
  token_type: string;
  error?: string;
}

interface IPaypalDisputesResponse {
    items: {
        dispute_id: string;
        create_time: string; //ISO 8601 timestamp
        dispute_amount: {currency_code: string; value: string},
        dispute_channel: string;
        dispute_life_cycle_stage: string;
        dispute_state: string;
        outcome: string;
        reason: string;
        status: string;
        update_time: string; //ISO 8601 timestamp
    }[];
    links: {
        rel: string;
        href: string;
    }[];
}
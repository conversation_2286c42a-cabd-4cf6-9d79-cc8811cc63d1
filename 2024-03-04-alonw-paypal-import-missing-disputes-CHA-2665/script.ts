import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { MongoClient, ObjectId } from "mongodb";
import dotenv from "dotenv";
import { PaypalClient, PaypalConnectionData } from "./paypal-client";
import { SQS } from "@aws-sdk/client-sqs";
dotenv.config();
export class Script extends ScriptBase {
  public getName(): string {
    return "Import missing paypal disputes";
  }
  public getAuthor(): string {
    return "Alon Weiss";
  }
  public getTicket(): string {
    return "CHA-2665";
  }
  dryRun(context: ScriptContext): Promise<void> {
    return this.logic(context, false);
  }
  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }
  wetRunOnPrimary(context: ScriptContext): Promise<void> {
    return this.logic(context, true);
  }

  async logic(ctx: ScriptContext, sendMessages: boolean = true) {
    console.log("Running in dry run (not changing anything)", ctx);
    const client = await loadMongoClient();
    const shops = await this.getShopsAndProcessors(client);
    console.dir(shops, { depth: null });
    let shopsWithMissingDisputesCount = 0;
    let missingDisputeCount = 0;
    for (const shop of shops) {
      try {
        console.log(
          `Processing shop: ${shop.shopName}, chargeflow_id: ${shop.chargeflowId}`
        );
        const missingDisputes = await this.getMissingDisputes(client, shop);
        console.log(
          `Shop: ${shop.shopName}, Missing disputes: ${missingDisputes.length}`
        );
        if (missingDisputes.length > 0) {
          console.log(`Missing disputes: ${missingDisputes}`);
          shopsWithMissingDisputesCount++;
          missingDisputeCount += missingDisputes.length;
          if (sendMessages) {
            await this.sendMessageToProcessDisputes(shop, missingDisputes);
          }
        }
      } catch (err) {
        console.error(`Failed to process shop ${shop.shopName}`, err);
      }
    }
    console.log(
      `Shops with missing disputes: ${shopsWithMissingDisputesCount}, Missing disputes: ${missingDisputeCount}`
    );
  }
  private async sendMessageToProcessDisputes(
    shop: PaypaylShopAndProcessor,
    missingDisputes: string[]
  ): Promise<void> {
    const params = {
      MessageAttributes: {
        shopName: {
          DataType: "String",
          StringValue: shop.shopName,
        },
      },
      MessageBody: JSON.stringify(missingDisputes),
      QueueUrl: process.env.AWS_SQS_DISPUTE_IDS_URL,
    };

    try {
      const sqs = new SQS();
      const res = await sqs.sendMessage(params);
    } catch (err) {
      console.error(`Failed to send messages for shop ${shop.shopName}`, err);
    }
  }
  private async getMissingDisputes(
    client: MongoClient,
    shop: PaypaylShopAndProcessor
  ): Promise<string[]> {
    if (!process.env.PAYPAL_CLIENT)
      throw new Error("Missing PAYPAL_CLIENT in env");
    if (!process.env.PAYPAL_SECRET)
      throw new Error("Missing PAYPAL_SECRET in env");
    if (!process.env.PAYPAL_BASE_URL)
      throw new Error("Missing PAYPAL_BASE_URL in env");
    const paypalClient = new PaypalClient(
      shop.connect,
      process.env.PAYPAL_BASE_URL,
      process.env.PAYPAL_CLIENT,
      process.env.PAYPAL_SECRET
    );
    const allCaseIds = await paypalClient.getAllDisputes();
    if (!allCaseIds.length) {
      return [];
    }

    const existingDisputes = await client
      .db("chargeflow_api")
      .collection("disputes")
      .find({
        chargeflowId: shop.chargeflowId,
      })
      .project({ "dispute.id": 1 })
      .toArray();

    const existingCaseIdLookup: Map<string, boolean> = new Map();
    for (const dispute of existingDisputes) {
      existingCaseIdLookup.set(dispute.dispute.id, true);
    }

    const missingDisputeIds = allCaseIds.filter((caseId) => {
      return !existingCaseIdLookup.has(caseId);
    });

    return missingDisputeIds;
  }

  private async getShopsAndProcessors(client: MongoClient) {
    // shopCfStatus: res?.chargeflow_status,
    // chargeflowId: res?.chargeflow_id,
    const allShopsAndProcessors = await client
      .db("chargeflow_api")
      .collection("processors")
      .aggregate([
        {
          $match: {
            processor_name: "paypal",
          },
        },
        {
          $lookup: {
            from: "shops",
            localField: "chargeflow_id",
            foreignField: "chargeflow_id",
            as: "shop",
          },
        },
        {
          $unwind: {
            path: "$shop",
          },
        },
      ])
      .toArray();
    return allShopsAndProcessors.map((doc) => {
      return {
        shopName: doc.shop.name,
        processorId: doc._id,
        connect: doc.connect,
        chargeflowId2: doc.shop.chargeflow_id,
        chargeflowId: doc.chargeflow_id,
        shopCfStatus: doc.shop.chargeflow_status,
      };
    });
  }
}

const script = new Script();
script.main();

interface PaypaylShopAndProcessor {
  shopName: string;
  processorId: ObjectId;
  connect: PaypalConnectionData;
  chargeflowId: ObjectId;
  shopCfStatus: string;
}

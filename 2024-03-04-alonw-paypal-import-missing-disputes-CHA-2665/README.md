# Purpose
This script finds disputes that exist in Paypal but do not exist in our database. It then sends an SQS message to PayPalDisputeIdsQueue to process those disputes

https://linear.app/chargeflow/issue/CHA-2665/unable-to-locate-paypal-dispute-in-chargeflow-app

## Motivation
Paypal's new dispute reconciliation is designed to use a "pointer" to optimize the search process. Apparently it misses some disputes, which puts us at risk as we're liable for (not) handling them.

## Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD
When running, make sure to store the stdout/stderr in a text file (this is what `tee log.txt` does)

The following env variables are required:

MONGO_URI - Mongo connection string (read replica is OK. This script does not write anything to the DB)
PAYPAL_BASE_URL - https://api-m.paypal.com/v1
PAYPAL_CLIENT - prod-paypal-client-id from SSM
PAYPAL_SECRET - prod-paypal-api-key from SSM
AWS_SQS_DISPUTE_IDS_URL=<queue url of prod-paypal-shopify-link-PayPalDisputeIdsQueue>

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_SESSION_TOKEN=

### Modes
dry - does not update anything, writes the results to the console
wet-secondary - not implemented
wet-primary - Sends SQS messages to the specified [production] queue

### How this script was tested
[x] Dry run with production customers (SQS message not sent)
[x] Sending one message to production 
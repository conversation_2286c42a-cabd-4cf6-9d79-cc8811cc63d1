# Purpose:

To backfill the processors collection (idempotently) with the shopify payments processor for all shops that have the shopify payments processor enabled.

# Motivation:

We want to start treating shopify payments as a "processor" entity vs a "shop" entity.

# Desired state
- All shopify shops should have an equivalent processor in the processors collection.

# Running this script

`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with

```
MONGO_URI='MONGO_URI'
MONGO_DB_NAME='MONGO_DB_NAME'
```

### How this script was tested
[x] Wet run against dev & prod with data diff validation afterwards
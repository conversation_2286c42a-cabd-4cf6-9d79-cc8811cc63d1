import dotenv from "dotenv";
import { <PERSON>riptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { isEnvVarsOkOrThrow } from "../shared/common";
import { IShop } from '@chargeflow-team/data-access';
import { Source } from "@chargeflow-team/data-access/dist/shared/enums";
import { Db, ObjectId } from "mongodb";
import { ICreateProcessor, IMongoProcessor, IProcessorStatus, IShopifyPaymentsConnectData, ProcessorStatusEnum } from "@chargeflow-team/data-access/dist/Processor/Types";

dotenv.config();

export class Script extends ScriptBase {
  // declared here for easy testing in a ghost collection
  processorCollectionName = "processors-with-shopify-test-v1";
  public getName(): string {
    return "Backfill processors collection with shopify payments from shops collection";
  }

  public getAuthor(): string {
    return "Ritz Jumola";
  }

  public getTicket(): string {
    return "CHA-8855";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    console.log("Running in dry run mode (not changing anything)");
    await this.logic(context, true);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  db: Db | undefined;

  private async getDb() {
    if (!this.db) {
      const mongo = await loadMongoClient();
      console.log("Mongo client loaded");
      this.db = mongo.db(process.env.MONGO_DB_NAME);
    }
    return this.db;
  }

  private async logic(ctx: ScriptContext, isDryRun: boolean) {
    try {
      isEnvVarsOkOrThrow([
        "MONGO_URI",
        "MONGO_DB_NAME",
      ]);

      const DB = await this.getDb();
      const SHOPS = DB.collection("shops");

      // aggregate query that returns a list of shops
      // that does not have a corresponding processor document
      // shop and processor is related by
      // shop.chargeflow_id === processor.chargeflow_id

      const pipeline = [
        {
          $match: {
            platform: 'shopify',
            chargeflow_status: { $nin: ['disabled', 'uninstalled'] },
          },
        },
        {
          $lookup: {
            from: this.processorCollectionName,
            let: { chargeflow_id: "$chargeflow_id" },
            pipeline: [
              {
                $match: {
                  processor_name: 'shopify',
                  $expr: {
                    $eq: ["$chargeflow_id", "$$chargeflow_id"],
                  },
                },
              },
            ],
            as: "processor",
          },
        },
        {
          $unwind: {
            path: "$processor",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            "processor._id": { $exists: false },
          },
        },
      ];

      const countPipeline = [
        ...pipeline,
        {
          $count: "count",
        },
      ];

      const countRes = await SHOPS.aggregate(countPipeline).toArray();
      console.log(`Active Shopify Shops without processor document:`, countRes);

      // now for each, create a processor document
      const limit = 1000;
      const total = countRes[0]?.count || 0;
      let hasMore = true;
      let totalInserted = 0;

      if (total === 0) {
        console.log("No shops to process");
        return;
      }

      console.log(`About to process ${total} shops`);

      while (hasMore) {
        const shops = await SHOPS.aggregate([
          ...pipeline,
          {
            $limit: limit,
          },
        ]).toArray();

        console.log(`Processing ${shops.length} shops`);

        if (shops.length === 0) {
          console.log("No more shops to process");
          break;
        }

        const requests: ICreateProcessor<IShopifyPaymentsConnectData>[]
          = shops.map(shop => this.shopifyShopToICreateProcessorMapper({
            _id: shop._id,
            chargeflow_id: shop.chargeflow_id,
            accountId: shop.accountId,
            shopify_payments_connected: shop.shopify_payments_connected,
            email: shop.email,
            connect: shop.connect,
            myshopify_domain: shop.myshopify_domain,
          }));

        const iMongoProcessors: IMongoProcessor[]
          = requests.map(this.createShopifyProcessorToMongoProcessor);

        totalInserted += (await this.runWriteOps(iMongoProcessors)).totalInserted;
        console.log('totalInserted', totalInserted);

        hasMore = totalInserted < total;
        console.log('hasMore', hasMore);
      }

      if (this.mode === 'dry') {
        console.log("Dry run completed. Would've inserted", total, "documents");
        return;
      } else {
        console.log(`Total inserted: ${totalInserted}`);
      }

    } catch (err) {
      console.log(err);
      process.exit(1);
    } finally {
      console.log("task completed");
      process.exit(0);
    }
  }

  async runWriteOps(newProcessors: IMongoProcessor[]): Promise<{ totalInserted: number}> {
    if (this.mode !== 'wet-primary') {
      console.log("Not in wet-primary mode. Skipping write ops", 'Current mode:', this.mode);
      return { totalInserted: 0 };
    }

    const DB = await this.getDb();
    const PROCESSORS = DB.collection(this.processorCollectionName);

    const res = await PROCESSORS.insertMany(newProcessors);

    return { totalInserted: res.insertedCount };
  }

  shopifyShopToICreateProcessorMapper(
    shop: Pick<IShop, '_id' | 'chargeflow_id' | 'accountId' | 'shopify_payments_connected' | 'email' | 'connect' | 'myshopify_domain'>,
  ): ICreateProcessor<IShopifyPaymentsConnectData> {
    if (!shop.chargeflow_id) {
      throw new Error(`Missing chargeflow_id for shop ${shop._id}`);
    }

    if (!shop.accountId) {
      throw new Error(`Missing accountId for shop ${shop._id}`);
    }

    try {
      return {
        'processor_name': Source.ShopifyPayments,
        'chargeflow_id': shop.chargeflow_id,
        'accountId': shop.accountId,
        isKeyValid: shop.shopify_payments_connected,
        createdByEmail: shop.email,
        connect: {
            accessToken: shop.connect.access_token,
            scope: shop.connect.scope,
            shopName: shop.connect.shop_name,
            domain: shop.myshopify_domain,
        },
      }
    } catch (err) {
      console.log('Error mapping shop to ICreateProcessor', shop, err);
      throw err;
    }
  }

  createShopifyProcessorToMongoProcessor(request: ICreateProcessor<IShopifyPaymentsConnectData>): IMongoProcessor {
    const newStatus: IProcessorStatus = {
      status: ProcessorStatusEnum.Active,
      actor: request.createdByEmail,
      dateCreated: new Date(),
    };

    return {
        ...request,
        date_created: new Date(),
        date_updated: new Date(),
        status: newStatus,
        statusHistory: [ newStatus ],
        chargeflow_id: new ObjectId(request.chargeflow_id),
        accountId: new ObjectId(request.accountId),
    };
  }
}

const script = new Script();
script.main();

# Purpose
Some customers requested to update their refund thresholds. This script does that by matching via chargeflowId.
https://linear.app/chargeflow/issue/CHA-11834/write-a-script-for-update-clients-with-refund-threshold

# Motivation
We need to change refund threshold for specific customers

# Running this script
`npx ts-node ./script.ts <dry | wet-primary>`

# Required env variables
MONGO_URI=<mongo_uri_string> (`alertRegistry` collection is in use)
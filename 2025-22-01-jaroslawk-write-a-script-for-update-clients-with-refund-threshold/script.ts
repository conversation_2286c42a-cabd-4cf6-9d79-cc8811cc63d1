import { MongoClient, ObjectId } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase } from "../shared/script-base";
import chargeflowIds from './devChargeflowIds.json';

export class RefundThresholdSettingScript extends ScriptBase {
  public getName() {
    return "Refund Threshold Setting Script";
  }

  public getAuthor() {
    return "Jaroslaw Kyc";
  }

  public getTicket(): string {
    return "CHA-11834/write-a-script-for-update-clients-with-refund-threshold";
  }

  public async dryRun(): Promise<void> {
    await this.handle(true);
  }

  public async wetRunOnPrimary(): Promise<void> {
    await this.handle(false);
  }

  public async wetRunOnSecondary(): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private async handle(isDryRun: boolean) {
    if (!process.env.MONGO_URI) {
      throw new Error("MONGO_URI is not set");
    }
    const client: MongoClient = await loadMongoClient();
    const db = client.db("chargeflow_api");
    const alertsRegistryCollection = db.collection("alertsRegistry");

    const parsedData = chargeflowIds.map((data) => ({
      chargeflowId: new ObjectId(data.chargeflowId),
      threshold: data.threshold,
    }));

    if (isDryRun) {
      const documents = await alertsRegistryCollection.find({ chargeflowId: { $in: parsedData.map(d => d.chargeflowId) } }).toArray();
      console.log(`Dry Run: Found ${documents.length} documents to update.`);
      documents.forEach(doc => {
        const data = parsedData.find(d => d.chargeflowId.equals(doc.chargeflowId));
        console.log(`Would update document with _id: ${doc._id}, setting refundThreshold to ${data?.threshold} and pushing to history.`);
      });
    } else {
      const bulkOps = parsedData.map(data => {
        const dateOfUpdate = new Date();
        console.log(`Trying to update document with chargeflowId: ${data.chargeflowId} with refundThreshold: ${data.threshold}`);
        return {
          updateOne: {
            filter: { chargeflowId: data.chargeflowId, 'refundSettings.threshold': { $exists: false } },
            update: [{
              $set: {
                'refundSettings.threshold': data.threshold,
                'refundSettings.thresholdUpdateDate': dateOfUpdate,
                'refundSettings.thresholdCreationDate': dateOfUpdate,
                history: {
                  $concatArrays: [
                    { $ifNull: ["$history", []] },
                    [
                      {
                        date: new Date(),
                        user: "script",
                        field: "refundSettings.threshold",
                        oldValue: null,
                        newValue: data.threshold,
                      },
                    ],
                  ]
                }
              },
            }],
        },
        }
      });

      const res = await alertsRegistryCollection.bulkWrite(bulkOps);
      console.log(`Wet Run: Updated ${res.modifiedCount} documents.`);
    }

    await client.close();
  }
}

const script = new RefundThresholdSettingScript();
script.main();

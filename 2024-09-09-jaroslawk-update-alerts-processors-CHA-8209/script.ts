import { MongoClient, AnyBulkWriteOperation } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase, <PERSON>riptContext, ScriptMode } from "../shared/script-base";

const processorMapper: { [key: string]: string } = {
  "449216": "Stripe",
  "409250": "Stripe",
  "401700": "Stripe",
  "403989": "Stripe",
  "442114": "Maverick",
  "408342": "Stripe",
  "401509": "Stripe",
  "460905": "Stripe",
  "420847": "Stripe",
  "424809": "CMS",
  "479338": "Adyen",
  "411832": "Stripe",
  "423194": "Authorize.net",
  "400077": "Stripe",
  "404945": "Stripe",
  "439802": "PaymentCloud",
  "420133": "Stripe",
  "480387": "Stripe",
  "406402": "Stripe",
  "407344": "Stripe",
  "414572": "EMS/Authorize",
  "448012": "PaymentCloud",
  "424469": "Stripe",
  "429428": "Checkout",
  "435294": "Safecharge",
  "406466": "Stripe",
  "419881": "Stripe",
  "468720": "Authorize.net",
  "448131": "Stripe",
  "403194": "Stripe",
  "448132": "Stripe",
  "401134": "Stripe",
  "400471": "Stripe",
  "493185": "Stripe",
  "422108": "Maverick",
  "403982": "Maverick",
  "494306": "Maverick",
  "498750": "Adyen",
  "400224": "Adyen",
  "487115": "Checkout",
  "402121": "Checkout",
  "481115": "Checkout",
  "492715": "Checkout",
  "429437": "Checkout",
  "411991": "Checkout",
  "438886": "Checkout",
  "454899": "Checkout",
  "426975": "Safecharge",
  "449251": "Luqra",
  "424004": "authorize.net",
  "403599": "Stripe",
  "424537": "Checkout",
  "425761": "MX Merchant",
  "416599": "Stripe",
  "469216": "Authorize.net",
  "271116": "Stripe",
  "271621": "Maverick",
  "230009": "Stripe",
  "264431": "Stripe",
  "538390": "Stripe",
  "230034": "CMS",
  "230202": "Adyen",
  "270100": "Stripe",
  "211755": "Stripe",
  "271006": "PaymentCloud",
  "230230": "Stripe",
  "230263": "Stripe",
  "524786": "EMS/Authorize",
  "271146": "PaymentCloud",
  "270770": "Safecharge",
  "535339": "Stripe",
  "534701": "Authorize.net",
  "269145": "Stripe",
  "513070": "Stripe",
  "230509": "Stripe",
  "521565": "Maverick",
  "270159": "Maverick",
  "230662": "Maverick",
  "526567": "Adyen",
  "518489": "Checkout",
  "270092": "Checkout",
  "270522": "Checkout",
  "271523": "Checkout",
  "270340": "Checkout",
  "543729": "Checkout",
  "271042": "Luqra",
  "528414": "authorize.net",
  "512995": "Stripe",
  "513340": "Checkout",
  "270088": "MX Merchant",
  "230576": "Stripe",
  "543286": "Authorize.net",
  "230102": "Stripe",
  "449215": "Stripe",
  "458570": "Checkout",
  "491480": "Simple Swipe",
  "511205": "Stripe",
  "513008": "Stripe",
  "424835": "Esquire Bank",
  "230146": "Esquire Bank"
};

class UpdateAlertsProcessorScript extends ScriptBase {
  public getName(): string {
    return "Update alerts processor based on acquirer bin and merchant profile";
  }

  public getAuthor(): string {
    return "Jaroslaw Kyc";
  }

  public getTicket(): string {
    return "CHA-8209";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    await this.logic(context);
  }

  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context);
  }

  wetRunOnSecondary(_context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }

  private capitalizeProcessorName(input: any): any {
    if (typeof input !== "string") {
      return input;
    }

    if (input.trim() === "") {
      return "";
    }

    return input
      .trim()
      .split(/[-_\s]+/)
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ")
      .trim();
  }

  private async logic(ctx: ScriptContext) {
    const mode = (ctx.args[0] ?? "dry") as ScriptMode;
    const client: MongoClient = await loadMongoClient();
    const db = client.db("chargeflow_api");
    const alertsCollection = db.collection("alerts");
    const merchantProfilesCollection = db.collection("merchantProfiles");

    const alertsWithoutProcessor = await alertsCollection
      .find({ processor: null })
      .toArray();

    console.log("Total alerts to process: %s", alertsWithoutProcessor.length);

    const updateOps: AnyBulkWriteOperation<Document>[] = [];

    await Promise.all(
      alertsWithoutProcessor.map(async (alert) => {
        const merchantProfiles = await merchantProfilesCollection
          .find({ merchantId: alert.merchantId })
          .toArray();

        let processor: string | null = null;

        if (alert.acquirer_bin) {
          processor = this.capitalizeProcessorName(
            processorMapper[alert.acquirer_bin]
          );
        }

        if (!processor && merchantProfiles.length === 1) {
          processor = merchantProfiles[0]?.processor;
        }

        if (processor) {
          updateOps.push({
            updateOne: {
              filter: { _id: alert._id },
              update: { $set: { processor } },
            },
          });
        }
      })
    );

    if (mode === "dry") {
      console.log("Dry run mode");
      console.log("Updating %s alerts: ", updateOps.length);
    } else {
      const updatedCount = await alertsCollection.bulkWrite(updateOps as any, {
        ordered: false,
      });
      console.log("Updated %s alerts", updatedCount.modifiedCount);
    }

    console.log("Total updated alerts: %s", updateOps.length);

    client.close();
  }
}

const script = new UpdateAlertsProcessorScript();
script.main();
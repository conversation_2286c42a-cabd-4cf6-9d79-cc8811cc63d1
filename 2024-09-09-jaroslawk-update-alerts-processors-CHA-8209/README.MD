# Purpose:
Updates alerts without processors, with a processor taken from acquirerBin, or if not present then from merchant profile if user has exact one profile

# Motivation:
Our goal is to decrease this percentage to 0.4% by implementing a fallback strategy using the MerchantProfiles collection.

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary> | tee log.txt`
NOTE: a `.env` file is expected to be present in CWD with **MONGO_URI**

### How this script was tested
[x] Wet run against dev
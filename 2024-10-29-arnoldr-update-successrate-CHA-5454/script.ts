import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Db, ObjectId } from 'mongodb';
import path from 'path';
import fs from 'fs/promises';

dotenv.config();

type JsonData = {
  chargeflowIds: string[];
}
const SUCCESS_RATE = 25;
const INACTIVE_SHOP_STATUSES = [ 'inactive', 'disabled', 'uninstalled' ];

export class DeleteChargesForPaidInvoices extends ScriptBase {
    db: Db | undefined = undefined;

    public getName(): string {
        return 'Update sucess rate of shops';
    }

    public getAuthor(): string {
        return '<PERSON> Ramos';
    }

    public getTicket(): string {
        return 'CHA-5454 and CHA 5455';
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.logic(context, true);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.logic(context, false);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error('Method not implemented.');
    }

    private async getCaseIdsFromJsonFile():Promise<JsonData | undefined> {
      try {
        const filePath = path.join(__dirname, "chargeflowIds.json");
        const data = await fs.readFile(filePath, "utf-8");
        return JSON.parse(data);
      } catch (error) {
        console.error("Error reading case ids from file", error);
        console.error(
          "Make sure the file <chargeflowIds.json> exists and is in the same directory as the script"
        );
        return undefined;
      }
    }

    private async updateSucessRates(filter:any,setFields:any): Promise<number> {
      const mongo = await loadMongoClient();
      const result = await mongo.db('chargeflow_api').collection('shops').updateMany(filter, {
        $set: setFields
      })

      return result.modifiedCount
    }

    private async countInactiveShopsToUpdate(filter:any): Promise<number> {
      const mongo = await loadMongoClient();
      const result = await mongo.db('chargeflow_api').collection('shops').countDocuments(filter);

      return result;
    }
    

    private async logic(context: ScriptContext, isDryRun: boolean) {
      console.log('Is dry run:', isDryRun);
      const jsonData = await this.getCaseIdsFromJsonFile();

      if (!jsonData) {
        console.error("No data found in json file");
        return;
      }

      const {chargeflowIds} = jsonData
      const inactiveShopsToUpdateFilter = {
        chargeflow_status: { $in: INACTIVE_SHOP_STATUSES },
        chargeflow_success_rate: { $lt: 25 }
      }

      const inactiveShopsToUpdateCount = await this.countInactiveShopsToUpdate(inactiveShopsToUpdateFilter);
      console.log(`Found ${inactiveShopsToUpdateCount} inactive shops to update`);
      console.log(`Found ${chargeflowIds.length} records found from the json file`);
      console.log(`Total records to be updated: ${inactiveShopsToUpdateCount + chargeflowIds.length}`);

      if (!isDryRun) {
        console.log(`Updating ${chargeflowIds.length} records from the json file`);
        const targeCustomersUpdateResult = await this.updateSucessRates(
          {
            chargeflow_id: {
              $in: chargeflowIds.map(id => new ObjectId(id)),
            },
          },
          { chargeflow_success_rate: SUCCESS_RATE }
        );
        console.log(`Successfully updated ${targeCustomersUpdateResult} records from the json file`);

        console.log(`Updating ${inactiveShopsToUpdateCount} inactive shops`);
        const inactiveShopsToUpdateResult = await this.updateSucessRates(
          inactiveShopsToUpdateFilter,
          { chargeflow_success_rate: SUCCESS_RATE }
        );
        console.log(`Successfully updated ${inactiveShopsToUpdateResult} inactive shops`);

        console.log(`Total records updated: ${targeCustomersUpdateResult + inactiveShopsToUpdateResult}`);
      }
    }
}

const script = new DeleteChargesForPaidInvoices();
script.main();

# Purpose:
Updates the sucess rates of certain shops and inactive shops to 25%

# Motivation:
Chargeflow Success rate bump

# Running this script
`npx ts-node ./script.ts <dry or wet-secondary or wet-primary>`
NOTE:
- a `.env` file is expected to be present in CWD with **MONGO_URI** 
- Fill in the target chargeflowIds of shops in `chargeflowIds.json` file

### How this script was tested
[x] Wet run against dev & test

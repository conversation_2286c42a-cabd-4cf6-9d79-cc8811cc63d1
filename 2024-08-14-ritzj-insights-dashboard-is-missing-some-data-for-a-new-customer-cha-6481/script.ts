import { MongoClient, WithId, Document } from "mongodb";
import { loadMongoClient } from "../shared/mongo-config";
import { ScriptBase } from "../shared/script-base";
import { ObjectId } from 'mongodb';
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';

export class UpdateDisputeStatusToPrevented extends ScriptBase {
  public getName(): string {
    return "Resend disputes to UpdateAmountInUsdQueue";
  }

  public getAuthor(): string {
    return "Ritz Jumola";
  }

  public getTicket(): string {
    return "CHA-6481";
  }

  async dryRun(): Promise<void> {
    await this.logic();
  }

  async wetRunOnPrimary(): Promise<void> {
    await this.logic();
  }

  async wetRunOnSecondary(): Promise<void> {
    throw new Error("Method is not implemented.");
  }

  async logic() {
    if (!process.env.QUEUE_URL) {
        throw new Error("QUEUE_URL is not set");
    }

    console.log(`QUEUE_URL: ${process.env.QUEUE_URL}`);

    // find disputes
    const query = {
      chargeflowId: new ObjectId("668c54a501621ec54c20cd77"),
      "lastDisputeStatus.status": { $in: ["won", "warning_won"] },
      "dispute.amountInUsd": null,
    };

    console.log("Query: ", query);

    const client: MongoClient = await loadMongoClient();
    const disputes = client.db("chargeflow_api").collection("disputes");

    const result = await disputes.find(query).limit(1).toArray()

    console.log(`Found ${result.length} disputes`);

    if (this.mode === 'dry') {
        console.log("Dry run completed");
        return;
    }

    const SQS = new SQSClient();

    const queueUrl = process.env.QUEUE_URL;

    const promises = result.map(dispute => {
        const params = {
            MessageBody: JSON.stringify(dispute),
            QueueUrl: queueUrl
        };

        const command = new SendMessageCommand(params);
        return SQS.send(command);
    });

    const response = await Promise.allSettled(promises);

    const success = response.filter(r => r.status === 'fulfilled').length;
    const failed = response.filter(r => r.status === 'rejected').length;

    console.log(`Sent ${success} disputes to the queue`);
    console.log(`Failed to send ${failed} disputes to the queue`);
  }
}
const script = new UpdateDisputeStatusToPrevented();
script.main();

# Purpose
This script resends disputes to the UpdateDisputeAmountUsdFunction via SQS in relation to an error we encountered on July 9th which caused disputes to be not sent to the UpdateDisputeAmountUsdFunction.

https://linear.app/chargeflow/issue/CHA-6481/insights-dashboard-is-missing-some-data-for-a-new-customer

## Motivation
We need to resend disputes to the UpdateDisputeAmountUsdFunction to ensure that the disputes are updated with the correct amount in USD.

## Running this script
`npx ts-node ./script.ts <dry | wet-primary>`

## Modes
- dry - only logs the total number of disputes that are eligible for resending based on the mongo query set
- wet-primary - sends disputes to the UpdateDisputeAmountUsdFunction via SQS

## Required env variables
MONGO_URI=<mongo_uri_string>
QUEUE_URL=<sqs queue url>

### How this script was tested
[x] Run locally with development database

[x] Run locally with production database
# Purpose
This script resends order-linked disputes to shopify linking queue.

https://linear.app/chargeflow/issue/CHA-8387/create-a-new-utility-script-to-resend-order-linked-disputes-to-shopify

# Motivation
Validate the new linking process defined in shopify repository to ensure that all disputes were correctly
linked to corresponding orders.

## Running this script

`npx ts-node ./script.ts <dry or wet-primary> <dateCreatedFrom> <dateCreatedFrom>`

`dateCreatedFrom` and `dateCreatedTo` define the date range from which the disputes will be fetched. The date format
should be `YYYY-MM-DDTHH:mm:ssZ` (for instance: `2024-09-30T10:30:00Z`)

NOTE: a `.env` file is expected to be present in CWD

1. Set the following environment variables in CWD:
```
MONGO_URI=<db connection string>
QUEUE_URL=<shopify linking queue url>
```
Make sure these variables points to correct environments (dev, prod) according to the environment you want to run the
script on.

2. Install deps from CWD: `npm i`
3. Run the `dry` mode first to check which disputes should be processed.
4. Run the `wet-primary` to resend disputes to shopify linking queue and clear temporary `order-link-shadow` collection.

### How this script was tested

- [x] test migration against `dev` db

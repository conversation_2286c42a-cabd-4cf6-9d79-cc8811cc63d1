import dotenv from 'dotenv';
import { ScriptBase, ScriptContext } from "../shared/script-base";
import { loadMongoClient } from "../shared/mongo-config";
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import { MongoClient } from 'mongodb';
import { extractDate, getLinkedDisputesQuery} from '../shared/order-linking-validation/shared-order-linking-functions';

dotenv.config();

export class Script extends ScriptBase {

    public getName(): string {
        return "Resend order linked disputes to Shopify linking queue";
    }

    public getAuthor(): string {
        return "bartlomiejo";
    }

    public getTicket(): string {
        return "CHA-8387";
    }

    async dryRun(context: ScriptContext): Promise<void> {
        await this.execute(context);
    }

    async wetRunOnPrimary(context: ScriptContext): Promise<void> {
        await this.execute(context);
    }

    wetRunOnSecondary(context: ScriptContext): Promise<void> {
        throw new Error("Method not implemented.");
    }

    private async execute(context: ScriptContext): Promise<void> {
        await this.validateEnvVariables();
        const dateCreatedFrom = await extractDate(context.args[1]);
        const dateCreatedTo = await extractDate(context.args[2]);
        const processorName = context.args[3];
        const mongoClient = await loadMongoClient();
        const disputes = await this.findLinkedDisputes(mongoClient, dateCreatedFrom, dateCreatedTo, processorName);
        if (this.mode === 'dry') {
            console.log("Dry run completed");
            return;
        }
        await this.clearOrderLinkShadowCollection(mongoClient);
        await this.sendDisputesToShopifyQueue(disputes);
    }
    
    private async validateEnvVariables() {
        if (!process.env.MONGO_URI) {
            throw new Error("MONGO_URI is not set");
        }
        if (this.mode === "wet-primary" && !process.env.QUEUE_URL) {
            throw new Error("QUEUE_URL is not set");
        }
    }

    private async findLinkedDisputes(client: MongoClient, dateCreatedFrom: Date, dateCreatedTo: Date, processorName: string) {
        if (!dateCreatedFrom) {
            throw new Error('Missing dateCreatedFrom');
        }
        if (!dateCreatedTo) {
            throw new Error('Missing dateCreatedTo');
        }
        console.log(`Searching for disputes linked to orders created from ${dateCreatedFrom} to ${dateCreatedTo}`);
        const query = await getLinkedDisputesQuery(dateCreatedFrom, dateCreatedTo, processorName);
        const disputes = await client.db("chargeflow_api")
            .collection('disputes')
            .find(query)
            .toArray();
        console.log(`Found ${disputes.length} disputes: `);
        return disputes;
    }

    private async mapToEventDetail(dispute: any) {
        return {
            disputeId: dispute._id,
            chargeflowId: dispute.chargeflowId,
            processorName: dispute.dispute?.processor,
            disputePropertiesForLinking: {
                transactionOrderId: dispute.transaction?.orderId,
                transactionId: dispute.transaction?.id,
                transactionDate: dispute.transaction?.dateCreated,
                transaction: dispute.transaction,
                customerEmail: dispute.transaction?.customerEmail,
            }
        };
    }

    private async sendDisputesToShopifyQueue(disputes: any) {
        const SQS = new SQSClient();
        const promises = disputes.map(async (dispute: any) => {
            const detail = await this.mapToEventDetail(dispute);
            const params = {
                MessageBody: JSON.stringify({detail}),
                QueueUrl: process.env.QUEUE_URL
            };
            const command = new SendMessageCommand(params);
            return SQS.send(command);
        });
        const response = await Promise.allSettled(promises);

        const success = response.filter(r => r.status === 'fulfilled').length;
        const failed = response.filter(r => r.status === 'rejected').length;

        console.log(`Sent ${success} disputes to the queue`);
        console.log(`Failed to send ${failed} disputes to the queue`);
    }

    private async clearOrderLinkShadowCollection(client: MongoClient) {
        console.log(`Clearing order-link-shadow collection`);
        await client.db("chargeflow_api")
            .collection('order-link-shadow')
            .deleteMany({});
        console.log(`Order-link-shadow collection cleared`);
    }
}

const script = new Script();
script.main();

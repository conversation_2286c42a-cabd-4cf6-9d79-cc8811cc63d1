import dotenv from 'dotenv';
import { loadMongoClient } from '../shared/mongo-config';
import { ScriptBase, ScriptContext } from '../shared/script-base';
import { Db, ObjectId } from 'mongodb';
import { first, sortBy } from 'lodash';

dotenv.config();

interface Dispute {
  _id: string;
  dispute: {
    id: string;
    amountInUsd: number;
  };
  chargeflow: {
    cfActions: {
      chargeflowPerformedAction: boolean;
    };
    evidences: any[]; // Replace with specific evidence type if known
  };
}

interface Action {
  _id: string;
  dateCreated: Date;
  chargeflowId: string;
  accountId: string | null;
  disputeId: string;
  caseId: string;
  shopName: string;
  processor: string;
  actor: string;
  actorRole: string;
  actionType: "evidence_submitted" | "manual_action" | "message_sent";
}

interface Case {
  _id: string;
  caseId: string;
  amountInUSD: number;
  evidenceSubmitted: number;
  messageSent: number;
  manualAction: number;
  status: string;
  disputes: Dispute[];
  actions: Action[];
  disputeIdsWithActions: string[];
  disputesWithActionsCount: number;
}

const TARGET_ACTION_TYPE = ['evidence_submitted', 'manual_action', 'message_sent']

const RECORDS_PER_PAGE = 500;

export class DeleteChargesForPaidInvoices extends ScriptBase {
  db: Db | undefined = undefined;

  public getName(): string {
    return "Fix past disputes managed by CF that were not computed";
  }

  public getAuthor(): string {
    return "Arnold <PERSON>";
  }

  public getTicket(): string {
    return "CHA-14313";
  }

  async dryRun(context: ScriptContext): Promise<void> {
    await this.logic(context, true);
  }


  async wetRunOnPrimary(context: ScriptContext): Promise<void> {
    await this.logic(context, false);
  }

  wetRunOnSecondary(context: ScriptContext): Promise<void> {
    throw new Error("Method not implemented.");
  }


  private async logic(context: ScriptContext, isDryRun: boolean): Promise<void> {
    const mongoClient = await loadMongoClient();
    const disputesTestMongoView = mongoClient.db("chargeflow_api").collection("cha-14313-investigation");
    const disputesMongoCollection = mongoClient.db("chargeflow_api").collection("disputes");

    let page = 0;
    let hasMore = true;

    const VIEW_FILTER = {
      // temporary filter for validating accuracy of the script
      // 'Case ID': 'PP-R-PPL-553581699',
      // this is for the first batch of running the script
      // the second batch will handle disputes with dupes in disputes collections
      disputesWithActionsCount: {$eq: 1 },
      // we only want to run this against disputes where we failed to set performedAction on
      PERFORMED_ACTIONS: { $not: { $elemMatch: { $eq: true } } },
    }

    const total = await disputesTestMongoView.countDocuments(VIEW_FILTER);

    console.log(
      `Total records to process:`,
      total,
    );

    console.log('Fetching disputes data in a paginated manner');

    const determineDisputeToUpdate = (disputes: any[]) => {
      // change this when handling multi dispute instances
      return first(disputes);
    };

    while (hasMore) {
      const results = await disputesTestMongoView
      .find(VIEW_FILTER)
      .skip(page * RECORDS_PER_PAGE)
      .limit(RECORDS_PER_PAGE)
      .toArray();

      console.log('Mapping disputes to update');

      const bulkOps: any[] = [];

      for (const disputeData of results) {

        const targetDispute = determineDisputeToUpdate(disputeData.disputes);

        console.log('targetDispute', targetDispute);

        const knownActions = disputeData.actions.filter(
          (action: any) =>
            TARGET_ACTION_TYPE.includes(action.actionType) &&
            action.disputeId.toString() === targetDispute._id.toString()
        );

        console.log('knownActions', knownActions.length);

        if (!knownActions.length) {
          console.log('No known actions for this dispute, skipping');
          continue;
        }

        const earliestAction = first(sortBy(knownActions, (action: any) => action.dateCreated));

        const earliestActionDateCreated = earliestAction.dateCreated;

        const setQuery = {
          // to easily track the ones that were modified by this script
          "isCha14313Batch1Modified": true,
          "chargeflow.cfActions.chargeflowPerformedAction": true,
        };

        if (targetDispute.chargeflow.submitted.submittedAt !== earliestActionDateCreated) {
          setQuery[
            "chargeflow.submitted.submittedAt"
          ] = earliestActionDateCreated;
        }

        if (targetDispute.chargeflow.submitted.amountInUSD !== targetDispute.dispute.amountInUsd) {
          setQuery[
            "chargeflow.submitted.amountInUSD"
          ] = targetDispute.dispute.amountInUsd;
        }

        const bulkOp = {
          updateOne: {
            filter: { _id: new ObjectId(targetDispute._id) },
            update: {
              $set: setQuery,
            },
          },
        };

        bulkOps.push(bulkOp);
      }

      console.log('Mapped bulk updates total', bulkOps.length);
      console.log('Mapped bulk updates sample', JSON.stringify(bulkOps[0], null, 2));

      if (!bulkOps.length) {
        console.log('No bulk updates to perform, skipping');
        page++;
        hasMore = total > page * RECORDS_PER_PAGE;
        continue;
      }

      if (!isDryRun) {
        console.log('Updating disputes in bulk');
        const bulkWriteResult = await disputesMongoCollection.bulkWrite(bulkOps, { ordered: false });
        console.log('Bulk write result:', bulkWriteResult);
      }

      console.log(`Fetched page ${page + 1} with ${results.length} records`);
      page++;
      hasMore = total > page * RECORDS_PER_PAGE;
      console.log(`Has more: ${hasMore}`);
    }

    await mongoClient.close();
  }
}



const script = new DeleteChargesForPaidInvoices();
script.main();